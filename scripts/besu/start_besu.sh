#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/common_function.sh

#
# 変数
#
TYPE=""
CONTAINS=""
NOT_CONTAINS=""
NUMBER=""

INSTANCE_LIST=()

SLEEP_TIME=10
MAX_SLEEP_NUM=30

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to start BESU process on AWS instances in order of new launch time.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    -t <value>
    [-c <value>]
    [-n <value>]
    [-m <value>]

  OPTIONS
    -t (string) (REQUIRED)
        Specify BESU type. ("validator" or "listener")
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.
    -m (string)
        Specify instance number.

  EXAMPLES
    Example 1: To start BESU of instances which contain "validator" and "shared".
      $0 -t validator -c validator,shared
    Example 2: To start BESU of instance which contains "validator" and "shared", and target is only 1.
      $0 -t validator -c validator,shared -m 1
----------------------------------------------------------------
EOF
}

#
# 引数チェック
#
function CHECK_ARGUMENT () {
  if ! [[ "${TYPE}" == "validator" ]] && ! [[ "${TYPE}" == "listener" ]]; then
    return 1
  fi

  return 0
}

#
# 対象の確認
#
function CHECK_TARGET () {
  COMMAND_DESCRIBE_INSTANCES="aws ec2 describe-instances --query 'reverse(sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId, Name:Tags[?Key==\`Name\`] | [0].Value},&LaunchTime))'"
  COMMAND_DESCRIBE_INSTANCES+=" --filters \"Name=instance-state-name,Values=running\""

  COMMAND_SELECT_CONTAIN=""
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({Name: \"${CONTAIN}\"})) | "
    done
  fi

  COMMAND_SELECT_NOT_CONTAIN=""
  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({Name: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi

  COMMAND_HEAD=""
  if [[ "${NUMBER}" != "" ]]; then
    COMMAND_HEAD="| head -${NUMBER}"
  fi

  echo "## Target instances"
  echo "\$ ${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}"
  echo ""
  echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash
  echo ""

  # インスタンスリストを控えておく
  INSTANCE_LIST=($(echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash))
}

#
# BESUの開始
#
function START_BESU () {
  # BESUの開始
  echo "## Starting BESU process...."
  INSTANCE_IDS=""
  for INSTANCE in "${INSTANCE_LIST[@]}"
  do
    INSTANCE_IDS+=",\"${INSTANCE}\""
  done
  INSTANCE_IDS="[${INSTANCE_IDS#,}]"
  TARGETS="[{ \"Key\":\"InstanceIds\",\"Values\":${INSTANCE_IDS} }]"
  COMMAND_START="systemctl start ${TYPE}"

  RES=$(aws ssm send-command --document-name "AWS-RunShellScript" \
    --targets "${TARGETS}" \
    --parameters "{\"workingDirectory\":[\"\"],\"executionTimeout\":[\"3600\"],\"commands\":[\"${COMMAND_START}\"]}" \
    --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
  COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')
  echo "Requested starting."
  echo ""

  # 開始実施時の出力確認
  echo "Checking response...."
  RES_INSTANCE_ID_LIST=($(
    aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
      --output text \
      --query 'CommandInvocations[].{InstanceId:InstanceId}'))
  RES_JSON_OUTPUT=$(
    aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
      --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

  FLAG_RES_START=0
  for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1)))
  do
    OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
    echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} response: "
    if [[ "${OUTPUT}" == "\"\"" ]]; then
      echo "OK"
    else
      echo "NG"
      FLAG_RES_START=1
    fi
  done

  if [[ "${FLAG_RES_START}" == "0" ]]; then
    echo "No issue."
    echo ""
  else
    echo "ERROR: Response for starting BESU process is failed. Please check the status. Stop the script."
    exit 1
  fi

  # BESUプロセスのステータスを確認
  echo "## Checking BESU process status...."
  COMMAND_STATUS="systemctl status ${TYPE}"

  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    RES=$(aws ssm send-command --document-name "AWS-RunShellScript" \
      --targets "${TARGETS}" \
      --parameters "{\"workingDirectory\":[\"\"],\"executionTimeout\":[\"3600\"],\"commands\":[\"${COMMAND_STATUS}\"]}" \
      --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
    COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')

    RES_INSTANCE_ID_LIST=($(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --output text \
        --query 'CommandInvocations[].{InstanceId:InstanceId}'))
    RES_JSON_OUTPUT=$(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

    FLAG_RES_STATUS=0
    for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1)))
    do
      OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
      echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} BESU process status: "
      if [[ "${OUTPUT}" =~ "Active: active (running)" ]]; then
        echo "started"
      else
        echo "not started"
        FLAG_RES_STATUS=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_RES_STATUS}" == "0" ]]; then
      echo "Finished starting."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Response for checking BESU process status is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# Main
#

# 引数を変数に代入
while getopts ht:c:n:m: OPT
do
  case $OPT in
    t) TYPE=$OPTARG;;
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    m) NUMBER=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# 引数チェック
if ! $(CHECK_ARGUMENT); then
  USAGE
  exit 1
fi

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target instances"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
START_BESU

echo ""
echo "Completed $0."
echo ""
