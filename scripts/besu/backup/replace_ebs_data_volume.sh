#!/bin/bash -eu

NEW_VOLUME_ID="$1"

#
# 引数チェック
#
function CHECK_ARGUMENT () {
  if [[ "${NEW_VOLUME_ID}" == "" ]]; then
    echo "Please run this script with {volume ID} as first argument"
    exit 1
  fi

  echo "Check ok."
  echo ""
}

#
# 確認用メッセージ
#
function CONFIRMATION () {
  read -p "Is it ok to proceed? (y/N): " ANSWER
  case "${ANSWER}" in
    [yY]*)
      echo "";;
    *)
      echo "Stop the script."
      exit 0;;
  esac
}

echo "This script is for replacing EBS data volume for backup instance."

echo "## Check script argument."
CHECK_ARGUMENT

echo "## Check your current role."
echo "$ aws sts get-caller-identity"
aws sts get-caller-identity
CONFIRMATION

echo "## Check new instance ID"
echo "$ aws ec2 describe-instances --filters Name=instance-state-name,Values=running Name=tag:Name,Values='*-backup-listener' --query 'reverse(sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId},&LaunchTime))' | jq -r \".[].InstanceId\" | head -1"
INSTANCE_ID=$(aws ec2 describe-instances --filters Name=instance-state-name,Values=running Name=tag:Name,Values='*-backup-listener' --query 'reverse(sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId},&LaunchTime))' | jq -r ".[].InstanceId" | head -1)
echo "instance ID: ${INSTANCE_ID}"
echo ""

echo "## Check volume ID of instance"
echo "$ aws ec2 describe-instances --instance-ids ${INSTANCE_ID} | jq -r '.Reservations[].Instances[].BlockDeviceMappings[] | select(.DeviceName == \"/dev/sdf\") | .Ebs.VolumeId'"
VOLUME_ID=$(aws ec2 describe-instances --instance-ids ${INSTANCE_ID} | jq -r '.Reservations[].Instances[].BlockDeviceMappings[] | select(.DeviceName == "/dev/sdf") | .Ebs.VolumeId')
echo "volume ID: ${VOLUME_ID}"
echo ""
CONFIRMATION

echo "## Detach current volume"
echo "$ aws ec2 detach-volume --volume-id ${VOLUME_ID}"
aws ec2 detach-volume --volume-id ${VOLUME_ID}
echo ""

echo "## Waiting until finishing detach"
SLEEP_TIME=5
MAX_SLEEP_NUM=3
for NUM in $(seq 1 ${MAX_SLEEP_NUM})
do
  VOLUME_STATUS=$(aws ec2 describe-volumes --volume-ids ${VOLUME_ID}| jq -r '.Volumes[].State')
  if [[ "${VOLUME_STATUS}" == "available" ]]; then
    echo "Finished detach."
    echo ""
  else
    if [[ "${NUM}" == "${MAX_SLEEP_NUM}" ]]; then
      echo "Detach status check is failed. Please check status. Stop the script."
      exit 1
    fi
    echo "Waiting for ${SLEEP_TIME} seconds. (${NUM}/${MAX_SLEEP_NUM})"
    sleep 5
  fi
done

echo "## Attach volume which is created from snapshot"
echo "$ aws ec2 attach-volume --instance-id ${INSTANCE_ID} --volume-id ${NEW_VOLUME_ID} --device /dev/sdf"
aws ec2 attach-volume --instance-id ${INSTANCE_ID} --volume-id ${NEW_VOLUME_ID} --device /dev/sdf

echo ""
echo "Complete the operation."
