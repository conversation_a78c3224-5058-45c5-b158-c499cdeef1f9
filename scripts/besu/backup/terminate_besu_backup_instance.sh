#!/bin/bash -eu

#
# 確認用メッセージ
#
function CONFIRMATION () {
  read -p "Is it ok to proceed? (y/N): " ANSWER
  case "${ANSWER}" in
    [yY]*)
      echo "";;
    *)
      echo "Stop the script."
      exit 0;;
  esac
}

echo "This script is for recreating BESU backup instance."

echo "## Check your current role."
echo "$ aws sts get-caller-identity"
aws sts get-caller-identity
CONFIRMATION

echo "## Check instance ID"
echo "$ aws ec2 describe-instances --filters Name=instance-state-name,Values=stopped Name=tag:Name,Values='*-backup-listener' | jq -r \".Reservations[].Instances[].InstanceId\""
INSTANCE_ID=$(aws ec2 describe-instances --filters Name=instance-state-name,Values=stopped Name=tag:Name,Values='*-backup-listener' | jq -r ".Reservations[].Instances[].InstanceId")
echo "instance ID: ${INSTANCE_ID}"
echo ""

echo "## Check volume ID of instance"
echo "$ aws ec2 describe-instances --instance-ids ${INSTANCE_ID} | jq -r '.Reservations[].Instances[].BlockDeviceMappings[] | select(.DeviceName == \"/dev/sdf\") | .Ebs.VolumeId'"
VOLUME_ID=$(aws ec2 describe-instances --instance-ids ${INSTANCE_ID} | jq -r '.Reservations[].Instances[].BlockDeviceMappings[] | select(.DeviceName == "/dev/sdf") | .Ebs.VolumeId')
echo "volume ID: ${VOLUME_ID}"
echo ""

echo "## Terminate instance: ${INSTANCE_ID}"
CONFIRMATION
echo "$ aws ec2 terminate-instances --instance-ids ${INSTANCE_ID}"
aws ec2 terminate-instances --instance-ids ${INSTANCE_ID}

echo ""
echo "## Termination started. Please check instance state by following command."
echo "$ aws ec2 describe-instances --instance-ids ${INSTANCE_ID} | jq -r \".Reservations[].Instances[].State\""

echo ""
echo "Complete the operation."
echo "You will use following information as next step. Please note."
echo "volume ID : ${VOLUME_ID}"
