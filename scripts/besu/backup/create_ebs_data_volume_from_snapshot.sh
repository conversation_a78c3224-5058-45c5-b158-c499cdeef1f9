#!/bin/bash -eu

VOLUME_ID="$1"

#
# 引数チェック
#
function CHECK_ARGUMENT () {
  if [[ "${VOLUME_ID}" == "" ]]; then
    echo "Please run this script with {volume ID} as first argument"
    exit 1
  fi

  echo "Check ok."
  echo ""
}

#
# 確認用メッセージ
#
function CONFIRMATION () {
  read -p "Is it ok to proceed? (y/N): " ANSWER
  case "${ANSWER}" in
    [yY]*)
      echo "";;
    *)
      echo "Stop the script."
      exit 0;;
  esac
}

echo "This script is for creating EBS data volume from snapshot."

echo "## Check script argument."
CHECK_ARGUMENT

echo "## Check your current role."
echo "$ aws sts get-caller-identity"
aws sts get-caller-identity
CONFIRMATION

echo "## Create EBS data volume from snapshot"
SNAPSHOT_ID=$(aws ec2 describe-snapshots --owner-ids self --filters Name=volume-id,Values="${VOLUME_ID}" --query 'reverse(sort_by(Snapshots[].{StartTime: StartTime, SnapshotId:SnapshotId},&StartTime))' | jq -r ".[].SnapshotId" | head -1)
echo "snapshot ID: ${SNAPSHOT_ID}"
echo ""

# タグ情報の取得
TAG_ENVIRONMENT=$(aws ec2 describe-snapshots --snapshot-ids ${SNAPSHOT_ID} | jq -r '.Snapshots[].Tags[] | select(.Key == "Environment") | .Value')
TAG_DLT=$(aws ec2 describe-snapshots --snapshot-ids ${SNAPSHOT_ID} | jq -r '.Snapshots[].Tags[] | select(.Key == "DLT") | .Value')
TAG_NAME=$(aws ec2 describe-snapshots --snapshot-ids ${SNAPSHOT_ID} | jq -r '.Snapshots[].Tags[] | select(.Key == "Name") | .Value')
AZ="$(aws ec2 describe-snapshots --snapshot-ids ${SNAPSHOT_ID} | jq -r '.Snapshots[].Tags[] | select(.Key == "Region") | .Value')"a # リージョンにaを付ける
echo "environment: ${TAG_ENVIRONMENT}"
echo "dlt: ${TAG_DLT}"
echo "name: ${TAG_NAME}"
echo "az: ${AZ}"
CONFIRMATION

# ボリューム作成 (タグをつける)
aws ec2 create-volume --availability-zone ${AZ} --snapshot-id ${SNAPSHOT_ID} --volume-type gp3 --tag-specifications "ResourceType=volume,Tags=[{Key=Environment,Value=${TAG_ENVIRONMENT}},{Key=DLT,Value=${TAG_DLT}},{Key=Name,Value=${TAG_NAME}},{Key=BesuBackup,Value=true}]"

echo ""
echo "You will use \"VolumeId\" information as next step. Please note."
