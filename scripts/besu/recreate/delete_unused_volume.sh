#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/../common_function.sh

#
# 変数
#
SLEEP_TIME=5
MAX_SLEEP_NUM=5

COMMAND_DESCRIBE_VOLUMES="aws ec2 describe-volumes --filter Name=status,Values=available | jq -r  '.Volumes[] | .VolumeId'"
VOLUME_LIST=()

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to delete unused volume in AWS.

    See "$0 -h" for this description.

  SYNOPSIS
    $0

  OPTIONS
    None
----------------------------------------------------------------
EOF
}

#
# 対象の確認
#
function CHECK_TARGET () {
  echo "## Target volumes"
  echo "\$ ${COMMAND_DESCRIBE_VOLUMES}"
  echo ""
  echo "${COMMAND_DESCRIBE_VOLUMES}" | bash

  # ボリュームリストを控えておく
  VOLUME_LIST=($(echo "${COMMAND_DESCRIBE_VOLUMES}" | bash))
  echo "Volume number: ${#VOLUME_LIST[*]}"
}

#
# 未使用ボリュームの削除
#
function DELETE_UNUSED_VOLUME () {
  # 未使用ボリュームの削除
  echo "## Deleting unused volumes...."
  for VOLUME in "${VOLUME_LIST[@]}"
  do
    aws ec2 delete-volume --volume-id ${VOLUME}
  done
  echo "Requested to delete unused volumes."
  echo ""

  # 削除されたことを確認
  echo "## Checking whether it is deleted or not...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    FLAG_OK=0

    RES="$(echo "${COMMAND_DESCRIBE_VOLUMES}" | bash)"
    # avaiable状態のボリュームがないこと
    if [[ "${RES}" == "" ]]; then
      echo "There is no available volume."
    else
      echo "There are available volumes."
      FLAG_OK=1
    fi

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_OK}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Checking available volume is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# Main
#

# 引数チェック
while getopts h OPT
do
  case $OPT in
    ?) USAGE
      exit 0;;
  esac
done

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target volumes"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
DELETE_UNUSED_VOLUME

echo ""
echo "Completed $0."
