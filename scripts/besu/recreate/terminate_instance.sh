#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/../common_function.sh

#
# 変数
#
STATE=""
CONTAINS=""
NOT_CONTAINS=""
NUMBER=""

INSTANCE_LIST=()
INSTANCE_STATUS_LIST=()
INSTANCE_AZ_LIST=()
INSTANCE_VOLUME_LIST=()

SLEEP_TIME=30
MAX_SLEEP_NUM=5

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to terminate AWS instances in order of old launch time.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    [-s <value>]
    [-c <value>]
    [-n <value>]
    [-m <value>]

  OPTIONS
    -s (string)
        Specify instance state.
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.
    -m (string)
        Specify instance number.

  EXAMPLES
    Example 1: To terminate AWS instances which state is "running", and contain "validator" and "shared".
      $0 -s running -c validator,shared
    Example 2: To terminate AWS instance which state is "running", and contains "validator" and "shared", and target is only 1.
      $0 -s running -c validator,shared -m 1
----------------------------------------------------------------
EOF
}

#
# 対象の確認
#
function CHECK_TARGET () {
  COMMAND_DESCRIBE_INSTANCES="aws ec2 describe-instances --query 'sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId, Name:Tags[?Key==\`Name\`] | [0].Value},&LaunchTime)'"

  if [[ "${STATE}" != "" ]]; then
    COMMAND_DESCRIBE_INSTANCES+=" --filters \"Name=instance-state-name,Values=${STATE}\""
  fi

  COMMAND_SELECT_CONTAIN=""
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({Name: \"${CONTAIN}\"})) | "
    done
  fi

  COMMAND_SELECT_NOT_CONTAIN=""
  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({Name: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi

  COMMAND_HEAD=""
  if [[ "${NUMBER}" != "" ]]; then
    COMMAND_HEAD="| head -${NUMBER}"
  fi

  echo "## Target instances"
  echo "\$ ${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}"
  echo ""
  echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash
  echo ""

  # インスタンスリストを控えておく
  INSTANCE_LIST=($(echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash))

  # インスタンスのAZとデータボリュームを控えておく
  for INSTANCE in "${INSTANCE_LIST[@]}"
  do
    echo "# instance: ${INSTANCE}"

    AZ=$(aws ec2 describe-instances --instance-ids ${INSTANCE} | jq -r '.Reservations[].Instances[].Placement.AvailabilityZone')
    echo "az: ${AZ}"
    INSTANCE_AZ_LIST+=(${AZ})

    VOLUME=$(aws ec2 describe-volumes --filter Name=attachment.instance-id,Values=${INSTANCE} Name=attachment.device,Values=/dev/sdf | jq -r ".Volumes[] | .VolumeId")
    echo "volume: ${VOLUME}"
    INSTANCE_VOLUME_LIST+=(${VOLUME})
  done
}

#
# インスタンスの終了
#
function TERMINATE_INSTANCE () {
  # インスタンスの終了
  echo "## Terminating instances...."
  for INSTANCE in "${INSTANCE_LIST[@]}"
  do
    aws ec2 terminate-instances --instance-ids ${INSTANCE}
  done
  echo "Requested termination."
  echo ""

  echo "## Waiting until finishing termination...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    # インスタンスのステータスを取得
    INSTANCE_STATUS_LIST=()
    for INSTANCE in "${INSTANCE_LIST[@]}"
    do
      INSTANCE_STATUS=$(
        aws ec2 describe-instances \
          --output text \
          --instance-ids ${INSTANCE} --query "Reservations[].Instances[].{State:State.Name}")
      INSTANCE_STATUS_LIST+=(${INSTANCE_STATUS})
    done

    # インスタンスのステータスを確認
    FLAG_FINISHED_TERMINATION=0
    for NUM in $(seq 0 $((${#INSTANCE_LIST[*]} - 1)))
    do
      echo "${INSTANCE_LIST[${NUM}]}: ${INSTANCE_STATUS_LIST[${NUM}]}"
      if [[ "${INSTANCE_STATUS_LIST[${NUM}]}" != "terminated" ]]; then
        FLAG_FINISHED_TERMINATION=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_FINISHED_TERMINATION}" == "0" ]]; then
      echo "Finished termination."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Termination check is failed. Please check status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# 対象の表示
#
function DISPLAY_TARGET () {
  echo "## Target instances (volume, availability zone)"
  for NUM in $(seq 0 $((${#INSTANCE_LIST[*]} - 1)))
  do
    echo "${INSTANCE_LIST[${NUM}]} (${INSTANCE_VOLUME_LIST[${NUM}]}, ${INSTANCE_AZ_LIST[${NUM}]})"
  done
  echo ""
  echo "## Target volumes"
  echo "${INSTANCE_VOLUME_LIST[@]}" | tr ' ' ','
  echo ""
}

#
# Main
#

# 引数を変数に代入
while getopts hs:c:n:m: OPT
do
  case $OPT in
    s) STATE=$OPTARG;;
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    m) NUMBER=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target instances"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
TERMINATE_INSTANCE

# 終了したインスタンスのAZとデータボリュームの情報を表示
DISPLAY_TARGET

echo ""
echo "Completed $0."
echo ""
