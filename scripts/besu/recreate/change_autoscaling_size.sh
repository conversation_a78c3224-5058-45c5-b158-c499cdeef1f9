#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/../common_function.sh

#
# 変数
#
TYPE=""
CONTAINS=""
NOT_CONTAINS=""

TARGET_ASG_TAG_NAME=""
TARGET_ASG_NAME=""
CURRENT_ASG_SIZE=""
CHANGED_ASG_SIZE=""
CURRENT_INSTANCE_LIST=()
CHANGED_INSTANCE_LIST=()

COMMAND_DESCRIBE_INSTANCES="aws ec2 describe-instances --query 'sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId, Name:Tags[?Key==\`Name\`] | [0].Value},&LaunchTime)'"
COMMAND_DESCRIBE_INSTANCES+=" --filters \"Name=instance-state-name,Values=running\""
COMMAND_SELECT_CONTAIN=""
COMMAND_SELECT_NOT_CONTAIN=""

SLEEP_TIME=60
MAX_SLEEP_NUM=10

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to change Auto Scaling Group size.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    -t <value>
    [-c <value>]
    [-n <value>]

  OPTIONS
    -t (string) (REQUIRED)
        Specify change type. ("double" or "half")
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.

  EXAMPLES
    Example 1: To change Auto Scaling Group size to double for main listener.
      $0 -t double -c listener -n shared
----------------------------------------------------------------
EOF
}

#
# 引数チェック
#
function CHECK_ARGUMENT () {
  if ! [[ "${TYPE}" == "double" ]] && ! [[ "${TYPE}" == "half" ]]; then
    return 1
  fi

  return 0
}

#
# 対象の確認
#
function CHECK_TARGET () {
  COMMAND_DESCRIBE_ASG="aws autoscaling describe-auto-scaling-groups --query='AutoScalingGroups[].{Name:Tags[?Key==\`Name\`] | [0].Value}'"
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({Name: \"${CONTAIN}\"})) | "
    done
  fi

  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({Name: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi
  echo "## Target Auto Scaling Group"
  echo "\$ ${COMMAND_DESCRIBE_ASG} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .Name'"
  echo ""
  echo "${COMMAND_DESCRIBE_ASG} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .Name'" | bash
  echo ""

  # 対象のAuto Scaling Groupとサイズを控えておく
  TARGET_ASG_TAG_NAME=$(echo "${COMMAND_DESCRIBE_ASG} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .Name'" | bash)
  TARGET_ASG_NAME=$(
    aws autoscaling describe-auto-scaling-groups \
      --output text \
      --filters "Name=tag:Name,Values=${TARGET_ASG_TAG_NAME}" \
      --query="AutoScalingGroups[].AutoScalingGroupName")

  CURRENT_ASG_SIZE=$(
    aws autoscaling describe-auto-scaling-groups \
      --output text \
      --auto-scaling-group-names ${TARGET_ASG_NAME} \
      --query="AutoScalingGroups[].DesiredCapacity")

  if [[ "${TYPE}" == "double" ]]; then
    CHANGED_ASG_SIZE=$((${CURRENT_ASG_SIZE} * 2))
  else
    CHANGED_ASG_SIZE=$((${CURRENT_ASG_SIZE} / 2))
  fi
  echo "## Change Auto Scaling Group size."
  echo "From: ${CURRENT_ASG_SIZE}"
  echo "To: ${CHANGED_ASG_SIZE}"
  echo ""

  # インスタンスリストを控えておく
  CURRENT_INSTANCE_LIST=($(echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId'" | bash))
  echo "## Current Instances"
  echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId'" | bash
}

#
# Auto Scaling Groupサイズの変更
#
function CHANGE_ASG_SIZE () {
  # Auto Scaling Groupサイズの変更
  echo "## Starting to change Auto Scaling Group size...."

  aws autoscaling update-auto-scaling-group \
    --auto-scaling-group-name ${TARGET_ASG_NAME} \
    --max-size ${CHANGED_ASG_SIZE} \
    --desired-capacity ${CHANGED_ASG_SIZE}
  echo "Requested to change Auto Scaling Group size."
  echo ""

  if [[ "${TYPE}" == "double" ]]; then
    # 新規インスタンスが立ち上がるか確認する
    echo "## Waiting until starting new instances...."
    for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
    do
      CHANGED_INSTANCE_LIST=($(echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId'" | bash))

      # インスタンス数を確認
      echo "Instance number: ${#CHANGED_INSTANCE_LIST[*]}"
      if [[ "${#CHANGED_INSTANCE_LIST[*]}" != "${CHANGED_ASG_SIZE}" ]]; then
        if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
          echo "ERROR: Instance check is failed. Please check the status. Stop the script."
          echo "RESULT: NG"
          exit 1
        fi
        echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
        sleep ${SLEEP_TIME}
        continue
      fi

      # インスタンスのステータスを取得
      INSTANCE_STATUS_LIST=()
      for INSTANCE in "${CHANGED_INSTANCE_LIST[@]}"
      do
        INSTANCE_STATUS=$(
          aws ec2 describe-instances \
            --output text \
            --instance-ids ${INSTANCE} --query "Reservations[].Instances[].{State:State.Name}")
        INSTANCE_STATUS_LIST+=(${INSTANCE_STATUS})
      done

      # インスタンスのステータスを確認
      FLAG_FINISHED_STARTING=0
      for NUM in $(seq 0 $((${#CHANGED_INSTANCE_LIST[*]} - 1)))
      do
        echo "${CHANGED_INSTANCE_LIST[${NUM}]}: ${INSTANCE_STATUS_LIST[${NUM}]}"
        if [[ "${INSTANCE_STATUS_LIST[${NUM}]}" != "running" ]]; then
          FLAG_FINISHED_STARTING=1
        fi
      done

      if [[ "${FLAG_FINISHED_STARTING}" == "0" ]]; then
        echo "Finished starting."
        echo ""
      else
        if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
          echo "ERROR: Instance check is failed. Please check status. Stop the script."
          echo "RESULT: NG"
          exit 1
        fi
        echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
        sleep ${SLEEP_TIME}
        continue
      fi

      # インスタンスのヘルスチェック結果を取得
      HEALTH_INSTANCE_STATUS_LIST=()
      HEALTH_SYSTEM_STATUS_LIST=()
      for INSTANCE in "${CHANGED_INSTANCE_LIST[@]}"
      do
        HEALTH_INSTANCE_STATUS=$(
          aws ec2 describe-instance-status \
            --output text \
            --instance-ids ${INSTANCE} --query "InstanceStatuses[].InstanceStatus.Details[].Status")
        HEALTH_SYSTEM_STATUS=$(
          aws ec2 describe-instance-status \
            --output text \
            --instance-ids ${INSTANCE} --query "InstanceStatuses[].SystemStatus.Details[].Status")
        HEALTH_INSTANCE_STATUS_LIST+=(${HEALTH_INSTANCE_STATUS})
        HEALTH_SYSTEM_STATUS_LIST+=(${HEALTH_SYSTEM_STATUS})
      done

      # インスタンスのヘルスチェック結果を確認
      FLAG_HEALTH_CHECK=0
      for NUM in $(seq 0 $((${#CHANGED_INSTANCE_LIST[*]} - 1)))
      do
        echo "${CHANGED_INSTANCE_LIST[${NUM}]}: ${HEALTH_INSTANCE_STATUS_LIST[${NUM}]}: ${HEALTH_SYSTEM_STATUS_LIST[${NUM}]}"
        if [[ "${HEALTH_INSTANCE_STATUS_LIST[${NUM}]}" != "passed" ]] || [[ "${HEALTH_SYSTEM_STATUS_LIST[${NUM}]}" != "passed" ]]; then
          FLAG_HEALTH_CHECK=1
        fi
      done

      if [[ "${FLAG_HEALTH_CHECK}" == "0" ]]; then
        echo "Finished health check."
        echo "RESULT: OK"
        echo ""
        break
      else
        if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
          echo "ERROR: Health check is failed. Please check status. Stop the script."
          echo "RESULT: NG"
          exit 1
        fi
        echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
        sleep ${SLEEP_TIME}
        continue
      fi
    done

  else
    # 古いインスタンスが終了することを確認する
    echo "## Waiting until stopping instances...."

    # 古いインスタンスを取得する
    for NUM in $(seq 0 $((${#CURRENT_INSTANCE_LIST[*]} - 1)))
    do
      CHANGED_INSTANCE_LIST+=(${CURRENT_INSTANCE_LIST[${NUM}]})
      if [[ "${NUM}" == "$((${CHANGED_ASG_SIZE} - 1))" ]]; then
        break
      fi
    done

    for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
    do
      # インスタンスのステータスを取得
      INSTANCE_STATUS_LIST=()
      for INSTANCE in "${CHANGED_INSTANCE_LIST[@]}"
      do
        INSTANCE_STATUS=$(
          aws ec2 describe-instances \
            --output text \
            --instance-ids ${INSTANCE} --query "Reservations[].Instances[].{State:State.Name}")
        INSTANCE_STATUS_LIST+=(${INSTANCE_STATUS})
      done

      # インスタンスのステータスを確認
      FLAG_FINISHED_TERMINATION=0
      for NUM in $(seq 0 $((${#CHANGED_INSTANCE_LIST[*]} - 1)))
      do
        echo "${CHANGED_INSTANCE_LIST[${NUM}]}: ${INSTANCE_STATUS_LIST[${NUM}]}"
        if [[ "${INSTANCE_STATUS_LIST[${NUM}]}" != "terminated" ]]; then
          FLAG_FINISHED_TERMINATION=1
        fi
      done

      # 動作に問題があれば規定回数繰り返す
      if [[ "${FLAG_FINISHED_TERMINATION}" == "0" ]]; then
        echo "Finished termination."
        echo "RESULT: OK"
        echo ""
        break
      else
        if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
          echo "ERROR: Instance check is failed. Please check status. Stop the script."
          echo "RESULT: NG"
          echo ""
          exit 1
        fi
        echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
        sleep ${SLEEP_TIME}
      fi
    done
  fi
}

#
# Main
#

# 引数を変数に代入
while getopts ht:c:n: OPT
do
  case $OPT in
    t) TYPE=$OPTARG;;
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# 引数チェック
if ! $(CHECK_ARGUMENT); then
  USAGE
  exit 1
fi

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target Auto Scaling Group"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
CHANGE_ASG_SIZE

echo ""
echo "Completed $0."
echo ""
