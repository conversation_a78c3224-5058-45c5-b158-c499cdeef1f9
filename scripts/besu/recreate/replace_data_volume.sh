#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/../common_function.sh

#
# 変数
#
TO_VOLUMES=""
CONTAINS=""
NOT_CONTAINS=""
NUMBER=""

INSTANCE_LIST=()
FROM_VOLUME_LIST=()
FROM_VOLUME_AZ_LIST=()
TO_VOLUME_LIST=()
TO_VOLUME_AZ_LIST=()
TO_VOLUME_STATUS_LIST=()

SLEEP_TIME=5
MAX_SLEEP_NUM=5

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to replace data volume of AWS instances in order of new launch time.
    The script can replace multiple data volumes for each instances all at once.
    But please take care of availability zone. The script will replace them placing in the same availability zone.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    -v <value>
    [-c <value>]
    [-n <value>]
    [-m <value>]

  OPTIONS
    -v (string) (REQUIRED)
        Specify volume ID separated by commas.
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.
    -m (string)
        Specify instance number.

  EXAMPLES
    Example 1: To replace data volume of AWS instances which contain "validator" and "shared".
      $0 -v vol-xxxxxxxx,vol-yyyyyyyy -c validator,shared
    Example 2: To replace data volume of AWS instance which contains "validator" and "shared", and target is only 1.
      $0 -v vol-xxxxxxxx -c validator,shared -m 1
----------------------------------------------------------------
EOF
}

#
# 引数チェック
#
function CHECK_ARGUMENT () {
  if [[ "${TO_VOLUMES}" == "" ]]; then
    return 1
  fi

  return 0
}

#
# 対象の確認
#
function CHECK_TARGET () {
  # 対象インスタンスを確認
  COMMAND_DESCRIBE_INSTANCES="aws ec2 describe-instances --query 'reverse(sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId, Name:Tags[?Key==\`Name\`] | [0].Value},&LaunchTime))'"
  COMMAND_DESCRIBE_INSTANCES+=" --filters \"Name=instance-state-name,Values=running\""
  COMMAND_SELECT_CONTAIN=""
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({Name: \"${CONTAIN}\"})) | "
    done
  fi

  COMMAND_SELECT_NOT_CONTAIN=""
  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({Name: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi

  COMMAND_HEAD=""
  if [[ "${NUMBER}" != "" ]]; then
    COMMAND_HEAD="| head -${NUMBER}"
  fi

  # インスタンスリストを確認する
  INSTANCE_LIST=($(echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash))

  # インスタンスにアタッチされているデータボリュームとAZを確認する
  for INSTANCE in "${INSTANCE_LIST[@]}"
  do
    FROM_VOLUME=$(
      aws ec2 describe-volumes \
        --filter Name=attachment.instance-id,Values=${INSTANCE} Name=attachment.device,Values=/dev/sdf | \
        jq -r ".Volumes[] | .VolumeId")
    FROM_VOLUME_AZ=$(
      aws ec2 describe-volumes --volume-ids "${FROM_VOLUME}" \
        --output text \
        --query "Volumes[].{AvailabilityZone:AvailabilityZone}")

    FROM_VOLUME_LIST+=("${FROM_VOLUME}")
    FROM_VOLUME_AZ_LIST+=("${FROM_VOLUME_AZ}")
  done

  # 付け替えるボリュームとAZ、状態を確認する
  for TO_VOLUME in $(echo "${TO_VOLUMES//,/ }")
  do
    TO_VOLUME_AZ=$(
      aws ec2 describe-volumes --volume-ids "${TO_VOLUME}" \
        --output text \
        --query "Volumes[].{AvailabilityZone:AvailabilityZone}")
    TO_VOLUME_STATUS=$(
      aws ec2 describe-volumes --volume-ids "${TO_VOLUME}" \
        --output text \
        --query "Volumes[].{State:State}")

    TO_VOLUME_LIST+=("${TO_VOLUME}")
    TO_VOLUME_AZ_LIST+=("${TO_VOLUME_AZ}")
    TO_VOLUME_STATUS_LIST+=("${TO_VOLUME_STATUS}")
  done
}

#
# 対象の表示
#
function DISPLAY_TARGET () {
  echo "## Target instances (current volume, availability zone)"
  for NUM in $(seq 0 $((${#INSTANCE_LIST[*]} - 1)))
  do
    echo "${INSTANCE_LIST[${NUM}]} (${FROM_VOLUME_LIST[${NUM}]}, ${FROM_VOLUME_AZ_LIST[${NUM}]})"
  done
  echo ""

  echo "## Target volumes (availability zone, volume status)"
  for NUM in $(seq 0 $((${#TO_VOLUME_LIST[*]} - 1)))
  do
    echo "${TO_VOLUME_LIST[${NUM}]}, ${TO_VOLUME_AZ_LIST[${NUM}]}, ${TO_VOLUME_STATUS_LIST[${NUM}]}"
  done
  echo ""
}

#
# 実行可能か確認
#
function CHECK_EXECUTABLE() {
  # インスタンス数と付け替えるボリューム数が同じこと
  if [[ "${#INSTANCE_LIST[*]}" != "${#TO_VOLUME_LIST[*]}" ]]; then
    echo "ERROR: Target instance number and target volume number are different. Stop the script."
    exit 1
  fi

  # 付け替えるボリュームが利用可能状態であること
  COMMAND_DESCRIBE_VOLUMES="aws ec2 describe-volumes --filter Name=status,Values=available | jq -r  '.Volumes[] | .VolumeId'"
  for TO_VOLUME_STATUS in "${TO_VOLUME_STATUS_LIST[@]}"
  do
    if [[ "${TO_VOLUME_STATUS}" != "available" ]]; then
      echo "ERROR: Target volumes includes non available status. Stop the script."
      exit 1
    fi
  done

  # アタッチされているボリュームと付け替えるボリュームのAZ群が同じこと
  FROM_SORT="$(printf "%s\n" "${FROM_VOLUME_AZ_LIST[@]}" | sort)"
  TO_SORT="$(printf "%s\n" "${TO_VOLUME_AZ_LIST[@]}" | sort)"
  if [[ "${FROM_SORT}" != "${TO_SORT}" ]]; then
    echo "ERROR: Attached volumes and target volumes are different AZ. Stop the script."
    exit 1
  fi
}

#
# データボリュームの付け替え
#
function REPLACE_DATA_VOLUME () {
  # インスタンスのデータボリュームをデタッチ
  echo "## Detaching current data volumes...."
  for FROM_VOLUME in "${FROM_VOLUME_LIST[@]}"
  do
    aws ec2 detach-volume --volume-id ${FROM_VOLUME}
  done
  echo "Requested detaching."
  echo ""

  # データボリュームがデタッチされたことを確認
  echo "## Checking whether it is detached or not...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    FLAG_DETACHED=0
    for FROM_VOLUME in "${FROM_VOLUME_LIST[@]}"
    do
      FROM_VOLUME_STATUS=$(
        aws ec2 describe-volumes --volume-ids "${FROM_VOLUME}" \
          --output text \
          --query "Volumes[].{State:State}")
      echo "${FROM_VOLUME}: ${FROM_VOLUME_STATUS}"
      if [[ "${FROM_VOLUME_STATUS}" != "available" ]]; then
        FLAG_DETACHED=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_DETACHED}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Checking available volume is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done

  # 指定されたデータボリュームをアタッチ
  echo "## Attaching target data volumes...."
  TMP_TO_VOLUME_LIST=(${TO_VOLUME_LIST[@]})
  TMP_TO_VOLUME_AZ_LIST=(${TO_VOLUME_AZ_LIST[@]})
  NUM_MAX=${#INSTANCE_LIST[*]}

  for NUM in $(seq 0 $((${NUM_MAX} - 1)))
  do
    # 同じAZを探し、ボリュームをアタッチする
    for TO_NUM in $(seq 0 $((${NUM_MAX} - 1)))
    do
      if [ "${FROM_VOLUME_AZ_LIST[${NUM}]}" == "${TMP_TO_VOLUME_AZ_LIST[${TO_NUM}]}" ]; then
        # ボリュームをアタッチ
        echo "Attach volume: ${TMP_TO_VOLUME_LIST[${TO_NUM}]} -> ${INSTANCE_LIST[${NUM}]}"
        aws ec2 attach-volume --instance-id "${INSTANCE_LIST[${NUM}]}" \
          --volume-id "${TMP_TO_VOLUME_LIST[${TO_NUM}]}" --device /dev/sdf

        # アタッチした対象を配列要素から削除
        unset TMP_TO_VOLUME_LIST[${TO_NUM}]
        unset TMP_TO_VOLUME_AZ_LIST[${TO_NUM}]
        break
      fi
    done
  done
  echo "Requested attaching."
  echo ""

  # データボリュームがアタッチされたことを確認
  echo "## Checking whether it is attached or not...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    FLAG_ATTACHED=0
    for TO_VOLUME in "${TO_VOLUME_LIST[@]}"
    do
      TO_VOLUME_STATUS=$(
        aws ec2 describe-volumes --volume-ids "${TO_VOLUME}" \
          --output text \
          --query "Volumes[].{State:State}")
      echo "${TO_VOLUME}: ${TO_VOLUME_STATUS}"
      if [[ "${TO_VOLUME_STATUS}" != "in-use" ]]; then
        FLAG_ATTACHED=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_ATTACHED}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Checking volume is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# データボリュームのマウント
#
function MOUNT_DATA_VOLUME () {
  # データボリュームのマウント
  echo "## Mounting data volumes...."
  INSTANCE_IDS=""
  for INSTANCE in "${INSTANCE_LIST[@]}"
  do
    INSTANCE_IDS+=",\"${INSTANCE}\""
  done
  INSTANCE_IDS="[${INSTANCE_IDS#,}]"
  TARGETS="[{ \"Key\":\"InstanceIds\",\"Values\":${INSTANCE_IDS} }]"

  RES=$(aws ssm send-command --document-name "AWS-RunShellScript" \
    --targets "${TARGETS}" \
    --parameters '{"workingDirectory":[""],"executionTimeout":["3600"],"commands":["DEVICE_NAME=$(lsblk | grep \"120G\" | awk '"'"'{print $1}'"'"')","mount /dev/${DEVICE_NAME} /mnt"]}' \
    --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
  COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')
  echo "Requested mounting."
  echo ""

  # マウント実施時の出力確認
  echo "Checking response...."
  RES_INSTANCE_ID_LIST=($(
    aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
      --output text \
      --query 'CommandInvocations[].{InstanceId:InstanceId}'))
  RES_JSON_OUTPUT=$(
    aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
      --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

  FLAG_RES_MOUNT=0
  for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1)))
  do
    OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
    echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} response: "
    if [[ "${OUTPUT}" == "\"\"" ]]; then
      echo "OK"
    else
      echo "NG"
      FLAG_RES_MOUNT=1
    fi
  done

  if [[ "${FLAG_RES_MOUNT}" == "0" ]]; then
    echo "No issue."
    echo ""
  else
    echo "ERROR: Response for mounting data volume is failed. Please check the status. Stop the script."
    exit 1
  fi

  # マウント状況を確認
  echo "## Checking mount status...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    RES=$(aws ssm send-command --document-name "AWS-RunShellScript" \
      --targets "${TARGETS}" \
      --parameters '{"workingDirectory":[""],"executionTimeout":["3600"],"commands":["lsblk"]}' \
      --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
    COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')

    RES_INSTANCE_ID_LIST=($(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --output text \
        --query 'CommandInvocations[].{InstanceId:InstanceId}'))
    RES_JSON_OUTPUT=$(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

    FLAG_RES_STATUS=0
    for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1)))
    do
      OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
      echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} mount status: "
      if [[ "${OUTPUT}" =~ "/mnt" ]]; then
        echo "OK"
      else
        echo "NG"
        FLAG_RES_STATUS=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_RES_STATUS}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Response for checking mount status is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# Main
#

# 引数を変数に代入
while getopts hv:c:n:m: OPT
do
  case $OPT in
    v) TO_VOLUMES=$OPTARG;;
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    m) NUMBER=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# 引数チェック
if ! $(CHECK_ARGUMENT); then
  USAGE
  exit 1
fi

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target instances"
echo "----------------------------------------------------------------"
CHECK_TARGET
DISPLAY_TARGET
CHECK_EXECUTABLE
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
REPLACE_DATA_VOLUME
MOUNT_DATA_VOLUME

echo ""
echo "Completed $0."
echo ""
