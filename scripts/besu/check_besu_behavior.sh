#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/common_function.sh

#
# 変数
#
CONTAINS=""
NOT_CONTAINS=""
NUMBER=""

INSTANCE_LIST=()

SLEEP_TIME=10
MAX_SLEEP_NUM=5

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to check BESU behavior in order of new launch time.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    [-c <value>]
    [-n <value>]
    [-m <value>]

  OPTIONS
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.
    -m (string)
        Specify instance number.

  EXAMPLES
    Example 1: To check BESU behavior of instances which contain "validator" and "shared".
      $0 -c validator,shared
    Example 2: To check BESU behavior of instance which contains "validator" and "shared", and target is only 1.
      $0 -c validator,shared -m 1
----------------------------------------------------------------
EOF
}

#
# 対象の確認
#
function CHECK_TARGET () {
  COMMAND_DESCRIBE_INSTANCES="aws ec2 describe-instances --query 'reverse(sort_by(Reservations[].Instances[].{LaunchTime: LaunchTime, InstanceId:InstanceId, Name:Tags[?Key==\`Name\`] | [0].Value},&LaunchTime))'"
  COMMAND_DESCRIBE_INSTANCES+=" --filters \"Name=instance-state-name,Values=running\""

  COMMAND_SELECT_CONTAIN=""
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({Name: \"${CONTAIN}\"})) | "
    done
  fi

  COMMAND_SELECT_NOT_CONTAIN=""
  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({Name: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi

  COMMAND_HEAD=""
  if [[ "${NUMBER}" != "" ]]; then
    COMMAND_HEAD="| head -${NUMBER}"
  fi

  echo "## Target instances"
  echo "\$ ${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}"
  echo ""
  echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash
  echo ""

  # インスタンスリストを控えておく
  INSTANCE_LIST=($(echo "${COMMAND_DESCRIBE_INSTANCES} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .InstanceId' ${COMMAND_HEAD}" | bash))
}

#
# BESUの動作確認
#
function CHECK_BESU_BEHAVIOR () {
  INSTANCE_IDS=""
  for INSTANCE in "${INSTANCE_LIST[@]}"
  do
    INSTANCE_IDS+=",\"${INSTANCE}\""
  done
  INSTANCE_IDS="[${INSTANCE_IDS#,}]"
  TARGETS="[{ \"Key\":\"InstanceIds\",\"Values\":${INSTANCE_IDS} }]"

  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    FLAG_OK=0

    # validatorを認識しているか確認
    echo "## Checking to be aware of validators...."
    RES=$(aws ssm send-command --document-name "AWS-RunShellScript" \
      --targets "${TARGETS}" \
      --parameters '{"workingDirectory":[""],"executionTimeout":["3600"],"commands":["curl -s -X POST --data '"'"'{\"jsonrpc\":\"2.0\",\"method\":\"ibft_getValidatorsByBlockNumber\",\"params\":[\"latest\"], \"id\":1}'"'"' http://127.0.0.1:8451"]}' \
      --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
    COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')

    RES_INSTANCE_ID_LIST=($(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --output text \
        --query 'CommandInvocations[].{InstanceId:InstanceId}'))
    RES_JSON_OUTPUT=$(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

    for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1)))
    do
      OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
      echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} response: "
      # "result":[" があること (同期できていない場合のresultは配列が空 [] なので " の有無で判定する)
      if [[ "${OUTPUT}" =~ '\"result\":[\"' ]]; then
        echo "OK"
      else
        echo "NG"
        FLAG_OK=1
      fi
    done

    # 接続ノードがあるか確認
    echo "## Checking connection nodes...."
    RES=$(aws ssm send-command --document-name "AWS-RunShellScript" \
      --targets "${TARGETS}" \
      --parameters '{"workingDirectory":[""],"executionTimeout":["3600"],"commands":["curl -s -X POST --data '"'"'{\"jsonrpc\":\"2.0\",\"method\":\"admin_peers\",\"params\":[],\"id\":1}'"'"' http://127.0.0.1:8451 | grep remoteAddress | sort"]}' \
      --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
    COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')

    RES_INSTANCE_ID_LIST=($(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --output text \
        --query 'CommandInvocations[].{InstanceId:InstanceId}'))
    RES_JSON_OUTPUT=$(
      aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
        --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

    for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1)))
    do
      OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
      echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} response: "
      # \"remoteAddress\"があること
      if [[ "${OUTPUT}" =~ '\"remoteAddress\"' ]]; then
        echo "OK"
      else
        echo "NG"
        FLAG_OK=1
      fi
    done

    # チェーンヘッドが更新されることを確認
    echo "## Checking to update chain head...."
    # 1回目の確認
    RES_1=$(aws ssm send-command --document-name "AWS-RunShellScript" \
      --targets "${TARGETS}" \
      --parameters '{"workingDirectory":[""],"executionTimeout":["3600"],"commands":["curl -s -X POST --data '"'"'{\"jsonrpc\":\"2.0\",\"method\":\"eth_blockNumber\",\"paams\":[],\"id\":1}'"'"' http://127.0.0.1:8451"]}' \
      --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
    COMMAND_ID_1=$(echo ${RES_1} | jq -r '.Command.CommandId')

    RES_1_INSTANCE_ID_LIST=($(
      aws ssm list-command-invocations --command-id ${COMMAND_ID_1} --details \
        --output text \
        --query 'CommandInvocations[].{InstanceId:InstanceId}'))
    RES_1_JSON_OUTPUT=$(
      aws ssm list-command-invocations --command-id ${COMMAND_ID_1} --details \
        --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

    sleep 2

    # 2回目の確認
    RES_2=$(aws ssm send-command --document-name "AWS-RunShellScript" \
      --targets "${TARGETS}" \
      --parameters '{"workingDirectory":[""],"executionTimeout":["3600"],"commands":["curl -s -X POST --data '"'"'{\"jsonrpc\":\"2.0\",\"method\":\"eth_blockNumber\",\"paams\":[],\"id\":1}'"'"' http://127.0.0.1:8451"]}' \
      --timeout-seconds 600 --max-concurrency "50" --max-errors "0")
    COMMAND_ID_2=$(echo ${RES_2} | jq -r '.Command.CommandId')

    RES_2_INSTANCE_ID_LIST=($(
      aws ssm list-command-invocations --command-id ${COMMAND_ID_2} --details \
        --output text \
        --query 'CommandInvocations[].{InstanceId:InstanceId}'))
    RES_2_JSON_OUTPUT=$(
      aws ssm list-command-invocations --command-id ${COMMAND_ID_2} --details \
        --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}')

    for NUM in $(seq 0 $((${#RES_1_INSTANCE_ID_LIST[*]} - 1)))
    do
      OUTPUT_1=$(echo "${RES_1_JSON_OUTPUT}" | jq .["${NUM}"].Output)
      OUTPUT_2=$(echo "${RES_2_JSON_OUTPUT}" | jq .["${NUM}"].Output)
      echo -n "${RES_1_INSTANCE_ID_LIST[${NUM}]} response: "
      # block numberが異なること
      if [[ "${OUTPUT_1}" != "${OUTPUT_2}" ]]; then
        echo "OK"
      else
        echo "NG"
        FLAG_OK=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_OK}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Checking BESU behavior is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# Main
#

# 引数を変数に代入
while getopts ht:c:n:m: OPT
do
  case $OPT in
    t) TYPE=$OPTARG;;
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    m) NUMBER=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target instances"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
CHECK_BESU_BEHAVIOR

echo ""
echo "Completed $0."
echo ""
