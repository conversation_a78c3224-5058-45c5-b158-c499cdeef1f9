#!/bin/bash
# transferを実行する
# 事前準備：
# 　coreのsignature-toolsをload-test-toolsと同階層に配置すること
# 　transfer元、transfer先のアカウントの事前作成が必要(共通領域用口座開設受付まで実行すること)
# 　script_test_common.envに下記が必要
#   　BASE_URL: 接続URL
#   　FROM_SIGN_IN_ID_LIST: 送金元のサインインID
#   　TO_APP_PASSWORD_LIST: 送金元のPASSWORD
#   　FROM_SIGN_IN_ID_LIST: 送金先のサインインID
#   　TO_APP_PASSWORD_LIST: 送金先のPASSWORD

source script_test_common.env
# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# transferの実行
# 引数1 BASE_URL
# 引数2 ID_TOKEN
# 引数3 送金元AccountId
# 引数4 INFO
# 引数5 SK_O
# 引数6 送金先AccountId
# 引数7 送金先DC口座番号
# 引数8 ループの回数（transferの金額にも使用）
execTransfer () {
  URL=$1
  TOKEN=$2
  FROM_ACCOUNT_ID=$3
  INFO=$4
  SK_O=$5
  TO_ACCOUNT_ID=$6
  TO_DC_BANK_NUMBER=$7
  LOOP=$8

  ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/transfer_signature.sh "$SK_O" "$FROM_ACCOUNT_ID" "$FROM_ACCOUNT_ID" "$TO_ACCOUNT_ID" $LOOP | grep "account signature:" | sed -r 's/^account signature: //')

  START_TIME=$(date +%s)
  echo "start: timestamp: $(date +%s) to_dc_bank_number: $TO_DC_BANK_NUMBER loop: $LOOP"

  # transfer
  REQUEST_BODY='{"to_dc_bank_number":"'$TO_DC_BANK_NUMBER'","transfer_amount":'$LOOP',"account_signature":"'$ACCOUNT_SIGNATURE'","info":"'$INFO'"}'
  TRANSFER_RES=$(curl -Ss -X POST $URL/transactions/transfer -H "Authorization:$TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY")
  TX_HASH=$(echo $TRANSFER_RES | jq -r ".transaction_hash")

  TRANSFER_END_TIME=`expr $(date +%s) - $START_TIME`
  echo "end transfer: timestamp: $(date +%s) elapsed: $TRANSFER_END_TIME to_dc_bank_number: $TO_DC_BANK_NUMBER loop: $LOOP tx_hash: $TX_HASH transfer_amount: $LOOP"
}

# transfer処理の呼び出し
# 引数1 BASE_URL
# 引数2 ID_TOKEN
# 引数3 送金元AccountId
# 引数4 INFO
# 引数5 SK_O
# 引数6 送金先AccountId
# 引数7 送金先DC口座番号
callTransfer (){
  for i in $(seq 1 $CONTINUOUS_TIME)
  do
    execTransfer $1 $2 $3 $4 $5 $6 $7 $i
  done
}


# メイン処理
declare -a FROM_SIGN_IN_ID=(${FROM_SIGN_IN_ID_LIST//,/ })
declare -a FROM_APP_PASSWORD=(${FROM_APP_PASSWORD_LIST//,/ })
declare -a TO_SIGN_IN_ID=(${TO_SIGN_IN_ID_LIST//,/ })
declare -a TO_APP_PASSWORD=(${TO_APP_PASSWORD_LIST//,/ })

if [ ${#FROM_SIGN_IN_ID[*]} -ne ${#FROM_APP_PASSWORD[*]} ] || [ ${#FROM_SIGN_IN_ID[*]} -ne ${#TO_SIGN_IN_ID[*]} ] || [ ${#FROM_SIGN_IN_ID[*]} -ne ${#TO_APP_PASSWORD[*]} ]; then
  echo "ERROR:The number of users in SIGN_IN_ID_LIST and APP_PASSWORD_LIST should be equal."
  exit 1
fi

# それぞれのアカウントでログインする
NUM_OF_USERS=${#FROM_SIGN_IN_ID[*]}
ID_TOKENS=()
FROM_ACCOUNT_IDS=()
INFOS=()
TO_ACCOUNT_IDS=()
TO_DC_BANK_NUMBERS=()
for ((i=0; i<NUM_OF_USERS; i++))
do
  ID_TOKENS+=(`curl -X POST ${BASE_URL}/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"${FROM_SIGN_IN_ID[$i]}\",\"password\":\"${FROM_APP_PASSWORD[$1]}\"}" | jq -r '.id_token'`)
  FROM_ACCOUNT_IDS+=(`curl -X POST ${BASE_URL}/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"${FROM_SIGN_IN_ID[$i]}\",\"password\":\"${FROM_APP_PASSWORD[$1]}\"}" | jq -r '.account_id'`)
  INFOS+=(`curl -X GET ${BASE_URL}/user/security -H "Authorization:${ID_TOKENS[$i]}" -H "Content-Type: application/json" | jq -r '.info'`)
  SK_O+=(`curl -X GET ${BASE_URL}/user/security -H "Authorization:${ID_TOKENS[$i]}" -H "Content-Type: application/json" | jq -r '.sk_o'`)
  TO_ACCOUNT_IDS+=(`curl -X POST ${BASE_URL}/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"${TO_SIGN_IN_ID[$i]}\",\"password\":\"${TO_APP_PASSWORD[$1]}\"}" | jq -r '.account_id'`)
  TO_DC_BANK_NUMBERS+=(`curl -X POST ${BASE_URL}/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"${TO_SIGN_IN_ID[$i]}\",\"password\":\"${TO_APP_PASSWORD[$1]}\"}" | jq -r '.dc_bank_number'`)
done

for ((i=0; i<NUM_OF_USERS; i++))
do
  callTransfer $BASE_URL ${ID_TOKENS[$i]} ${FROM_ACCOUNT_IDS[$i]} ${INFOS[$i]} ${SK_O[$i]} ${TO_ACCOUNT_IDS[$i]} ${TO_DC_BANK_NUMBERS[$i]} &
done

exit 0