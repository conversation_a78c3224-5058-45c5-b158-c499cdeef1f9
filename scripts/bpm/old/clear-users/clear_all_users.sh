#!/bin/bash
# BPM Server の account_info に登録されているユーザ情報、およびこれに該当する COGNITO の認証情報を削除する。

# 設定ファイルの存在チェック
if [  ! -e "bpm.env" ]; then
  echo "bpm.env が存在しません。template.env ファイルをコピーして、設定ファイルを用意してください。"
  exit 1
fi

echo ""
echo "---- bpm.env の設定内容 ----"
cat ./bpm.env
echo ""
echo "-----------------------------------"
echo ""

echo "account_info のユーザ情報削除を行います。上記設定内容で実行する場合は y を入力してください。"
echo "また、実行前に scripts/bpm/test-tools/port_forward.sh を実行して DB へのポートフォワードを行ってください。"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

source ./bpm.env

# BPM Server の DB に接続
RESULT=$(psql -h $DB_HOST -p $LOCAL_DB_PORT -U $DB_USER -d $DB_NAME -t -c \
    "SELECT DISTINCT international_phone_number FROM account_info WHERE international_phone_number IS NOT NULL" \
)
# DB より取得した国際電話番号を配列化
PHONE_NUMBERS=(`echo $RESULT`)

if [ ${#PHONE_NUMBERS[@]} -eq 0 ]; then
    echo "account_info テーブルにユーザ情報が存在しなかったので終了します。"
    exit 0
fi

for PHONE_NUMBER in ${PHONE_NUMBERS[@]}; do
    DOMAIN_PHONE_NUMBER="0${PHONE_NUMBER:3}"
    echo "phone_number $DOMAIN_PHONE_NUMBER を削除します。"
    curl -Ss -X DELETE -H "Content-Type: application/json" "$BASE_URL/management/user/$DOMAIN_PHONE_NUMBER"
    echo ""
done

echo "account_info のユーザ情報削除が完了しました。"
