#!/bin/bash
# 共通領域用準正常系テストの事前準備を行うシェル

echo "対象の環境にポートフォワード済、対象の環境のProfileに変更していることを確認し、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit
fi

# 環境変数の設定
source ./env-bpmfin.sh

cp /dev/null env_semi_normal_01.sh
echo "#!/bin/bash" >> env_semi_normal_01.sh

# SEMI_NORMAL_PHONE_NUMBERを基準に、PHONE_NUMBER8まで作成する
PHONE_NUMBER=$(echo $SEMI_NORMAL_PHONE_NUMBER | cut -c -10)
for i in {1..8}
do
  echo "export PHONE_NUMBER${i}=$PHONE_NUMBER${i}" >> env_semi_normal_01.sh
done

# SEMI_NORMAL_INTERNATIONAL_PHONE_NUMBERを基準に、INTERNATIONAL_PHONE_NUMBER8まで作成する
INTERNATIONAL_PHONE_NUMBER=$(echo $SEMI_NORMAL_INTERNATIONAL_PHONE_NUMBER | cut -c -12)
for j in {1..8}
do
  echo "export INTERNATIONAL_PHONE_NUMBER${j}=$INTERNATIONAL_PHONE_NUMBER${j}" >> env_semi_normal_01.sh
done

# SEMI_NORMAL_PASSWORDを基準に、PASSWORD8まで作成する
PASSWORD=$(echo $SEMI_NORMAL_PASSWORD | cut -c -8)
for x in {1..8}
do
  echo "export PASSWORD${x}=$PASSWORD${x}" >> env_semi_normal_01.sh
done

# SEMI_NORMAL_SIGN_IN_IDを基準に、SIGN_IN_ID8まで作成する
SIGN_IN_ID=$(echo $SEMI_NORMAL_SIGN_IN_ID | cut -c -6)
for y in {1..8}
do
  echo "export SIGN_IN_ID${y}=$SIGN_IN_ID${y}" >> env_semi_normal_01.sh
done

# 作成した環境変数の設定
source ./env_semi_normal_01.sh

echo "----- semi_normal_create_account_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# 管理者を作成
MANAGEMENT_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\"}" )
TEMPORARY_PASSWORD=$(echo $MANAGEMENT_RES | jq -r ".temporary_password")
if [ $TEMPORARY_PASSWORD == null ]; then
  echo "管理者の作成に失敗しました"
fi

ADMIN_SIGNIN_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$TEMPORARY_PASSWORD\"}");
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")
if [ $ADMIN_SESSION == null ]; then
  echo "管理者の作成に失敗しました"
fi

curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"$ADMIN_SESSION\"}"
echo ""

ADMIN_SIGNIN_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者の作成に失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者の作成に失敗しました"
  exit 1
fi

# 1つ目の口座を作成（サインアップまで）
./create-test-user.sh "F1" "$INTERNATIONAL_PHONE_NUMBER1" "$SIGN_IN_ID1" "$PASSWORD1"

# 2つ目の口座を作成（端末認証まで）
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成
./create-test-user.sh "F2" "$INTERNATIONAL_PHONE_NUMBER2" "$SIGN_IN_ID2" "$PASSWORD2"
SIGN_IN_RES2=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
if [ $ACCESS_TOKEN2 == null ]; then
  echo "2つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN2 == null ]; then
  echo "2つ目の口座のサインインに失敗しました"
fi

# 3つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成（残高0）
./create-test-user.sh "F3" "$INTERNATIONAL_PHONE_NUMBER3" "$SIGN_IN_ID3" "$PASSWORD3"
SIGN_IN_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
DC_BANK_NUMBER3=$(echo $SIGN_IN_RES3 | jq -r ".dc_bank_number")
ACCOUNT_ID3=$(echo $SIGN_IN_RES3 | jq -r ".account_id")
if [ $ACCESS_TOKEN3 == null ]; then
  echo "3つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN3 == null ]; then
  echo "3つ目の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER3 == null ]; then
  echo "3つ目の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID3 == null ]; then
  echo "3つ目の口座のサインインに失敗しました"
fi

SECURITY_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json")
SK_O3=$(echo $SECURITY_RES3 | jq -r ".sk_o")
INFO3=$(echo $SECURITY_RES3 | jq -r ".info")
if [ $SK_O3 == null ]; then
  echo "3つ目の口座の署名作成に失敗しました"
fi

if [ $INFO3 == null ]; then
  echo "3つ目の口座の署名作成に失敗しました"
fi

TRANSFER_SIGNATURE3=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O3 $ACCOUNT_ID3 $ACCOUNT_ID3 $ACCOUNT_ID4 2000 | grep "account signature:" | sed -r 's/^account signature: //')
SYNCHRONOUS_SIGNATURE3=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O3 $ACCOUNT_ID3 | grep "account signature:" | sed -r 's/^account signature: //')
EXCHANGE_SIGNATURE3=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O3 $ACCOUNT_ID3 $REGION_ID 3000 | grep "account signature:" | sed -r 's/^account signature: //')

# 4つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成（残高あり）
./create-test-user.sh "F3" "$INTERNATIONAL_PHONE_NUMBER4" "$SIGN_IN_ID4" "$PASSWORD4"
SIGN_IN_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")
ACCOUNT_ID4=$(echo $SIGN_IN_RES4 | jq -r ".account_id")
if [ $ACCESS_TOKEN4 == null ]; then
  echo "4つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN4 == null ]; then
  echo "4つ目の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER4 == null ]; then
  echo "4つ目の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID4 == null ]; then
  echo "4つ目の口座のサインインに失敗しました"
fi

SECURITY_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
SK_O4=$(echo $SECURITY_RES4 | jq -r ".sk_o")
INFO4=$(echo $SECURITY_RES4 | jq -r ".info")
if [ $SK_O4 == null ]; then
  echo "4つ目の口座の署名作成に失敗しました"
fi

if [ $INFO4 == null ]; then
  echo "4つ目の口座の署名作成に失敗しました"
fi

curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"mint_amount\":100000}"
echo ""

TRANSFER_SIGNATURE4=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O4 $ACCOUNT_ID4 $ACCOUNT_ID4 $ACCOUNT_ID3 2000 | grep "account signature:" | sed -r 's/^account signature: //')
EXCHANGE_SIGNATURE4=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O4 $ACCOUNT_ID4 $REGION_ID 3000 | grep "account signature:" | sed -r 's/^account signature: //')
TRANSFER_SIGNATURE3=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O3 $ACCOUNT_ID3 $ACCOUNT_ID3 $ACCOUNT_ID4 2000 | grep "account signature:" | sed -r 's/^account signature: //')

# 5つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成（本人未確認）
./create-test-user.sh "F3" "$INTERNATIONAL_PHONE_NUMBER5" "$SIGN_IN_ID5" "$PASSWORD5"
SIGN_IN_RES5=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\"}")
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
DC_BANK_NUMBER5=$(echo $SIGN_IN_RES5 | jq -r ".dc_bank_number")
ACCOUNT_ID5=$(echo $SIGN_IN_RES5 | jq -r ".account_id")
if [ $ACCESS_TOKEN5 == null ]; then
  echo "5つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN5 == null ]; then
  echo "5つ目の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER5 == null ]; then
  echo "5つ目の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID5 == null ]; then
  echo "5つ目の口座のサインインに失敗しました"
fi

SECURITY_RES5=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
SK_O5=$(echo $SECURITY_RES5 | jq -r ".sk_o")
INFO5=$(echo $SECURITY_RES5 | jq -r ".info")
if [ $SK_O5 == null ]; then
  echo "5つ目の口座の署名作成に失敗しました"
fi

if [ $INFO5 == null ]; then
  echo "5つ目の口座の署名作成に失敗しました"
fi

curl -4 --proxy socks5://localhost:$SOCKS_PORT -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER5/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}"
echo ""

# 6つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成（本人確認済）
./create-test-user.sh "F3" "$INTERNATIONAL_PHONE_NUMBER6" "$SIGN_IN_ID6" "$PASSWORD6"
SIGN_IN_RES6=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID6\", \"password\":\"$PASSWORD6\"}")
ACCESS_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".access_token")
ID_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".id_token")
DC_BANK_NUMBER6=$(echo $SIGN_IN_RES6 | jq -r ".dc_bank_number")
ACCOUNT_ID6=$(echo $SIGN_IN_RES6 | jq -r ".account_id")
if [ $ACCESS_TOKEN6 == null ]; then
  echo "6つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN6 == null ]; then
  echo "6つ目の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER6 == null ]; then
  echo "6つ目の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID6 == null ]; then
  echo "6つ目の口座のサインインに失敗しました"
fi

SECURITY_RES6=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json")
SK_O6=$(echo $SECURITY_RES6 | jq -r ".sk_o")
INFO6=$(echo $SECURITY_RES6 | jq -r ".info")
if [ $SK_O6 == null ]; then
  echo "6つ目の口座の署名作成に失敗しました"
fi

if [ $INFO6 == null ]; then
  echo "6つ目の口座の署名作成に失敗しました"
fi

# 7つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成（解約済）
./create-test-user.sh "F3" "$INTERNATIONAL_PHONE_NUMBER7" "$SIGN_IN_ID7" "$PASSWORD7"
SIGN_IN_RES7=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID7\", \"password\":\"$PASSWORD7\"}")
ACCESS_TOKEN7=$(echo $SIGN_IN_RES7 | jq -r ".access_token")
ID_TOKEN7=$(echo $SIGN_IN_RES7 | jq -r ".id_token")
DC_BANK_NUMBER7=$(echo $SIGN_IN_RES7 | jq -r ".dc_bank_number")
ACCOUNT_ID7=$(echo $SIGN_IN_RES7 | jq -r ".account_id")
if [ $ACCESS_TOKEN7 == null ]; then
  echo "7つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN7 == null ]; then
  echo "7つ目の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER7 == null ]; then
  echo "7つ目の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID7 == null ]; then
  echo "7つ目の口座のサインインに失敗しました"
fi

SECURITY_RES7=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN7" -H "Content-Type: application/json")
SK_O7=$(echo $SECURITY_RES7 | jq -r ".sk_o")
INFO7=$(echo $SECURITY_RES7 | jq -r ".info")
if [ $SK_O7 == null ]; then
  echo "7つ目の口座の署名作成に失敗しました"
fi

if [ $INFO7 == null ]; then
  echo "7つ目の口座の署名作成に失敗しました"
fi

curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN7" -H "Content-Type: application/json" -d "{\"reason_code\":90000}"
echo ""

# 8つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成（認証停止）
./create-test-user.sh "F3" "$INTERNATIONAL_PHONE_NUMBER8" "$SIGN_IN_ID8" "$PASSWORD8"
SIGN_IN_RES8=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID8\", \"password\":\"$PASSWORD8\"}")
ACCESS_TOKEN8=$(echo $SIGN_IN_RES8 | jq -r ".access_token")
ID_TOKEN8=$(echo $SIGN_IN_RES8 | jq -r ".id_token")
DC_BANK_NUMBER8=$(echo $SIGN_IN_RES8 | jq -r ".dc_bank_number")
ACCOUNT_ID8=$(echo $SIGN_IN_RES8 | jq -r ".account_id")
if [ $ACCESS_TOKEN8 == null ]; then
  echo "8つ目の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN8 == null ]; then
  echo "8つ目の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER8 == null ]; then
  echo "8つ目の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID8 == null ]; then
  echo "8つ目の口座のサインインに失敗しました"
fi

SECURITY_RES8=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN8" -H "Content-Type: application/json")
SK_O8=$(echo $SECURITY_RES8 | jq -r ".sk_o")
INFO8=$(echo $SECURITY_RES8 | jq -r ".info")
if [ $SK_O8 == null ]; then
  echo "8つ目の口座の署名作成に失敗しました"
fi

if [ $INFO8 == null ]; then
  echo "8つ目の口座の署名作成に失敗しました"
fi

AUTH_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER8/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}")
STOP_CANCEL_NUMBER8=$(echo $AUTH_RES | jq -r ".stop_cancel_number")

# 口座作成の結果を、env_semi_normal_01.shに出力する
echo "export ADMIN_USER_NAME=$ADMIN_USER_NAME" >> env_semi_normal_01.sh
echo "export ADMIN_SESSION=$ADMIN_SESSION" >> env_semi_normal_01.sh
echo "export ADMIN_ACCESS_TOKEN=$ADMIN_ACCESS_TOKEN" >> env_semi_normal_01.sh
echo "export ADMIN_ID_TOKEN=$ADMIN_ID_TOKEN" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN2=$ACCESS_TOKEN2" >> env_semi_normal_01.sh
echo "export ID_TOKEN2=$ID_TOKEN2" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN3=$ACCESS_TOKEN3" >> env_semi_normal_01.sh
echo "export ID_TOKEN3=$ID_TOKEN3" >> env_semi_normal_01.sh
echo "export DC_BANK_NUMBER3=$DC_BANK_NUMBER3" >> env_semi_normal_01.sh
echo "export ACCOUNT_ID3=$ACCOUNT_ID3" >> env_semi_normal_01.sh
echo "export SK_O3=$SK_O3" >> env_semi_normal_01.sh
echo "export INFO3=$INFO3" >> env_semi_normal_01.sh
echo "export TRANSFER_SIGNATURE3=$TRANSFER_SIGNATURE3" >> env_semi_normal_01.sh
echo "export SYNCHRONOUS_SIGNATURE3=$SYNCHRONOUS_SIGNATURE3" >> env_semi_normal_01.sh
echo "export EXCHANGE_SIGNATURE3=$EXCHANGE_SIGNATURE3" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN4=$ACCESS_TOKEN4" >> env_semi_normal_01.sh
echo "export ID_TOKEN4=$ID_TOKEN4" >> env_semi_normal_01.sh
echo "export DC_BANK_NUMBER4=$DC_BANK_NUMBER4" >> env_semi_normal_01.sh
echo "export ACCOUNT_ID4=$ACCOUNT_ID4" >> env_semi_normal_01.sh
echo "export SK_O4=$SK_O4" >> env_semi_normal_01.sh
echo "export INFO4=$INFO4" >> env_semi_normal_01.sh
echo "export TRANSFER_SIGNATURE4=$TRANSFER_SIGNATURE4" >> env_semi_normal_01.sh
echo "export EXCHANGE_SIGNATURE4=$EXCHANGE_SIGNATURE4" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN5=$ACCESS_TOKEN5" >> env_semi_normal_01.sh
echo "export ID_TOKEN5=$ID_TOKEN5" >> env_semi_normal_01.sh
echo "export DC_BANK_NUMBER5=$DC_BANK_NUMBER5" >> env_semi_normal_01.sh
echo "export ACCOUNT_ID5=$ACCOUNT_ID5" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN6=$ACCESS_TOKEN6" >> env_semi_normal_01.sh
echo "export ID_TOKEN6=$ID_TOKEN6" >> env_semi_normal_01.sh
echo "export DC_BANK_NUMBER6=$DC_BANK_NUMBER6" >> env_semi_normal_01.sh
echo "export ACCOUNT_ID6=$ACCOUNT_ID6" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN7=$ACCESS_TOKEN7" >> env_semi_normal_01.sh
echo "export ID_TOKEN7=$ID_TOKEN7" >> env_semi_normal_01.sh
echo "export DC_BANK_NUMBER7=$DC_BANK_NUMBER7" >> env_semi_normal_01.sh
echo "export ACCOUNT_ID7=$ACCOUNT_ID7" >> env_semi_normal_01.sh

echo "export ACCESS_TOKEN8=$ACCESS_TOKEN8" >> env_semi_normal_01.sh
echo "export ID_TOKEN8=$ID_TOKEN8" >> env_semi_normal_01.sh
echo "export DC_BANK_NUMBER8=$DC_BANK_NUMBER8" >> env_semi_normal_01.sh
echo "export ACCOUNT_ID8=$ACCOUNT_ID8" >> env_semi_normal_01.sh

echo "----- semi_normal_create_account_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`