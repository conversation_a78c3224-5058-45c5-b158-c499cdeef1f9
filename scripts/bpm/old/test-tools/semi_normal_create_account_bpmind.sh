#!/bin/bash
# 付加領域用準正常系テストの事前準備を行うシェル

echo "対象の付加領域にポートフォワード済、対象の付加領域のProfileに変更していることを確認し、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit
fi

# 引数チェック
if [ $# -ne 6 ]; then
  echo "[引数なし] ./semi_normal_create_account_bpmind.sh \$ACCOUNT_ID3 \$SK_O3 \$INFO3 \$ACCOUNT_ID4 \$SK_O4 \$INFO4"
  exit 9
fi
# 共通領域で作成したACCOUNT_ID3
ACCOUNT_ID3=$1
# 共通領域の署名鍵取得のSK_O3
SK_O3=$2
# 共通領域の署名鍵取得のINFO3
INFO3=$3
# 共通領域で作成したACCOUNT_ID4
ACCOUNT_ID4=$4
# 共通領域の署名鍵取得のSK_O4
SK_O4=$5
# 共通領域の署名鍵取得のINFO4
INFO4=$6

# 環境変数の設定
source ./env-bpmind.sh

cp /dev/null env_semi_normal_03.sh
echo "#!/bin/bash" >> env_semi_normal_03.sh

# SEMI_NORMAL_PHONE_NUMBERを基準に、PHONE_NUMBER3とPHONE_NUMBER4を作成する
PHONE_NUMBER=$(echo $SEMI_NORMAL_PHONE_NUMBER | cut -c -10)
for i in {3..4}
do
  echo "export PHONE_NUMBER${i}=$PHONE_NUMBER${i}" >> env_semi_normal_03.sh
done

# SEMI_NORMAL_INTERNATIONAL_PHONE_NUMBERを基準に、INTERNATIONAL_PHONE_NUMBER3とINTERNATIONAL_PHONE_NUMBER4を作成する
INTERNATIONAL_PHONE_NUMBER=$(echo $SEMI_NORMAL_INTERNATIONAL_PHONE_NUMBER | cut -c -12)
for j in {3..4}
do
  echo "export INTERNATIONAL_PHONE_NUMBER${j}=$INTERNATIONAL_PHONE_NUMBER${j}" >> env_semi_normal_03.sh
done

# SEMI_NORMAL_PASSWORDを基準に、PASSWORD3とPASSWORD4を作成する
PASSWORD=$(echo $SEMI_NORMAL_PASSWORD | cut -c -8)
for x in {3..4}
do
  echo "export PASSWORD${x}=$PASSWORD${x}" >> env_semi_normal_03.sh
done

# SEMI_NORMAL_SIGN_IN_IDを基準に、SIGN_IN_ID3とSIGN_IN_ID4を作成する
SIGN_IN_ID=$(echo $SEMI_NORMAL_SIGN_IN_ID | cut -c -6)
for y in {3..4}
do
  echo "export SIGN_IN_ID${y}=$SIGN_IN_ID${y}" >> env_semi_normal_03.sh
done

# 作成した環境変数の設定
source ./env_semi_normal_03.sh

echo "----- semi_normal_create_account_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# 1つ目の口座を作成（残高0）
./create-test-user.sh "I2" "$INTERNATIONAL_PHONE_NUMBER3" "$SIGN_IN_ID3" "$PASSWORD3"
echo "1つ目の口座のサインイン"
SIGN_IN_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
if [ $ACCESS_TOKEN3 == null ]; then
  echo "1つ目の口座作成に失敗しました"
fi

if [ $ID_TOKEN3 == null ]; then
  echo "1つ目の口座作成に失敗しました"
fi

echo "1つ目の口座の付加領域アカウント開設受付チェック"
SYNCHRONOUS_SIGNATURE3=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O3 $ACCOUNT_ID3 | grep "account signature:" | sed -r 's/^account signature: //')
SYNC_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}")
echo $SYNC_RES3
DC_BANK_NUMBER3=$(echo $SYNC_RES3 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER3 == null ]; then
  echo "1つ目の口座作成に失敗しました"
fi

TRANSFER_SIGNATURE3=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O3 $ACCOUNT_ID3 $ACCOUNT_ID3 $ACCOUNT_ID4 2000 | grep "account signature:" | sed -r 's/^account signature: //')
EXCHANGE_SIGNATURE3=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O3 $ACCOUNT_ID3 $REGION_ID 3001 | grep "account signature:" | sed -r 's/^account signature: //')

# 2つ目の口座を作成（残高あり）
./create-test-user.sh "I2" "$INTERNATIONAL_PHONE_NUMBER4" "$SIGN_IN_ID4" "$PASSWORD4"
echo "2つ目の口座のサインイン"
SIGN_IN_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
if [ $ACCESS_TOKEN4 == null ]; then
  echo "2つ目の口座作成に失敗しました"
fi

if [ $ID_TOKEN4 == null ]; then
  echo "2つ目の口座作成に失敗しました"
fi

echo "2つ目の口座の付加領域アカウント開設受付チェック"
SYNCHRONOUS_SIGNATURE4=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O4 $ACCOUNT_ID4 | grep "account signature:" | sed -r 's/^account signature: //')
SYNC_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID4\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE4\", \"info\":\"$INFO4\"}")
echo $SYNC_RES4
DC_BANK_NUMBER4=$(echo $SYNC_RES4 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER4 == null ]; then
  echo "2つ目の口座作成に失敗しました"
fi

TRANSFER_SIGNATURE4=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O4 $ACCOUNT_ID4 $ACCOUNT_ID4 $ACCOUNT_ID3 2000 | grep "account signature:" | sed -r 's/^account signature: //')
EXCHANGE_SIGNATURE4=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O4 $ACCOUNT_ID4 $REGION_ID 3001 | grep "account signature:" | sed -r 's/^account signature: //')

# 口座作成の結果を、env_semi_normal_03.shに出力する
echo "export ACCESS_TOKEN3=$ACCESS_TOKEN3" >> env_semi_normal_03.sh
echo "export ID_TOKEN3=$ID_TOKEN3" >> env_semi_normal_03.sh
echo "export DC_BANK_NUMBER3=$DC_BANK_NUMBER3" >> env_semi_normal_03.sh
echo "export ACCOUNT_ID3=$ACCOUNT_ID3" >> env_semi_normal_03.sh
echo "export SYNCHRONOUS_SIGNATURE3=$SYNCHRONOUS_SIGNATURE3" >> env_semi_normal_03.sh
echo "export EXCHANGE_SIGNATURE3=$EXCHANGE_SIGNATURE3" >> env_semi_normal_03.sh
echo "export TRANSFER_SIGNATURE3=$TRANSFER_SIGNATURE3" >> env_semi_normal_03.sh

echo "export ACCESS_TOKEN4=$ACCESS_TOKEN4" >> env_semi_normal_03.sh
echo "export ID_TOKEN4=$ID_TOKEN4" >> env_semi_normal_03.sh
echo "export DC_BANK_NUMBER4=$DC_BANK_NUMBER4" >> env_semi_normal_03.sh
echo "export ACCOUNT_ID4=$ACCOUNT_ID4" >> env_semi_normal_03.sh
echo "export EXCHANGE_SIGNATURE4=$EXCHANGE_SIGNATURE4" >> env_semi_normal_03.sh
echo "export TRANSFER_SIGNATURE4=$TRANSFER_SIGNATURE4" >> env_semi_normal_03.sh

echo "----- semi_normal_create_account_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`