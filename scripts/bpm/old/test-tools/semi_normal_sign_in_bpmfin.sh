#!/bin/bash
# 共通領域の準正常系アカウントにサインインを行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

cp /dev/null env_semi_normal_02.sh
echo "#!/bin/bash" >> env_semi_normal_02.sh

# SEMI_NORMAL_PASSWORDを基準に、PASSWORD8まで作成する
PASSWORD=$(echo $SEMI_NORMAL_PASSWORD | cut -c -8)
for x in {2..8}
do
  echo "export PASSWORD${x}=$PASSWORD${x}" >> env_semi_normal_02.sh
done

# SEMI_NORMAL_SIGN_IN_IDを基準に、SIGN_IN_ID8まで作成する
SIGN_IN_ID=$(echo $SEMI_NORMAL_SIGN_IN_ID | cut -c -6)
for y in {2..8}
do
  echo "export SIGN_IN_ID${y}=$SIGN_IN_ID${y}" >> env_semi_normal_02.sh
done

# 作成した環境変数の設定
source ./env_semi_normal_02.sh

echo "----- semi_normal_sign_in_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# 管理者サインイン
ADMIN_SIGNIN_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者のサインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者のサインインに失敗しました"
  exit 1
fi

# SIGN_IN_ID2の口座（端末認証まで）
SIGN_IN_RES2=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
echo $SIGN_IN_RES2
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
DC_BANK_NUMBER2=$(echo $SIGN_IN_RES2 | jq -r ".dc_bank_number")
ACCOUNT_ID2=$(echo $SIGN_IN_RES2 | jq -r ".account_id")
if [ "$ACCESS_TOKEN2" == null ]; then
  echo "SIGN_IN_ID2の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN2 == null ]; then
  echo "SIGN_IN_ID2の口座のサインインに失敗しました"
fi

# SIGN_IN_ID3の口座（共通領域口座作成済、残高0）
SIGN_IN_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
echo $SIGN_IN_RES3
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
DC_BANK_NUMBER3=$(echo $SIGN_IN_RES3 | jq -r ".dc_bank_number")
ACCOUNT_ID3=$(echo $SIGN_IN_RES3 | jq -r ".account_id")
if [ $ACCESS_TOKEN3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi

# SIGN_IN_ID4の口座（共通領域口座作成済、残高あり）
SIGN_IN_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
echo $SIGN_IN_RES4
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")
ACCOUNT_ID4=$(echo $SIGN_IN_RES4 | jq -r ".account_id")
if [ "$ACCESS_TOKEN4" == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi


# SIGN_IN_ID5の口座（共通領域用口座作成済、enabled = false）
SIGN_IN_RES5=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\"}")
echo $SIGN_IN_RES5
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
DC_BANK_NUMBER5=$(echo $SIGN_IN_RES5 | jq -r ".dc_bank_number")
ACCOUNT_ID5=$(echo $SIGN_IN_RES5 | jq -r ".account_id")
if [ $ACCESS_TOKEN5 == null ]; then
  echo "SIGN_IN_ID5の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN5 == null ]; then
  echo "SIGN_IN_ID5の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER5 == null ]; then
  echo "SIGN_IN_ID5の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID5 == null ]; then
  echo "SIGN_IN_ID5の口座のサインインに失敗しました"
fi

# SIGN_IN_ID6の口座（共通領域用口座作成済、identified = false）
SIGN_IN_RES6=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID6\", \"password\":\"$PASSWORD6\"}")
echo $SIGN_IN_RES6
ACCESS_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".access_token")
ID_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".id_token")
DC_BANK_NUMBER6=$(echo $SIGN_IN_RES6 | jq -r ".dc_bank_number")
ACCOUNT_ID6=$(echo $SIGN_IN_RES6 | jq -r ".account_id")
if [ $ACCESS_TOKEN6 == null ]; then
  echo "SIGN_IN_ID6の口座のサインインに失敗しました"
fi

if [ $ID_TOKEN6 == null ]; then
  echo "SIGN_IN_ID6の口座のサインインに失敗しました"
fi

if [ $DC_BANK_NUMBER6 == null ]; then
  echo "SIGN_IN_ID6の口座のサインインに失敗しました"
fi

if [ $ACCOUNT_ID6 == null ]; then
  echo "SIGN_IN_ID6の口座のサインインに失敗しました"
fi

# サインインの結果を、env_semi_normal_02.shに出力する

echo "export ADMIN_ACCESS_TOKEN=$ADMIN_ACCESS_TOKEN" >> env_semi_normal_02.sh
echo "export ADMIN_ID_TOKEN=$ADMIN_ID_TOKEN" >> env_semi_normal_02.sh

echo "export ACCESS_TOKEN2=$ACCESS_TOKEN2" >> env_semi_normal_02.sh
echo "export ID_TOKEN2=$ID_TOKEN2" >> env_semi_normal_02.sh

echo "export ACCESS_TOKEN3=$ACCESS_TOKEN3" >> env_semi_normal_02.sh
echo "export ID_TOKEN3=$ID_TOKEN3" >> env_semi_normal_02.sh
echo "export DC_BANK_NUMBER3=$DC_BANK_NUMBER3" >> env_semi_normal_02.sh
echo "export ACCOUNT_ID3=$ACCOUNT_ID3" >> env_semi_normal_02.sh

echo "export ACCESS_TOKEN4=$ACCESS_TOKEN4" >> env_semi_normal_02.sh
echo "export ID_TOKEN4=$ID_TOKEN4" >> env_semi_normal_02.sh
echo "export DC_BANK_NUMBER4=$DC_BANK_NUMBER4" >> env_semi_normal_02.sh
echo "export ACCOUNT_ID4=$ACCOUNT_ID4" >> env_semi_normal_02.sh

echo "export ACCESS_TOKEN5=$ACCESS_TOKEN5" >> env_semi_normal_02.sh
echo "export ID_TOKEN5=$ID_TOKEN5" >> env_semi_normal_02.sh
echo "export DC_BANK_NUMBER5=$DC_BANK_NUMBER5" >> env_semi_normal_02.sh
echo "export ACCOUNT_ID5=$ACCOUNT_ID5" >> env_semi_normal_02.sh

echo "export ACCESS_TOKEN6=$ACCESS_TOKEN6" >> env_semi_normal_02.sh
echo "export ID_TOKEN6=$ID_TOKEN6" >> env_semi_normal_02.sh
echo "export DC_BANK_NUMBER6=$DC_BANK_NUMBER6" >> env_semi_normal_02.sh
echo "export ACCOUNT_ID6=$ACCOUNT_ID6" >> env_semi_normal_02.sh

echo "----- semi_normal_sign_in_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`