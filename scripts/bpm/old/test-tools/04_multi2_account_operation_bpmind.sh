#!/bin/bash
# 付加領域マルチモードで送金、返還、解約を行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 11 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$REGION_ID \
  \$SIGN_IN_ID4 \$PASSWORD4 \$NEW_PASSWORD4 \$SK_O4 \$INFO4 \
  \$SIGN_IN_ID5 \$PASSWORD5 \$SK_O5 \$INFO5 \$BUSINESS_ENTITY_ID2"
  echo ""
  exit 9
fi

REGION_ID=$1
SIGN_IN_ID4=$2
PASSWORD4=$3
NEW_PASSWORD4=$4
SK_O4=$5
INFO4=$6
SIGN_IN_ID5=$7
PASSWORD5=$8
SK_O5=$9
INFO5=${10}
BUSINESS_ENTITY_ID2=${11}

echo "----- 04_multi2_account_operation_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "サインイン受付"
SIGN_IN_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGN_IN_RES4 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
ACCOUNT_ID4=$(echo $SIGN_IN_RES4 | jq -r ".account_id")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")

if [ $ACCESS_TOKEN4 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN4 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

if [ $ACCOUNT_ID4 == null ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGN_IN_RES5

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
ACCOUNT_ID5=$(echo $SIGN_IN_RES5 | jq -r ".account_id")
DC_BANK_NUMBER5=$(echo $SIGN_IN_RES5 | jq -r ".dc_bank_number")

if [ $ACCESS_TOKEN2 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN5 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

if [ $ACCOUNT_ID5 == null ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "6-11 共通領域用口座／付加領域用口座取得"
USER_RES6_11=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $USER_RES6_11 | jq .
BALANCE6_11=$(echo $USER_RES6_11 | jq -r ".balance")
if [ "$BALANCE6_11" != "3000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-5 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

# 送金用アカウント署名作成
TRANSFER_SIGNATURE=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O4 $ACCOUNT_ID4 $ACCOUNT_ID4 $ACCOUNT_ID5 3000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$TRANSFER_SIGNATURE" ]; then
  echo "送金用アカウント署名作成に失敗しました"
  exit 1
fi

echo "12-2 共通領域コイン送金／付加領域コイン移転受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER5\", \"transfer_amount\":3000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO4\"}" | jq .
echo ""

echo "13-2 共通領域コイン送金／付加領域コイン移転"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER5\", \"transfer_amount\":3000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO4\"}" | jq .
echo ""

sleep 8

echo "6-12 共通領域用口座／付加領域用口座取得"
USER_RES6_12=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $USER_RES6_12 | jq .
BALANCE6_12=$(echo $USER_RES6_12 | jq -r ".balance")
if [ "$BALANCE6_12" != "0" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-6 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "6-13 共通領域用口座／付加領域用口座取得"
USER_RES6_13=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $USER_RES6_13 | jq .
BALANCE6_13=$(echo $USER_RES6_13 | jq -r ".balance")
if [ "$BALANCE6_13" != "3000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-7 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" | jq .
echo ""

# チャージ用アカウント署名作成
EXCHANGE_SIGNATURE=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O5 $ACCOUNT_ID5 $REGION_ID 3000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$EXCHANGE_SIGNATURE" ]; then
  echo "送金用アカウント署名作成に失敗しました"
  exit 1
fi

echo "15-2 共通領域コインと付加領域コインのチャージ／返還受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO5\"}" | jq .
echo ""

echo "16-2 共通領域コインと付加領域コインのチャージ／返還"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO5\"}" | jq .
echo ""

sleep 8

echo "6-14 共通領域用口座／付加領域用口座取得"
USER_RES6_14=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $USER_RES6_14 | jq .
BALANCE6_14=$(echo $USER_RES6_14 | jq -r ".balance")
if [ "$BALANCE6_14" != "0" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-8 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" | jq .
echo ""

echo "49-2 パスワード変更"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/password/change -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"access_token\":\"$ACCESS_TOKEN4\", \"password\":\"$PASSWORD4\", \"new_password\":\"$NEW_PASSWORD4\"}" | jq .
echo ""

echo "24-2 デジタル通貨口座解約受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "25-2 デジタル通貨口座解約"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "付加領域の手順7の実施に必要になるので、付加領域側で下記変数の設定をしてください"
echo ""
echo "export DC_BANK_NUMBER4=$DC_BANK_NUMBER4"
echo ""

echo "----- 04_multi2_account_operation_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`