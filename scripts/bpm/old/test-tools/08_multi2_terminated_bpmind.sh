#!/bin/bash
# 付加領域マルチモードで停止、解約を行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 5 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME2 \$ADMIN_NEW_PASSWORD2 \$SIGN_IN_ID6 \$PASSWORD6 \$BUSINESS_ENTITY_ID2"
  echo ""
  exit 9
fi

ADMIN_USER_NAME2=$1
ADMIN_NEW_PASSWORD2=$2
SIGN_IN_ID6=$3
PASSWORD6=$4
BUSINESS_ENTITY_ID2=$5

echo "----- 08_multi1_terminated_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "サインイン受付"
SIGN_IN_RES6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID6\", \"password\":\"$PASSWORD6\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGN_IN_RES6 | jq .

# サインイン受付のレスポンスを変数に設定
DC_BANK_NUMBER6=$(echo $SIGN_IN_RES6 | jq -r ".dc_bank_number")

if [ $DC_BANK_NUMBER6 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME2\", \"password\":\"$ADMIN_NEW_PASSWORD2\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $ADMIN_SIGNIN_RES | jq .

# 管理者サインインのレスポンスを変数に設定
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "20-3 ユーザー認証停止/停止解除"
AUTH_STOP_RES20_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER6/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}")
echo $AUTH_STOP_RES20_3 | jq .
STOP_CANCEL_NUMBER=$(echo $AUTH_STOP_RES20_3 | jq -r ".stop_cancel_number")
if [ $STOP_CANCEL_NUMBER == null ]; then
  echo "ユーザー認証停止/停止解除に失敗しました"
  exit 1
fi

sleep 4

echo "20-4 ユーザー認証停止/停止解除"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER6/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000, \"reason_detail\":\"auth restart\", \"stop_cancel_number\":\"$STOP_CANCEL_NUMBER\"}" | jq .
echo ""

sleep 4

echo "26-3 デジタル通貨口座解約受付（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER6/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "26-4 デジタル通貨口座解約（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER6/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

sleep 4

echo "7-2 共通領域用口座／付加領域用口座取得（管理画面用）"
USER_RES7_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER6 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json")
echo $USER_RES7_2 | jq .
TERMINATED7_2=$(echo $USER_RES7_2 | jq -r ".terminated")
if [ "$TERMINATED7_2" != "true" ]; then
  echo "共通領域用口座／付加領域用口座取得（管理画面用）に失敗しました"
  exit 1
fi

echo "----- 08_multi2_terminated_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`