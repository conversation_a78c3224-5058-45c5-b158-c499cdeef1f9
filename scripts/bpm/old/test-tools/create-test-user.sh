#!/bin/bash
# テスト用に共通領域用口座作成まで実施した口座を作成するシェル
# Developer Admin Team Roleを持っている人が、ローカルから実行する前提です

MODE_EXPLANTION="example [MODE=F1:Fin SignUp F2:Fin Device Check F3:Fin Create User I1:Ind SignUp I2:Ind Device Check]"

# 引数チェック
if [ $# -ne 4 ]; then
    echo "[引数なし] ./create-test-user.sh \$MODE \$USER_NAME \$SIGN_IN_ID \$PASSWORD"
    echo $MODE_EXPLANTION
    echo "        [USER_NAME=+819012348765] [SIGN_IN_ID=DCP9001] [PASSWORD=Passw0rd]"
    exit 9
fi
MODE=$1
USER_NAME=$2
SIGN_IN_ID=$3
PASSWORD=$4

# 環境変数
if [ "$MODE" = "F1" ] || [ "$MODE" = "F2" ] || [ "$MODE" = "F3" ]; then
    source ./env-bpmfin.sh
fi
if [ "$MODE" = "I1" ] || [ "$MODE" = "I2" ]; then
    source ./env-bpmind.sh
fi

if [ -z "$BASE_URL" ]; then
    echo "動作モードが不正です"
    echo $MODE_EXPLANTION
    exit 9
fi

echo "----- create-test-user start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# ポートフォワードの接続確認と、BPM Serverが起動出来ていることの確認で、サインイン受付から"Sign-in failed"が返されることを確認
SIGN_IN_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -s -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"1\", \"password\":\"1\"}")
RESPONCE_DETAIL=$(echo $SIGN_IN_RES | jq -r ".detail")
if [ "Sign-in failed" != "$RESPONCE_DETAIL" ]; then
    echo "httpsの接続に失敗しました"
    exit 9
fi

# USER_NAME と SIGN_IN_ID の存在チェック
SQL_RESPONCE=$(psql -h $DB -p $LOCAL_DB_PORT -U $DB_USER -d $DB_NAME -t -c "select count(*) from account_info where international_phone_number='$USER_NAME'")
if [ $SQL_RESPONCE -ne 0 ]; then
    echo "登録済のUSER_NAMEです"
    exit 9
fi

SQL_RESPONCE=$(psql -h $DB -p $LOCAL_DB_PORT -U $DB_USER -d $DB_NAME -t -c "select count(*) from account_info where sign_in_id='$SIGN_IN_ID'")
if [ $SQL_RESPONCE -ne 0 ]; then
    echo "登録済のSIGN_IN_IDです"
    exit 9
fi

# Cognitoにユーザー作成
AWS_RESPONCE=$(aws cognito-idp admin-create-user --user-pool-id $USER_POOL_ID --username $USER_NAME --user-attributes Name=phone_number,Value=$USER_NAME --message-action SUPPRESS)
if [ $(echo $AWS_RESPONCE | grep -c "FORCE_CHANGE_PASSWORD") != 1 ]; then
    echo "Cognitoのユーザー作成に失敗しました"
    exit 9
fi

if [ "${MODE:1:1}" = "1" ]; then
    echo "サインアップまで完了"
    echo "----- create-test-user end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
    exit
fi

# Cognitoのユーザーパスワード変更
aws cognito-idp admin-set-user-password --user-pool-id $USER_POOL_ID --username $USER_NAME --password $PASSWORD --permanent

# Cognitoのユーザーパスワード変更後のステータス確認
AWS_RESPONCE=$(aws cognito-idp admin-get-user --username $USER_NAME --user-pool-id $USER_POOL_ID)
if [ $(echo $AWS_RESPONCE | grep -c "CONFIRMED") != 1 ]; then
    echo "Cognitoのユーザーパスワード変更に失敗しました"
    exit 9
fi

# RDSのaccount_infoにinsert
SQL_RESPONCE=$(psql -h $DB -p $LOCAL_DB_PORT -U $DB_USER -d $DB_NAME -t -c "insert into account_info (sign_in_id, sub, international_phone_number, business_entity_id) values ('$SIGN_IN_ID','dummy','$USER_NAME','single')")

if [ "${MODE:1:1}" = "2" ]; then
    echo "端末認証まで完了"
    echo "----- create-test-user end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
    exit
fi

# サインイン
SIGN_IN_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -s -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID\", \"password\":\"$PASSWORD\"}")
ID_TOKEN=$(echo $SIGN_IN_RES | jq -r ".id_token")
if [ $ID_TOKEN = null ]; then
    echo "サインインに失敗しました"
    exit 9
fi

# 共通領域用口座作成
USER_RES=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -s -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}")
sleep 1
DC_BANK_NUMBER=$(echo $USER_RES | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER = null ]; then
    echo "共通領域用口座作成に失敗しました"
    exit 9
fi

echo "共通領域用口座開設まで完了"
echo "DC_BANK_NUMBER=$DC_BANK_NUMBER"

echo "----- create-test-user end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
