#!/bin/bash
# 付加領域マルチモードで送金、返還、解約を行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 11 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$REGION_ID \
  \$SIGN_IN_ID1 \$PASSWORD1 \$NEW_PASSWORD1 \$SK_O1 \$INFO1 \
  \$SIGN_IN_ID2 \$PASSWORD2 \$SK_O2 \$INFO2 \$BUSINESS_ENTITY_ID1"
  echo ""
  exit 9
fi

REGION_ID=$1
SIGN_IN_ID1=$2
PASSWORD1=$3
NEW_PASSWORD1=$4
SK_O1=$5
INFO1=$6
SIGN_IN_ID2=$7
PASSWORD2=$8
SK_O2=$9
INFO2=${10}
BUSINESS_ENTITY_ID1=${11}

echo "----- 04_multi1_account_operation_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}")
echo $SIGN_IN_RES1 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")
ACCOUNT_ID1=$(echo $SIGN_IN_RES1 | jq -r ".account_id")
DC_BANK_NUMBER1=$(echo $SIGN_IN_RES1 | jq -r ".dc_bank_number")

if [ $ACCESS_TOKEN1 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN1 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

if [ $ACCOUNT_ID1 == null ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}")
echo $SIGN_IN_RES2

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
ACCOUNT_ID2=$(echo $SIGN_IN_RES2 | jq -r ".account_id")
DC_BANK_NUMBER2=$(echo $SIGN_IN_RES2 | jq -r ".dc_bank_number")

if [ $ACCESS_TOKEN2 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN2 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

if [ $ACCOUNT_ID2 == null ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "6-7 共通領域用口座／付加領域用口座取得"
USER_RES6_7=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USER_RES6_7 | jq .
BALANCE6_7=$(echo $USER_RES6_7 | jq -r ".balance")
if [ "$BALANCE6_7" != "3000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-1 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

# 送金用アカウント署名作成
TRANSFER_SIGNATURE=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O1 $ACCOUNT_ID1 $ACCOUNT_ID1 $ACCOUNT_ID2 3000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$TRANSFER_SIGNATURE" ]; then
  echo "送金用アカウント署名作成に失敗しました"
  exit 1
fi

echo "12-1 共通領域コイン送金／付加領域コイン移転受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN1" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER2\", \"transfer_amount\":3000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO1\"}" | jq .
echo ""

echo "13-1 共通領域コイン送金／付加領域コイン移転"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN1" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER2\", \"transfer_amount\":3000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO1\"}" | jq .
echo ""

sleep 8

echo "6-8 共通領域用口座／付加領域用口座取得"
USER_RES6_8=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USER_RES6_8 | jq .
BALANCE6_8=$(echo $USER_RES6_8 | jq -r ".balance")
if [ "$BALANCE6_8" != "0" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-2 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "6-5 共通領域用口座／付加領域用口座取得"
USER_RES6_9=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $USER_RES6_9 | jq .
BALANCE6_9=$(echo $USER_RES6_9 | jq -r ".balance")
if [ "$BALANCE6_9" != "3000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-3 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" | jq .
echo ""

# チャージ用アカウント署名作成
EXCHANGE_SIGNATURE=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O2 $ACCOUNT_ID2 $REGION_ID 3000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$EXCHANGE_SIGNATURE" ]; then
  echo "送金用アカウント署名作成に失敗しました"
  exit 1
fi

echo "15-1 共通領域コインと付加領域コインのチャージ／返還受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO2\"}" | jq .
echo ""

echo "16-1 共通領域コインと付加領域コインのチャージ／返還"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO2\"}" | jq .
echo ""

sleep 8

echo "6-10 共通領域用口座／付加領域用口座取得"
USER_RES6_10=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $USER_RES6_10 | jq .
BALANCE6_10=$(echo $USER_RES6_10 | jq -r ".balance")
if [ "$BALANCE6_10" != "0" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "18-4 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" | jq .
echo ""

echo "49-1 パスワード変更"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/password/change -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"access_token\":\"$ACCESS_TOKEN1\", \"password\":\"$PASSWORD1\", \"new_password\":\"$NEW_PASSWORD1\"}" | jq .
echo ""

echo "24-1 デジタル通貨口座解約受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "25-1 デジタル通貨口座解約"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "付加領域の手順7の実施に必要になるので、実行前にターミナルで以下のコマンドを実行してください"
echo ""
echo "export DC_BANK_NUMBER1=$DC_BANK_NUMBER1"
echo ""

echo "----- 04_multi1_account_operation_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`