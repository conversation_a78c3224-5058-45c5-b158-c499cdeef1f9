#!/bin/bash
# 共通領域の準正常系テストを行うシェル

# 環境変数の設定
source ./env-bpmfin.sh
source ./env_semi_normal_01.sh

DIR=$(dirname "$0")
cd $DIR

LOG_FILE=semi_normal_result_bpmfin.txt
cp /dev/null $LOG_FILE
exec 1>$LOG_FILE

echo "----- semi_normal_test_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"\", \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"0901122334\", \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"abcdefghijk\", \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER3\", \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"password\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"password\":\"Passwo1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"password\":\"Password\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"password\":\"PASSWORD1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signup -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"password\":\"password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"auth_code\":\"123456\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"\", \"auth_code\":\"123456\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"0901122334\", \"auth_code\":\"123456\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"abcdefghijk\", \"auth_code\":\"123456\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223399\", \"auth_code\":\"123456\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER1\", \"auth_code\":\"123456\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"auth_code\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"auth_code\":\"12345\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"09011223344\", \"auth_code\":\"abcdef\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/device/check -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER1\", \"auth_code\":\"$AUTH_CODE1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"password\":\"$PASSWORD2\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"\", \"password\":\"$PASSWORD2\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"Passwo1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"Password\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"PASSWORD1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"DCPDUMMY\", \"password\":\"$PASSWORD2\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"DummyPass0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"005\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"abcd\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"1-7\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"abc-abcdefg\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"**********\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"001-1234567\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"101-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"102-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"103-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"104-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"105-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"106-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"107-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"151-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"152-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"153-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"154-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"155-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"156-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"005\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"abcd\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"1-7\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"abc-abcdefg\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"**********\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"001-1234567\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"001-1234567\", \"bank_account_name\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"101-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"102-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"103-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"104-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"105-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"106-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"107-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"151-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"152-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"153-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"154-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"155-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0005\", \"bank_account_number\":\"156-1234567\", \"bank_account_name\":\"XXXXXXXX\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER3 -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/DCPDUMMY -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/balances -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER3/balances -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/DC9000009/balances -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER3 -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/users/DC9000009 -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/user/security -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Content-Type: application/json" -d "{\"mint_amount\":10000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"mint_amount\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"mint_amount\":0}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"mint_amount\":*************}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"mint_amount\":10000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"mint_amount\":10000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":0, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":*************, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"DCPDUMMY\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"0\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":0, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":*************, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"DCPDUMMY\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN3" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER4\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"0\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER3\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE4\", \"info\":\"0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":2999, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":4000, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":0, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":*************, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"0\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":2999, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":4000, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":0, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":*************, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"0\", \"info\":\"$INFO4\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE4\", \"info\":\"0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Content-Type: application/json" -d "{\"burn_amount\":4000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"burn_amount\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"burn_amount\":0}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"burn_amount\":*************}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"burn_amount\":4000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/transactions -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/transactions?offset=************* -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/transactions?limit=0 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/transactions?limit=101 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/transactions?region_id=2999 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/transactions?region_id=4000 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions?offset=************* -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions?limit=0 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions?limit=101 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions?region_id=2999 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions?region_id=4000 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/management/user/DCPDUMMY/transactions -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":30000, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":9999, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":100000, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":99999, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/DCPDUMMY/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER8/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000, \"reason_detail\":\"auth restart\", \"stop_cancel_number\":\"12345\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER8/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":9999, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":100000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/DCPDUMMY/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":99999, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER5/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER7/frozen/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":9999, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":100000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/DCPDUMMY/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":99999, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER5/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER4/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER7/frozen -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":80000, \"reason_detail\":\"frozen\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":9999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":100000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":99999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":9999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":100000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"reason_code\":99999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":9999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":100000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":99999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/DCPDUMMY/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER5/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER6/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER7/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER4/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":9999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":100000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":99999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/DCPDUMMY/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER5/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER6/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER7/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER4/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"region_id\":}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"region_id\":2999}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"region_id\":4000}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history?offset=************* -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history?limit=0 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history?limit=101 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history?from_date=a -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history?to_date=a -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X GET $BASE_URL/token/history?from_date=2022-01-01T00:00:00\&to_date=2021-12-01T00:00:00 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -d "{}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -d "{\"user_name\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -d "{\"user_name\":\"semiNormalTest1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{ \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"\", \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"semiNormalDummy\", \"password\":\"Password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"DummyPass0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"\", \"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"Passwo1\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"Password\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"PASSWORD1\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"password1\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"$ADMIN_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"semiNormalDummy\", \"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"$ADMIN_SESSION\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"aaa\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"Passwo1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"Password\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"PASSWORD1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"password1\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"aaa\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"DummyPass0\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X DELETE $BASE_URL/management/user/09011223399 -H "Content-Type: application/json" -w '%{http_code}\n'

echo "----- semi_normal_test_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`