#!/bin/bash
# テスト用に共通領域用口座作成を行うシェル

echo "対象の環境にポートフォワード済、対象の環境のProfileに変更していることを確認し、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit
fi

# 環境変数の設定
source ./env-bpmfin.sh

echo "----- create-account-bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# 1つ目の口座を作成
# create-test-userを呼び出して、端末認証まで済んでいる口座を作成
./create-test-user.sh "F2" "$PHONE_NUMBER1" "$SIGN_IN_ID1" "$PASSWORD1"

# 2つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成
./create-test-user.sh "F3" "$PHONE_NUMBER2" "$SIGN_IN_ID2" "$PASSWORD2"

# 3つ目の口座を作成
# create-test-userを呼び出して、共通領域用口座作成まで済んでいる口座を作成
./create-test-user.sh "F3" "$PHONE_NUMBER3" "$SIGN_IN_ID3" "$PASSWORD3"

echo "----- create-account-bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`