#!/bin/bash
# 共通領域シングルモードで付加領域用口座作成〜チャージを行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 7 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$REGION_ID \$SIGN_IN_ID4 \$PASSWORD4 \$SIGN_IN_ID5 \$PASSWORD5 \$SIGN_IN_ID6 \$PASSWORD6"
  echo ""
  exit 9
fi

REGION_ID=$1
SIGN_IN_ID4=$2
PASSWORD4=$3
SIGN_IN_ID5=$4
PASSWORD5=$5
SIGN_IN_ID6=$6
PASSWORD6=$7

echo "----- 03_single2_exchange_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "サインイン受付"
SIGN_IN_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
echo "$SIGN_IN_RES4" | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")
ACCOUNT_ID4=$(echo $SIGN_IN_RES4 | jq -r ".account_id")
if [ $ID_TOKEN4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID4 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "署名鍵取得"
SECURITY_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo "$SECURITY_RES4" | jq .
SK_O4=$(echo $SECURITY_RES4 | jq -r ".sk_o")
INFO4=$(echo $SECURITY_RES4 | jq -r ".info")
if [ $SK_O4 == null ]; then
  echo "署名鍵取得(SK_O4)に失敗しました"
  exit 1
fi
if [ $INFO4 == null ]; then
  echo "署名鍵取得(INFO4)に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\"}")
echo $SIGN_IN_RES5 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
DC_BANK_NUMBER5=$(echo $SIGN_IN_RES5 | jq -r ".dc_bank_number")
ACCOUNT_ID5=$(echo $SIGN_IN_RES5 | jq -r ".account_id")
if [ $ID_TOKEN5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID5 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID6\", \"password\":\"$PASSWORD6\"}")
echo $SIGN_IN_RES6 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".access_token")
ID_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".id_token")
if [ $ID_TOKEN6 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "8-18 残高取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "44-4 付加領域用口座作成"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" | jq .
echo ""

sleep 8

echo "8-19 残高取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "44-5 付加領域用口座作成"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" | jq .
echo ""

sleep 8

echo "44-6 付加領域用口座作成"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" | jq .
echo ""

sleep 8

# チャージ用アカウント署名作成
EXCHANGE_SIGNATURE=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O4 $ACCOUNT_ID4 $REGION_ID 3000 | grep "account signature:" | sed -r 's/^account signature: //')

echo "15-2 共通領域コインと付加領域コインのチャージ／返還受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO4\"}" | jq .
echo ""

echo "16-2 共通領域コインと付加領域コインのチャージ／返還"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO4\"}" | jq .
echo ""

sleep 8

echo "6-16 共通領域用口座／付加領域用口座取得"
USER_RES6_16=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $USER_RES6_16 | jq .
BALANCE6_16=$(echo $USER_RES6_16 | jq -r ".balance")
if [ "$BALANCE6_16" != "4000" ]; then
  echo "残高が異なります"
  exit 1
fi

echo "8-20 残高取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "18-12 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" | jq .
echo ""

echo "----- 03_single2_exchange_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`