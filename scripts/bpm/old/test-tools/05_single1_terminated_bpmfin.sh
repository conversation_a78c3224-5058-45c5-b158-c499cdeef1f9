#!/bin/bash
# 共通領域シングルモードで解約を行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 7 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_PASSWORD \$SIGN_IN_ID1 \$PASSWORD1 \$NEW_PASSWORD1 \$SIGN_IN_ID2 \$PASSWORD2"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_PASSWORD=$2
SIGN_IN_ID1=$3
PASSWORD1=$4
NEW_PASSWORD1=$5
SIGN_IN_ID2=$6
PASSWORD2=$7

echo "----- 05_single1_terminated_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .
echo ""

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\"}")
echo $SIGN_IN_RES1 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")
DC_BANK_NUMBER1=$(echo $SIGN_IN_RES1 | jq -r ".dc_bank_number")

if [ $ACCESS_TOKEN1 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN1 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
echo $SIGN_IN_RES2 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
DC_BANK_NUMBER2=$(echo $SIGN_IN_RES2 | jq -r ".dc_bank_number")

if [ $ID_TOKEN2 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "49-1 パスワード変更"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/password/change -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"access_token\":\"$ACCESS_TOKEN1\", \"password\":\"$PASSWORD1\", \"new_password\":\"$NEW_PASSWORD1\"}" | jq .
echo ""

echo "8-9 残高取得"
BALANCES_RES8_9=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $BALANCES_RES8_9 | jq .
TOTAL_BALANCE8_9=$(echo $BALANCES_RES8_9 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_9" != "4000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "6-7 共通領域用口座／付加領域用口座取得"
USER_RES6_7=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $USER_RES6_7 | jq .
BALANCE6_7=$(echo $USER_RES6_7 | jq -r ".balance")
if [ "$BALANCE6_7" !=  "5000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-10 残高取得"
BALANCES_RES8_10=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $BALANCES_RES8_10 | jq .
TOTAL_BALANCE8_10=$(echo $BALANCES_RES8_10 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_10" != "5000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-7 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" | jq .
echo ""

echo "17-2 共通領域コイン償却"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"burn_amount\":4000}" | jq .
echo ""

sleep 8

echo "17-3 共通領域コイン償却"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"burn_amount\":5000}" | jq .
echo ""

# 承認モードが有効な場合に限り実施
if [ "$TOKEN_FLOW_MODE" == "approval" ]; then
  echo "53-3 承認待ち処理一覧取得"
  ORDER_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET "$BASE_URL/management/orders?offset=0&limit=2&order_status=pending" -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json");
  echo "$ORDER_RES3" | jq .
  echo ""

  ORDER_ID3=$(echo $ORDER_RES3 | jq -r ".order_items[1].order_id")
  if [ $ORDER_ID3 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER3=$(echo $ORDER_RES3 | jq -r ".order_items[1].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER3" != "$DC_BANK_NUMBER1" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER1)と異なります"
    exit 1
  fi

  echo "54-3 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID3/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":4000}' | jq .
  echo ""

  sleep 8

  ORDER_ID4=$(echo $ORDER_RES3 | jq -r ".order_items[0].order_id")
  if [ $ORDER_ID3 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER4=$(echo $ORDER_RES3 | jq -r ".order_items[0].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER4" != "$DC_BANK_NUMBER2" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER2)と異なります"
    exit 1
  fi

  echo "54-4 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID4/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":5000}' | jq .
  echo ""
fi

sleep 8

echo "24-1 デジタル通貨口座解約受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "25-1 デジタル通貨口座解約"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "共通領域の手順6および9の実施に必要になるので、以降のシェルを実行前に以下のコマンドを実行してください"
echo ""
echo "export DC_BANK_NUMBER1=$DC_BANK_NUMBER1"
echo "export DC_BANK_NUMBER2=$DC_BANK_NUMBER2"
echo ""

echo "----- 05_single1_terminated_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`