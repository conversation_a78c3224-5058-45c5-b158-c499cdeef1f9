#!/bin/bash
# 共通領域シングルモードで管理者作成〜照会を行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 4 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_PASSWORD \$ADMIN_NEW_PASSWORD \$DC_BANK_NUMBER1"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_PASSWORD=$2
ADMIN_NEW_PASSWORD=$3
DC_BANK_NUMBER1=$4

echo "----- 06_single1_admin_operation_bpmfin.sh start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .
echo ""

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "49-3 パスワード変更"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD\", \"new_password\":\"$ADMIN_NEW_PASSWORD\"}" | jq .
echo ""

echo "7-1 共通領域用口座／付加領域用口座取得（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER1 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" | jq .
echo ""

echo "9-1 残高取得（管理画面用）"
BALANCE_RES9_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER1/balances -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json")
echo $BALANCE_RES9_1 | jq .
TOTAL_BALANCE9_1=$(echo $BALANCE_RES9_1 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE9_1" != "0" ]; then
  echo "残高取得（管理画面用）に失敗しました"
fi

echo "19-1 デジタル通貨取引一覧照会（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER1/transactions?offset=0\&limit=5\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" | jq .
echo ""

echo "----- 06_single1_admin_operation_bpmfin.sh end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`