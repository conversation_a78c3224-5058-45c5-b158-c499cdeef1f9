#!/bin/bash
# 付加領域の準正常系アカウントにサインインを行うシェル

# 環境変数の設定
source ./env-bpmind.sh

cp /dev/null env_semi_normal_04.sh
echo "#!/bin/bash" >> env_semi_normal_04.sh

# SEMI_NORMAL_PASSWORDを基準に、PASSWORD3とPASSWORD4を作成する
PASSWORD=$(echo $SEMI_NORMAL_PASSWORD | cut -c -8)
for x in {3..4}
do
  echo "export PASSWORD${x}=$PASSWORD${x}" >> env_semi_normal_04.sh
done

# SEMI_NORMAL_SIGN_IN_IDを基準に、SIGN_IN_ID3とSIGN_IN_ID4を作成する
SIGN_IN_ID=$(echo $SEMI_NORMAL_SIGN_IN_ID | cut -c -6)
for y in {3..4}
do
  echo "export SIGN_IN_ID${y}=$SIGN_IN_ID${y}" >> env_semi_normal_04.sh
done

# 作成した環境変数の設定
source ./env_semi_normal_04.sh

echo "----- semi_normal_sign_in_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

# SIGN_IN_ID3の口座（共通領域口座作成済、残高0）
SIGN_IN_RES3=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
echo $SIGN_IN_RES3
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
DC_BANK_NUMBER3=$(echo $SIGN_IN_RES3 | jq -r ".dc_bank_number")
ACCOUNT_ID3=$(echo $SIGN_IN_RES3 | jq -r ".account_id")
if [ $ACCESS_TOKEN3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi 

if [ $ID_TOKEN3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi 

if [ $DC_BANK_NUMBER3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi 

if [ $ACCOUNT_ID3 == null ]; then
  echo "SIGN_IN_ID3の口座のサインインに失敗しました"
fi 

# SIGN_IN_ID4の口座（共通領域口座作成済、残高あり）
SIGN_IN_RES4=$(curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
echo $SIGN_IN_RES4
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")
ACCOUNT_ID4=$(echo $SIGN_IN_RES4 | jq -r ".account_id")
if [ $ACCESS_TOKEN4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi 

if [ $ID_TOKEN4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi 

if [ $DC_BANK_NUMBER4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi 

if [ $ACCOUNT_ID4 == null ]; then
  echo "SIGN_IN_ID4の口座のサインインに失敗しました"
fi 

# 口座作成の結果を、env_semi_normal_04.shに出力する
echo "export ACCESS_TOKEN3=$ACCESS_TOKEN3" >> env_semi_normal_04.sh
echo "export ID_TOKEN3=$ID_TOKEN3" >> env_semi_normal_04.sh
echo "export DC_BANK_NUMBER3=$DC_BANK_NUMBER3" >> env_semi_normal_04.sh
echo "export ACCOUNT_ID3=$ACCOUNT_ID3" >> env_semi_normal_04.sh

echo "export ACCESS_TOKEN4=$ACCESS_TOKEN4" >> env_semi_normal_04.sh
echo "export ID_TOKEN4=$ID_TOKEN4" >> env_semi_normal_04.sh
echo "export DC_BANK_NUMBER4=$DC_BANK_NUMBER4" >> env_semi_normal_04.sh
echo "export ACCOUNT_ID4=$ACCOUNT_ID4" >> env_semi_normal_04.sh

echo "----- semi_normal_sign_in_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`