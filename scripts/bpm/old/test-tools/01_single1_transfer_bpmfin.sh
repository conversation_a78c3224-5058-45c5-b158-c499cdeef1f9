#!/bin/bash
# 共通領域シングルモードでサインイン〜送金を行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 8 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_PASSWORD \$SIGN_IN_ID1 \$PASSWORD1 \$PHONE_NUMBER2 \$PASSWORD2 \$PHONE_NUMBER3 \$PASSWORD3"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_PASSWORD=$2
SIGN_IN_ID1=$3
PASSWORD1=$4
PHONE_NUMBER2=$5
PASSWORD2=$6
PHONE_NUMBER3=$7
PASSWORD3=$8

echo "----- 01_single1_transfer_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "47-2 管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .
echo ""

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "3-1 サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\"}")
echo $SIGN_IN_RES1 | jq .
if [ -z "$SIGN_IN_RES1" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")
if [ $ACCESS_TOKEN1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ID_TOKEN1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "4-1 共通領域用口座開設受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234501\", \"bank_account_name\":\"accountname1\"}" | jq .
echo ""

echo "5-1 共通領域用口座作成"
USER_RES5_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234501\", \"bank_account_name\":\"accountname1\"}")
echo "$USER_RES5_1" | jq .
DC_BANK_NUMBER1=$(echo $USER_RES5_1 | jq -r ".dc_bank_number")
ACCOUNT_ID5_1=$(echo $USER_RES5_1 | jq -r ".account_id")
if [ $DC_BANK_NUMBER1 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID5_1 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi

sleep 10

echo "6-1 共通領域用口座／付加領域用口座取得"
USER_RES6_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$USER_RES6_1" | jq .
DC_BANK_NUMBER6_1=$(echo $USER_RES6_1 | jq -r ".dc_bank_number")
if [ "$DC_BANK_NUMBER1" != "$DC_BANK_NUMBER6_1" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi
SIGN_IN_ID6_1=$(echo $USER_RES6_1 | jq -r ".sign_in_id")
if [ "$SIGN_IN_ID1" != "$SIGN_IN_ID6_1" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "59-1 端末認証なしサインアップ"
SIGN_IN_ID2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER2\", \"password\":\"$PASSWORD2\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID2 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-2 サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
echo $SIGN_IN_RES2 | jq .
if [ -z "$SIGN_IN_RES2" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
if [ $ACCESS_TOKEN2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ID_TOKEN2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "4-2 共通領域用口座開設受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234502\", \"bank_account_name\":\"accountname2\"}" | jq .
echo ""

echo "5-2 共通領域用口座作成"
USER_RES5_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234502\", \"bank_account_name\":\"accountname2\"}")
echo "$USER_RES5_2" | jq .
DC_BANK_NUMBER2=$(echo $USER_RES5_2 | jq -r ".dc_bank_number")
ACCOUNT_ID5_2=$(echo $USER_RES5_2 | jq -r ".account_id")
if [ $DC_BANK_NUMBER2 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID5_2 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi

sleep 10

echo "6-2 共通領域用口座／付加領域用口座取得"
USER_RES6_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo "$USER_RES6_2" | jq .
DC_BANK_NUMBER6_2=$(echo $USER_RES6_2 | jq -r ".dc_bank_number")
if [ "$DC_BANK_NUMBER2" != "$DC_BANK_NUMBER6_2" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "59-2 端末認証なしサインアップ"
SIGN_IN_ID3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER3\", \"password\":\"$PASSWORD3\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID3 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-3 サインイン受付"
SIGN_IN_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
echo $SIGN_IN_RES3 | jq .
if [ -z "$SIGN_IN_RES3" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
if [ $ACCESS_TOKEN3 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ID_TOKEN3 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "4-3 共通領域用口座開設受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234503\", \"bank_account_name\":\"accountname3\"}" | jq .
echo ""

echo "5-3 共通領域用口座作成"
USER_RES5_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"bank_code\":\"0000\", \"bank_account_number\":\"001-1234503\", \"bank_account_name\":\"accountname3\"}")
echo "$USER_RES5_3" | jq .
DC_BANK_NUMBER3=$(echo $USER_RES5_3 | jq -r ".dc_bank_number")
ACCOUNT_ID3=$(echo $USER_RES5_3 | jq -r ".account_id")
if [ $DC_BANK_NUMBER3 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID3 == null ]; then
  echo "共通領域用口座作成に失敗しました"
  exit 1
fi

sleep 10

echo "6-3 共通領域用口座／付加領域用口座取得"
USER_RES6_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json")
echo "$USER_RES6_3" | jq .
DC_BANK_NUMBER6_3=$(echo $USER_RES6_3 | jq -r ".dc_bank_number")
if [ "$DC_BANK_NUMBER3" != "$DC_BANK_NUMBER6_3" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-1 残高取得"
BALANCES_RES8_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$BALANCES_RES8_1" | jq .
TOTAL_BALANCE8_1=$(echo $BALANCES_RES8_1 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_1" != "0" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "8-2 残高取得"
BALANCES_RES8_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo "$BALANCES_RES8_2" | jq .
TOTAL_BALANCE8_2=$(echo $BALANCES_RES8_2 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_2" != "0" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "8-3 残高取得"
BALANCES_RES8_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json")
echo "$BALANCES_RES8_3" | jq .
TOTAL_BALANCE8_3=$(echo $BALANCES_RES8_3 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_3" != "0" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-1 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "45-1 共通領域コイン発行／償却状況照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/token/history -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "51-1 アカウントID取得"
USERS_RES51_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER1 -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USERS_RES51_1 | jq .
ACCOUNT_ID51_1=$(echo $USERS_RES51_1 | jq -r ".account_id")
if [ "$ACCOUNT_ID5_1" != "$ACCOUNT_ID51_1" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "10-1 署名鍵取得"
SECURITY_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$SECURITY_RES1" | jq .
SK_O1=$(echo $SECURITY_RES1 | jq -r ".sk_o")
INFO1=$(echo $SECURITY_RES1 | jq -r ".info")
if [ $SK_O1 == null ]; then
  echo "署名鍵取得(SK_O1)に失敗しました"
  exit 1
fi
if [ $INFO1 == null ]; then
  echo "署名鍵取得(INFO1)に失敗しました"
  exit 1
fi

echo "11-1 共通領域コイン発行"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/mint -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"mint_amount\":10000}" | jq .
echo ""

# 承認モードが有効な場合に限り実施
if [ "$TOKEN_FLOW_MODE" == "approval" ]; then
  echo "53-1 承認待ち処理一覧取得"
  ORDER_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET "$BASE_URL/management/orders?offset=0&limit=1&order_status=pending" -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json");
  echo "$ORDER_RES1" | jq .
  echo ""

  ORDER_ID1=$(echo $ORDER_RES1 | jq -r ".order_items[0].order_id")
  if [ $ORDER_ID1 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER1=$(echo $ORDER_RES1 | jq -r ".order_items[0].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER1" != "$DC_BANK_NUMBER1" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER1)と異なります"
    exit 1
  fi

  echo "54-1 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID1/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":10000}' | jq .
  echo ""
fi

sleep 8

echo "6-4 共通領域用口座／付加領域用口座取得"
USER_RES6_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$USER_RES6_4" | jq .
BALANCE6_4=$(echo $USER_RES6_4 | jq -r ".balance")
if [ "$BALANCE6_4" != "10000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-4 残高取得"
BALANCES_RES8_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$BALANCES_RES8_4" | jq .
TOTAL_BALANCE8_4=$(echo $BALANCES_RES8_4 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_4" != "10000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-2 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "45-2 共通領域コイン発行／償却状況照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/token/history -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "17-1 共通領域コイン償却"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"burn_amount\":1000}" | jq .
echo ""

# 承認モードが有効な場合に限り実施
if [ "$TOKEN_FLOW_MODE" == "approval" ]; then
  echo "53-2 承認待ち処理一覧取得"
  ORDER_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET "$BASE_URL/management/orders?offset=0&limit=1&order_status=pending" -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json");
  echo "$ORDER_RES2" | jq .
  echo ""

  ORDER_ID2=$(echo $ORDER_RES2 | jq -r ".order_items[0].order_id")
  if [ $ORDER_ID2 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER2=$(echo $ORDER_RES2 | jq -r ".order_items[0].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER2" != "$DC_BANK_NUMBER1" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER1)と異なります"
    exit 1
  fi

  echo "54-2 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID2/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":1000}' | jq .
  echo ""
fi

sleep 8

echo "6-5 共通領域用口座／付加領域用口座取得"
USER_RES6_5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$USER_RES6_5" | jq .
BALANCE6_5=$(echo $USER_RES6_5 | jq -r ".balance")
if [ "$BALANCE6_5" != "9000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-5 残高取得"
BALANCES_RES8_5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$BALANCES_RES8_5" | jq .
TOTAL_BALANCE8_5=$(echo $BALANCES_RES8_5 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_5" != "9000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-3 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions?offset=0\&limit=5\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "45-3 共通領域コイン発行／償却状況照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/token/history -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "10-2 署名鍵取得"
SECURITY_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json")
echo $SECURITY_RES3 | jq .

# 署名鍵を変数に設定
SK_O3=$(echo $SECURITY_RES3 | jq -r ".sk_o")
INFO3=$(echo $SECURITY_RES3 | jq -r ".info")
if [ $SK_O3 == null ]; then
  echo "署名鍵取得に失敗しました"
  exit 1
fi

if [ $INFO3 == null ]; then
  echo "署名鍵取得に失敗しました"
  exit 1
fi

echo "3-4 サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\"}")
echo $SIGN_IN_RES1 | jq .
if [ -z "$SIGN_IN_RES1" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")
DC_BANK_NUMBER1=$(echo $SIGN_IN_RES1 | jq -r ".dc_bank_number")
ACCOUNT_ID1=$(echo $SIGN_IN_RES1 | jq -r ".account_id")
if [ $ID_TOKEN1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "3-5 サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
echo $SIGN_IN_RES2 | jq .
if [ -z "$SIGN_IN_RES2" ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
DC_BANK_NUMBER2=$(echo $SIGN_IN_RES2 | jq -r ".dc_bank_number")
ACCOUNT_ID2=$(echo $SIGN_IN_RES2 | jq -r ".account_id")
if [ $ID_TOKEN2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "51-2 アカウントID取得"
USERS_RES51_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER2 -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USERS_RES51_2 | jq .
ACCOUNT_ID51_2=$(echo $USERS_RES51_2 | jq -r ".account_id")
if [ "$ACCOUNT_ID2" != "$ACCOUNT_ID51_2" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "10-3 署名鍵取得"
SECURITY_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo "$SECURITY_RES2" | jq .
SK_O2=$(echo $SECURITY_RES2 | jq -r ".sk_o")
INFO2=$(echo $SECURITY_RES2 | jq -r ".info")
if [ $SK_O2 == null ]; then
  echo "署名鍵取得(SK_O2)に失敗しました"
  exit 1
fi
if [ $INFO2 == null ]; then
  echo "署名鍵取得(INFO2)に失敗しました"
  exit 1
fi

# 送金用アカウント署名作成
TRANSFER_SIGNATURE=$($SIGNATURE_TOOLS/tools/transfer_signature.sh $SK_O1 $ACCOUNT_ID1 $ACCOUNT_ID1 $ACCOUNT_ID2 2000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$TRANSFER_SIGNATURE" ]; then
  echo "送金用アカウント署名作成に失敗しました"
  exit 1
fi

echo "12-1 共通領域コイン送金／付加領域コイン移転受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN1" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER2\", \"transfer_amount\":2000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO1\"}" | jq .
echo ""

echo "13-1 共通領域コイン送金／付加領域コイン移転"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization:$ID_TOKEN1" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER2\", \"transfer_amount\":2000, \"account_signature\": \"$TRANSFER_SIGNATURE\", \"info\":\"$INFO1\"}" | jq .
echo ""

echo "6-6 共通領域用口座／付加領域用口座取得"
USER_RES6_6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$USER_RES6_6" | jq .
BALANCE6_6=$(echo $USER_RES6_6 | jq -r ".balance")
if [ "$BALANCE6_6" != "7000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-6 残高取得"
BALANCES_RES8_6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$BALANCES_RES8_6" | jq .
TOTAL_BALANCE8_6=$(echo $BALANCES_RES8_6 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_6" != "7000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-4 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "6-7 共通領域用口座／付加領域用口座取得"
USER_RES6_7=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo "$USER_RES6_7" | jq .
BALANCE6_7=$(echo $USER_RES6_7 | jq -r ".balance")
if [ "$BALANCE6_7" != "2000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-7 残高取得"
BALANCES_RES8_7=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo "$BALANCES_RES8_7" | jq .
TOTAL_BALANCE8_7=$(echo $BALANCES_RES8_7 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_7" != "2000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-5 デジタル通貨取引一覧照会"
TRANSACTION_RES18_5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo "$TRANSACTION_RES18_5" | jq .

echo ""
echo "以降のスクリプト実行前に、以下をターミナル上で実行してください"
echo ""
echo "export SK_O1=$SK_O1"
echo "export INFO1=$INFO1"
echo "export ACCOUNT_ID1=$ACCOUNT_ID1"
echo "export SK_O2=$SK_O2"
echo "export INFO2=$INFO2"
echo "export ACCOUNT_ID2=$ACCOUNT_ID2"
echo "export SK_O3=$SK_O3"
echo "export INFO3=$INFO3"
echo "export ACCOUNT_ID3=$ACCOUNT_ID3"
echo "export SIGN_IN_ID1=$SIGN_IN_ID1"
echo "export SIGN_IN_ID2=$SIGN_IN_ID2"
echo "export SIGN_IN_ID3=$SIGN_IN_ID3"
echo ""

echo "----- 01_single1_transfer_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`