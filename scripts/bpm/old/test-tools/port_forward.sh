#!/bin/bash

if [ $# -ne 1 ]; then
    echo "環境名を入力してください"
    echo "USAGE : ./bpm/test-tools/port_forward.sh ( bpmfin | bpmind )"
    exit
fi

# 環境変数を読み込む
if [ $1 == "bpmfin" ]; then
  source ./env-bpmfin.sh
elif [ $1 == "bpmind" ]; then
  source ./env-bpmind.sh
else
  echo "環境名は( bpmfin | bpmind )を指定してください"
  exit 0
fi

echo "対象の環境のProfileに変更していることを確認し、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

# 踏み台のインスタンスIDを取得する
INSTANCE_ID=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=*bastion" --query "Reservations[].Instances[].InstanceId" | grep "i-" | sed 's/^.*"\(.*\)".*$/\1/')

# 踏み台にポートフォワードする
ssh -i ~/.ssh/$PRIVATE_KEY -D $SOCKS_PORT -N ec2-user@$INSTANCE_ID &
# RDSにポートフォワードする
ssh -i ~/.ssh/$PRIVATE_KEY ec2-user@$INSTANCE_ID -L $LOCAL_DB_PORT:$DB_PORT_FORWARD:$DB_PORT -N &
