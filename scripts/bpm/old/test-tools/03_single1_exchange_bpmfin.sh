#!/bin/bash
# 共通領域シングルモードで付加領域用口座作成〜チャージを行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 7 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$REGION_ID \$SIGN_IN_ID1 \$PASSWORD1 \$SIGN_IN_ID2 \$PASSWORD2 \$SIGN_IN_ID3 \$PASSWORD3"
  echo ""
  exit 9
fi

REGION_ID=$1
SIGN_IN_ID1=$2
PASSWORD1=$3
SIGN_IN_ID2=$4
PASSWORD2=$5
SIGN_IN_ID3=$6
PASSWORD3=$7

echo "----- 03_single1_exchange_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\"}")
echo "$SIGN_IN_RES1" | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")
DC_BANK_NUMBER1=$(echo $SIGN_IN_RES1 | jq -r ".dc_bank_number")
ACCOUNT_ID1=$(echo $SIGN_IN_RES1 | jq -r ".account_id")
if [ $ID_TOKEN1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID1 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "署名鍵取得"
SECURITY_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/security -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo "$SECURITY_RES1" | jq .
SK_O1=$(echo $SECURITY_RES1 | jq -r ".sk_o")
INFO1=$(echo $SECURITY_RES1 | jq -r ".info")
if [ $SK_O1 == null ]; then
  echo "署名鍵取得(SK_O1)に失敗しました"
  exit 1
fi
if [ $INFO1 == null ]; then
  echo "署名鍵取得(INFO1)に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
echo $SIGN_IN_RES2 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
DC_BANK_NUMBER2=$(echo $SIGN_IN_RES2 | jq -r ".dc_bank_number")
ACCOUNT_ID2=$(echo $SIGN_IN_RES2 | jq -r ".account_id")
if [ $ID_TOKEN2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $DC_BANK_NUMBER2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi
if [ $ACCOUNT_ID2 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
echo $SIGN_IN_RES3 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
if [ $ID_TOKEN3 == null ]; then
  echo "サインイン受付に失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "8-15 残高取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "44-1 付加領域用口座作成"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" | jq .
echo ""

sleep 8

echo "8-16 残高取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "44-2 付加領域用口座作成"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" | jq .
echo ""

sleep 8

echo "44-3 付加領域用口座作成"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/user/industry -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"region_id\":$REGION_ID}" | jq .
echo ""

sleep 8

# チャージ用アカウント署名作成
EXCHANGE_SIGNATURE=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O1 $ACCOUNT_ID1 $REGION_ID 3000 | grep "account signature:" | sed -r 's/^account signature: //')

echo "15-1 共通領域コインと付加領域コインのチャージ／返還受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO1\"}" | jq .
echo ""

echo "16-1 共通領域コインと付加領域コインのチャージ／返還"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3000, \"account_signature\":\"$EXCHANGE_SIGNATURE\", \"info\":\"$INFO1\"}" | jq .
echo ""

sleep 8

echo "6-15 共通領域用口座／付加領域用口座取得"
USER_RES6_6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USER_RES6_6 | jq .
BALANCE6_6=$(echo $USER_RES6_6 | jq -r ".balance")
if [ "$BALANCE6_6" != "4000" ]; then
  echo "残高が異なります"
  exit 1
fi

echo "8-17 残高取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "18-11 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" | jq .
echo ""

echo "----- 03_single1_exchange_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`