#!/bin/bash
# 共通領域シングルモードで解約を行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 7 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_PASSWORD \$SIGN_IN_ID4 \$PASSWORD4 \$NEW_PASSWORD4 \$SIGN_IN_ID5 \$PASSWORD5"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_PASSWORD=$2
SIGN_IN_ID4=$3
PASSWORD4=$4
NEW_PASSWORD4=$5
SIGN_IN_ID5=$6
PASSWORD5=$7

echo "----- 05_single2_terminated_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .
echo ""

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "サインイン受付"
SIGN_IN_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\"}")
echo $SIGN_IN_RES4 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")
DC_BANK_NUMBER4=$(echo $SIGN_IN_RES4 | jq -r ".dc_bank_number")

if [ $ACCESS_TOKEN4 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN4 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

echo "3-2 サインイン受付"
SIGN_IN_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\"}")
echo $SIGN_IN_RES5 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")
DC_BANK_NUMBER5=$(echo $SIGN_IN_RES5 | jq -r ".dc_bank_number")

if [ $ID_TOKEN5 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "49-2 パスワード変更"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/password/change -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"access_token\":\"$ACCESS_TOKEN4\", \"password\":\"$PASSWORD4\", \"new_password\":\"$NEW_PASSWORD4\"}" | jq .
echo ""

echo "8-23 残高取得"
BALANCES_RES8_23=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $BALANCES_RES8_23 | jq .
TOTAL_BALANCE8_23=$(echo $BALANCES_RES8_23 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_23" != "4000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "6-18 共通領域用口座／付加領域用口座取得"
USER_RES6_18=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $USER_RES6_18 | jq .
BALANCE6_18=$(echo $USER_RES6_18 | jq -r ".balance")
if [ "$BALANCE6_18" !=  "5000" ]; then
  echo "共通領域用口座／付加領域用口座取得に失敗しました"
  exit 1
fi

echo "8-24 残高取得"
BALANCES_RES8_24=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user/balances -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $BALANCES_RES8_24 | jq .
TOTAL_BALANCE8_24=$(echo $BALANCES_RES8_24 | jq -r ".total_balance")
if [ "$TOTAL_BALANCE8_24" != "5000" ]; then
  echo "残高取得に失敗しました"
  exit 1
fi

echo "18-14 デジタル通貨取引一覧照会"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/transactions -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" | jq .
echo ""

echo "17-5 共通領域コイン償却"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"burn_amount\":4000}" | jq .
echo ""

sleep 8

echo "17-6 共通領域コイン償却"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/token/burn -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"burn_amount\":5000}" | jq .
echo ""

# 承認モードが有効な場合に限り実施
if [ "$TOKEN_FLOW_MODE" == "approval" ]; then
  echo "53-6 承認待ち処理一覧取得"
  ORDER_RES6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET "$BASE_URL/management/orders?offset=0&limit=2&order_status=pending" -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json");
  echo "$ORDER_RES6" | jq .
  echo ""

  ORDER_ID6=$(echo $ORDER_RES6 | jq -r ".order_items[1].order_id")
  if [ $ORDER_ID6 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER6=$(echo $ORDER_RES6 | jq -r ".order_items[1].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER6" != "$DC_BANK_NUMBER4" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER4)と異なります"
    exit 1
  fi

  echo "54-7 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID6/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":4000}' | jq .
  echo ""

  sleep 8

  ORDER_ID4=$(echo $ORDER_RES6 | jq -r ".order_items[0].order_id")
  if [ $ORDER_ID6 == null ]; then
    echo "承認待ち処理の取得に失敗しました"
    exit 1
  fi
  ORDER_DC_BANK_NUMBER4=$(echo $ORDER_RES6 | jq -r ".order_items[0].dc_bank_number");
  if [ "$ORDER_DC_BANK_NUMBER4" != "$DC_BANK_NUMBER5" ]; then
    echo "承認待ち処理一覧取得から得られたDC口座番号が想定($DC_BANK_NUMBER5)と異なります"
    exit 1
  fi

  echo "54-8 承認待ち処理承認"
  curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/orders/$ORDER_ID4/approve -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d '{"approval_amount":5000}' | jq .
  echo ""
fi

sleep 8

echo "24-2 デジタル通貨口座解約受付"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated/check -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "25-2 デジタル通貨口座解約"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/terminated -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "共通領域の手順6および9の実施に必要になるので、以降のシェルを実行前に以下のコマンドを実行してください"
echo ""
echo "export DC_BANK_NUMBER4=$DC_BANK_NUMBER4"
echo "export DC_BANK_NUMBER5=$DC_BANK_NUMBER5"
echo ""

echo "----- 05_single2_terminated_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`