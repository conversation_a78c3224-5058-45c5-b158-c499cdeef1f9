#!/bin/bash
# 付加領域マルチモードで管理者作成〜照会を行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 5 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME2 \$ADMIN_PASSWORD2 \$ADMIN_NEW_PASSWORD2 \$DC_BANK_NUMBER4 \$BUSINESS_ENTITY_ID2"
  echo ""
  exit 9
fi

ADMIN_USER_NAME2=$1
ADMIN_PASSWORD2=$2
ADMIN_NEW_PASSWORD2=$3
# 付加領域用口座作成で作成したDC_BANK_NUMBER1
DC_BANK_NUMBER4=$4
BUSINESS_ENTITY_ID2=$5

echo "----- 07_multi2_create_admin_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "46-2 管理者作成"
SIGNUP_RES46_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME2\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGNUP_RES46_2 | jq .
TEMPORARY_PASSWORD=$(echo $SIGNUP_RES46_2  | jq -r ".temporary_password")
if [ $TEMPORARY_PASSWORD == null ]; then
  echo "管理者作成に失敗しました"
  exit 1
fi

echo "47-3 管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME2\", \"password\":\"$TEMPORARY_PASSWORD\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $ADMIN_SIGNIN_RES | jq .

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_SESSION == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "48-2 管理者パスワード登録"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME2\", \"new_password\":\"$ADMIN_PASSWORD2\", \"session\":\"$ADMIN_SESSION\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}" | jq .
echo ""

echo "47-4 管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME2\", \"password\":\"$ADMIN_PASSWORD2\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $ADMIN_SIGNIN_RES | jq .

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "49-4 パスワード変更"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/password/change -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"access_token\":\"$ADMIN_ACCESS_TOKEN\", \"password\":\"$ADMIN_PASSWORD2\", \"new_password\":\"$ADMIN_NEW_PASSWORD2\"}" | jq .
echo ""

echo "21-2 共通領域用口座／付加領域用口座取得（管理画面用）"
USER_21_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user?offset=0\&limit=100 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json")
echo $USER_21_2 | jq .

echo "19-1 デジタル通貨取引一覧照会（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER4/transactions?offset=0\&limit=5\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3001\&region_id_sort=desc\&date_sort=desc -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" | jq .
echo ""

echo "----- 07_multi2_create_admin_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`