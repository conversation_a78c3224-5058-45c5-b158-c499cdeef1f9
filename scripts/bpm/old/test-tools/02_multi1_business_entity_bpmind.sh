#!/bin/bash
# 付加領域マルチモードでサインイン〜付加領域用アカウント開設チェックを行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 3 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ID_ADMIN_TOKEN1 \$BUSINESS_ENTITY_NAME1 \$BUSINESS_ENTITY_NAME2"
  echo ""
  exit 9
fi

ID_ADMIN_TOKEN1=$1
BUSINESS_ENTITY_NAME1=$2
BUSINESS_ENTITY_NAME2=$3

echo "----- 02_multi1_business_entity_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "53-1 事業者の登録1"
BUSINESS_ENTITY_RES1=$(curl -Ss -X POST $BASE_URL/admin/business_entities -H "Authorization: $ID_ADMIN_TOKEN1"  -H "Content-Type: application/json" -d "{\"business_entity_name\":\"$BUSINESS_ENTITY_NAME1\"}")
BUSINESS_ENTITY_ID1=$(echo $BUSINESS_ENTITY_RES1 | jq -r ".business_entity_id");

if [ $BUSINESS_ENTITY_ID1 == null ]; then
  echo "事業者の登録1に失敗しました"
  exit 1
fi
echo $BUSINESS_ENTITY_RES1 | jq .

echo "53-2 事業者の登録2"
BUSINESS_ENTITY_RES2=$(curl -Ss -X POST $BASE_URL/admin/business_entities -H "Authorization: $ID_ADMIN_TOKEN1"  -H "Content-Type: application/json" -d "{\"business_entity_name\":\"$BUSINESS_ENTITY_NAME2\"}")
BUSINESS_ENTITY_ID2=$(echo $BUSINESS_ENTITY_RES2 | jq -r ".business_entity_id");

if [ $BUSINESS_ENTITY_ID2 == null ]; then
  echo "事業者の登録2に失敗しました"
  exit 1
fi
echo $BUSINESS_ENTITY_RES2 | jq .

echo ""
echo "以降のスクリプト実行前に、以下の変数を付加領域用の環境変数に定義してください"
echo ""
echo "export BUSINESS_ENTITY_ID1=$BUSINESS_ENTITY_ID1"
echo "export BUSINESS_ENTITY_ID2=$BUSINESS_ENTITY_ID2"
echo ""


echo "----- 02_multi1_business_entity_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`