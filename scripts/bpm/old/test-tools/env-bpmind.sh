#!/bin/bash

# 環境に応じて変更する
export BASE_URL=https://api.sandbox-bpmind.dc-labo.com
export DB_PORT_FORWARD=dcbg-dcf-sandbox-bpmind-db-cluster.cluster-cpyqo7egmbfo.ap-northeast-1.rds.amazonaws.com
export DB=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_NAME=bpm
export PGPASSWORD=
export USER_POOL_ID=ap-northeast-1_ykqhuIyBl
export PRIVATE_KEY=bastion_sandbox-bpmind.pem
export REGION_ID=3000

# 固定
export SOCKS_PORT=1081
# VPN接続していれば、APIはローカルから接続可能なので、API_PORT_FOWARD_SETTING は空文字を設定すること。
# export API_PORT_FOWARD_SETTING=--proxy socks5://localhost:$SOCKS_PORT
export API_PORT_FOWARD_SETTING=
export LOCAL_DB_PORT=5433
export SIGNATURE_TOOLS=../../core/signature-tools

# テストデータ作成スクリプトに応じて設定すること。ファイル名が 00 〜 09 で始まっているものに対しては設定不要
# export SIGN_IN_ID1=DCP9031
# export PASSWORD1=Password1
# export NEW_PASSWORD1=NPassword1
# export PHONE_NUMBER1=+************
# export SIGN_IN_ID2=DCP9032
# export PASSWORD2=Password2
# export PHONE_NUMBER2=+************
# export SIGN_IN_ID3=DCP9033
# export PASSWORD3=Password3
# export PHONE_NUMBER3=+************
# export ADMIN_USER_NAME=bankUser1
# export ADMIN_PASSWORD=APassword1
# export ADMIN_NEW_PASSWORD=ANPassword1
# # SEMI_NORMAL_PHONE_NUMBERは、下一桁を3-4で複製します
# export SEMI_NORMAL_PHONE_NUMBER=***********
# # SEMI_NORMAL_INTERNATIONAL_PHONE_NUMBERは、下一桁を3-4で複製します
# export SEMI_NORMAL_INTERNATIONAL_PHONE_NUMBER=+************
# # SEMI_NORMAL_PASSWORDは、下一桁を3-4で複製します
# export SEMI_NORMAL_PASSWORD=Password1
# # SEMI_NORMAL_SIGN_IN_IDは、下一桁を3-4で複製します
# export SEMI_NORMAL_SIGN_IN_ID=DCP9130
# export AUTH_CODE1=123456