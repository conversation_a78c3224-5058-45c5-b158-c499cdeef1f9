#!/bin/bash
# 共通領域シングルモードで管理者作成〜照会を行うシェル

# 環境変数の設定
source ./env-bpmfin.sh

# 引数チェック
if [ $# -ne 2 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_PASSWORD"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_PASSWORD=$2

echo "----- 00_single1_create_admin_bpmfin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "46-1 管理者作成"
SIGNUP_RES46_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signup -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\"}")
echo $SIGNUP_RES46_1 | jq .
TEMPORARY_PASSWORD=$(echo $SIGNUP_RES46_1 | jq -r ".temporary_password")
if [ $TEMPORARY_PASSWORD == null ]; then
  echo "管理者作成に失敗しました"
  exit 1
fi

echo "47-1 管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$TEMPORARY_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_SESSION == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo ""

echo "48-1 管理者パスワード登録"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/password/register -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"new_password\":\"$ADMIN_PASSWORD\", \"session\":\"$ADMIN_SESSION\"}" | jq .
echo ""

echo "47-2 管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .
echo ""

# 管理者サインインのレスポンスを変数に設定
ADMIN_ACCESS_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".access_token")
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")
ADMIN_SESSION=$(echo $ADMIN_SIGNIN_RES | jq -r ".session")

if [ $ADMIN_ACCESS_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "----- 00_single1_create_admin_bpmfin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`