#!/bin/bash
# 付加領域マルチモードでサインイン〜付加領域用アカウント開設チェックを行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 32 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \
\$PHONE_NUMBER1 \$PASSWORD1 \$ACCOUNT_ID1 \$SK_O1 \$INFO1 \
\$PHONE_NUMBER2 \$PASSWORD2 \$ACCOUNT_ID2 \$SK_O2 \$INFO2 \
\$PHONE_NUMBER3 \$PASSWORD3 \$ACCOUNT_ID3 \$SK_O3 \$INFO3 \
\$PHONE_NUMBER4 \$PASSWORD4 \$ACCOUNT_ID4 \$SK_O4 \$INFO4 \
\$PHONE_NUMBER5 \$PASSWORD5 \$ACCOUNT_ID5 \$SK_O5 \$INFO5 \
\$PHONE_NUMBER6 \$PASSWORD6 \$ACCOUNT_ID6 \$SK_O6 \$INFO6 \
\$BUSINESS_ENTITY_ID1 \$BUSINESS_ENTITY_ID2"
  echo ""
  exit 9
fi
# ユーザ1 の共通領域で作成したアカウントID, SK_O, INFO
PHONE_NUMBER1=$1
PASSWORD1=$2
ACCOUNT_ID1=$3
SK_O1=$4
INFO1=$5
# ユーザ2 の共通領域で作成したアカウントID, SK_O, INFO
PHONE_NUMBER2=$6
PASSWORD2=$7
ACCOUNT_ID2=$8
SK_O2=$9
INFO2=${10}
# ユーザ3 の共通領域で作成したアカウントID, SK_O, INFO
PHONE_NUMBER3=${11}
PASSWORD3=${12}
ACCOUNT_ID3=${13}
SK_O3=${14}
INFO3=${15}
# ユーザ4 の共通領域で作成したアカウントID, SK_O, INFO
PHONE_NUMBER4=${16}
PASSWORD4=${17}
ACCOUNT_ID4=${18}
SK_O4=${19}
INFO4=${20}
# ユーザ5 の共通領域で作成したアカウントID, SK_O, INFO
PHONE_NUMBER5=${21}
PASSWORD5=${22}
ACCOUNT_ID5=${23}
SK_O5=${24}
INFO5=${25}
# ユーザ6 の共通領域で作成したアカウントID, SK_O, INFO
PHONE_NUMBER6=${26}
PASSWORD6=${27}
ACCOUNT_ID6=${28}
SK_O6=${29}
INFO6=${30}
BUSINESS_ENTITY_ID1=${31}
BUSINESS_ENTITY_ID2=${32}

echo "./02_multi2_synchronous_bpmind.sh $SIGN_IN_ID1 $PASSWORD1 $ACCOUNT_ID1 $SK_O1 $INFO1 $SIGN_IN_ID2 $PASSWORD2 $ACCOUNT_ID2 $SK_O2 $INFO2 $SIGN_IN_ID3 $PASSWORD3 $ACCOUNT_ID3 $SK_O3 $INFO3 $SIGN_IN_ID4 $PASSWORD4 $ACCOUNT_ID4 $SK_O4 $INFO4 $SIGN_IN_ID5 $PASSWORD5 $ACCOUNT_ID5 $SK_O5 $INFO5 $SIGN_IN_ID6 $PASSWORD6 $ACCOUNT_ID6 $SK_O6 $INFO6 $BUSINESS_ENTITY_ID1 $BUSINESS_ENTITY_ID2"

echo "----- 02_multi2_synchronous_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "3-1 サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}")
echo $SIGN_IN_RES1 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")

if [ $ACCESS_TOKEN1 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN1 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成
SYNCHRONOUS_SIGNATURE1=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O1 $ACCOUNT_ID1 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-1 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE1\", \"info\":\"$INFO1\"}")
echo $SYNCHRONOUS_RES14_1 | jq .
DC_BANK_NUMBER1=$(echo $SYNCHRONOUS_RES14_1 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER1 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-1 共通領域用口座／付加領域用口座取得"
USER_RES6_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USER_RES6_1 | jq .
DC_BANK_NUMBER6_1=$(echo $USER_RES6_1 | jq -r ".dc_bank_number")
SIGN_IN_ID6_1=$(echo $USER_RES6_1 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_1" != "$DC_BANK_NUMBER1" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

if [ "$SIGN_IN_ID6_1" != "$SIGN_IN_ID1" ]; then
  echo "sign_in_idが異なります"
  exit 1
fi

echo "59-1 端末認証なしサインアップ"
SIGN_IN_ID2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER2\", \"password\":\"$PASSWORD2\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID2 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-2 サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}")
echo $SIGN_IN_RES2 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")

if [ $ACCESS_TOKEN2 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN2 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成2
SYNCHRONOUS_SIGNATURE2=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O2 $ACCOUNT_ID2 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-2 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID2\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE2\", \"info\":\"$INFO2\"}")
echo $SYNCHRONOUS_RES14_2 | jq .
DC_BANK_NUMBER2=$(echo $SYNCHRONOUS_RES14_2 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER2 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-2 共通領域用口座／付加領域用口座取得"
USER_RES6_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $USER_RES6_2 | jq .
DC_BANK_NUMBER6_2=$(echo $USER_RES6_2 | jq -r ".dc_bank_number")
SIGN_IN_ID6_2=$(echo $USER_RES6_2 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_2" != "$DC_BANK_NUMBER2" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi


echo "59-2 端末認証なしサインアップ"
SIGN_IN_ID3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER3\", \"password\":\"$PASSWORD3\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID3 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-3 サインイン受付"
SIGN_IN_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID1\"}")
echo $SIGN_IN_RES2 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".access_token")
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")

if [ $ACCESS_TOKEN3 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN3 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成2
SYNCHRONOUS_SIGNATURE3=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O3 $ACCOUNT_ID3 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-3 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}")
echo $SYNCHRONOUS_RES14_3 | jq .
DC_BANK_NUMBER3=$(echo $SYNCHRONOUS_RES14_3 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER3 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-3 共通領域用口座／付加領域用口座取得"
USER_RES6_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json")
echo $USER_RES6_3 | jq .
DC_BANK_NUMBER6_3=$(echo $USER_RES6_3 | jq -r ".dc_bank_number")
SIGN_IN_ID6_3=$(echo $USER_RES6_3 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_3" != "$DC_BANK_NUMBER3" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

echo "51-1 アカウントID取得"
USERS_RES51_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER1 -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USERS_RES51_1 | jq .
ACCOUNT_ID51_1=$(echo $USERS_RES51_1 | jq -r ".account_id")
if [ "$ACCOUNT_ID51_1" != "$ACCOUNT_ID1" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "51-2 アカウントID取得"
USERS_RES51_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER2 -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $USERS_RES51_2 | jq .
ACCOUNT_ID51_2=$(echo $USERS_RES51_2 | jq -r ".account_id")
if [ "$ACCOUNT_ID51_2" != "$ACCOUNT_ID2" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "59-3 端末認証なしサインアップ"
SIGN_IN_ID4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER4\", \"password\":\"$PASSWORD4\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID4 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-4 サインイン受付"
SIGN_IN_RES4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID4\", \"password\":\"$PASSWORD4\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGN_IN_RES4 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".access_token")
ID_TOKEN4=$(echo $SIGN_IN_RES4 | jq -r ".id_token")

if [ $ACCESS_TOKEN4 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN4 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成4
SYNCHRONOUS_SIGNATURE4=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O4 $ACCOUNT_ID4 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-4 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID4\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE4\", \"info\":\"$INFO4\"}")
echo $SYNCHRONOUS_RES14_4 | jq .
DC_BANK_NUMBER4=$(echo $SYNCHRONOUS_RES14_4 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER4 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-4 共通領域用口座／付加領域用口座取得"
USER_RES6_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $USER_RES6_4 | jq .
DC_BANK_NUMBER6_4=$(echo $USER_RES6_4 | jq -r ".dc_bank_number")
SIGN_IN_ID6_4=$(echo $USER_RES6_4 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_4" != "$DC_BANK_NUMBER4" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

echo "59-4 端末認証なしサインアップ"
SIGN_IN_ID5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER5\", \"password\":\"$PASSWORD5\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID5 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-5 サインイン受付"
SIGN_IN_RES5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID5\", \"password\":\"$PASSWORD5\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGN_IN_RES5 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".access_token")
ID_TOKEN5=$(echo $SIGN_IN_RES5 | jq -r ".id_token")

if [ $ACCESS_TOKEN5 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN5 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成5
SYNCHRONOUS_SIGNATURE5=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O5 $ACCOUNT_ID5 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-5 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID5\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE5\", \"info\":\"$INFO5\"}")
echo $SYNCHRONOUS_RES14_5 | jq .
DC_BANK_NUMBER5=$(echo $SYNCHRONOUS_RES14_5 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER5 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-5 共通領域用口座／付加領域用口座取得"
USER_RES6_5=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $USER_RES6_5 | jq .
DC_BANK_NUMBER6_5=$(echo $USER_RES6_5 | jq -r ".dc_bank_number")
SIGN_IN_ID6_5=$(echo $USER_RES6_5 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_5" != "$DC_BANK_NUMBER5" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

echo "59-5 端末認証なしサインアップ"
SIGN_IN_ID6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signup/without_device -H "Content-Type: application/json" -d "{\"phone_number\":\"$PHONE_NUMBER6\", \"password\":\"$PASSWORD6\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}" | jq -r ".sign_in_id")
if [ $SIGN_IN_ID6 == null ]; then
  echo "端末認証なしサインアップに失敗しました"
  exit 1
fi

echo "3-6 サインイン受付"
SIGN_IN_RES6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID6\", \"password\":\"$PASSWORD6\", \"business_entity_id\":\"$BUSINESS_ENTITY_ID2\"}")
echo $SIGN_IN_RES6 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".access_token")
ID_TOKEN6=$(echo $SIGN_IN_RES6 | jq -r ".id_token")

if [ $ACCESS_TOKEN6 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN6 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成5
SYNCHRONOUS_SIGNATURE6=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O6 $ACCOUNT_ID6 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-6 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID6\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE6\", \"info\":\"$INFO6\"}")
echo $SYNCHRONOUS_RES14_6 | jq .
DC_BANK_NUMBER6=$(echo $SYNCHRONOUS_RES14_6 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER6 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-6 共通領域用口座／付加領域用口座取得"
USER_RES6_6=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN6" -H "Content-Type: application/json")
echo $USER_RES6_6 | jq .
DC_BANK_NUMBER6_6=$(echo $USER_RES6_6 | jq -r ".dc_bank_number")
SIGN_IN_ID6_6=$(echo $USER_RES6_6 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_6" != "$DC_BANK_NUMBER6" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

echo "51-3 アカウントID取得"
USERS_RES51_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER4 -H "Authorization: $ID_TOKEN4" -H "Content-Type: application/json")
echo $USERS_RES51_3 | jq .
ACCOUNT_ID51_3=$(echo $USERS_RES51_3 | jq -r ".account_id")
if [ "$ACCOUNT_ID51_3" != "$ACCOUNT_ID4" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "51-4 アカウントID取得"
USERS_RES51_4=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER5 -H "Authorization: $ID_TOKEN5" -H "Content-Type: application/json")
echo $USERS_RES51_4 | jq .
ACCOUNT_ID51_4=$(echo $USERS_RES51_4 | jq -r ".account_id")
if [ "$ACCOUNT_ID51_4" != "$ACCOUNT_ID5" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo ""
echo "以降のスクリプト実行前にターミナル上に以下のコマンドを実行してください"
echo ""
echo "export SIGN_IN_ID1=$SIGN_IN_ID1"
echo "export SIGN_IN_ID2=$SIGN_IN_ID2"
echo "export SIGN_IN_ID3=$SIGN_IN_ID3"
echo "export SIGN_IN_ID4=$SIGN_IN_ID4"
echo "export SIGN_IN_ID5=$SIGN_IN_ID5"
echo "export SIGN_IN_ID6=$SIGN_IN_ID6"
echo ""

echo "----- 02_multi2_synchronous_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`