#!/bin/bash
# 付加領域の準正常系テストを行うシェル

# 環境変数の設定
source ./env-bpmind.sh
source ./env_semi_normal_03.sh

DIR=$(dirname "$0")
cd $DIR

LOG_FILE=semi_normal_result_bpmind.txt
cp /dev/null $LOG_FILE
exec 1>$LOG_FILE

echo "----- semi_normal_test_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization:$ID_TOKEN3" -H "Content-Type: application/json" -d "{\"to_dc_bank_number\":\"$DC_BANK_NUMBER4\", \"transfer_amount\":2000, \"account_signature\":\"$TRANSFER_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"0\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"0\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"a12345\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3001, \"account_signature\":\"$EXCHANGE_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'
curl -4 --proxy socks5://localhost:$SOCKS_PORT -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"to_region_id\":$REGION_ID, \"exchange_amount\":3001, \"account_signature\":\"$EXCHANGE_SIGNATURE3\", \"info\":\"$INFO3\"}" -w '%{http_code}\n'

echo "----- semi_normal_test_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`