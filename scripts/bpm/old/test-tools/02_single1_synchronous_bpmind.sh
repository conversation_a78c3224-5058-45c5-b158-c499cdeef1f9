#!/bin/bash
# 付加領域シングルモードでサインイン〜付加領域用アカウント開設チェックを行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 15 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \
\$SIGN_IN_ID1 \$PASSWORD1 \$ACCOUNT_ID1 \$SK_O1 \$INFO1 \
\$SIGN_IN_ID2 \$PASSWORD2 \$ACCOUNT_ID2 \$SK_O2 \$INFO2 \
\$SIGN_IN_ID3 \$PASSWORD3 \$ACCOUNT_ID3 \$SK_O3 \$INFO3"
  echo ""
  exit 9
fi
# ユーザ1 の共通領域で作成したアカウントID, SK_O, INFO
SIGN_IN_ID1=$1
PASSWORD1=$2
ACCOUNT_ID1=$3
SK_O1=$4
INFO1=$5
# ユーザ2 の共通領域で作成したアカウントID, SK_O, INFO
SIGN_IN_ID2=$6
PASSWORD2=$7
ACCOUNT_ID2=$8
SK_O2=$9
INFO2=${10}
# ユーザ3 の共通領域で作成したアカウントID, SK_O, INFO
SIGN_IN_ID3=${11}
PASSWORD3=${12}
ACCOUNT_ID3=${13}
SK_O3=${14}
INFO3=${15}

echo "./02_single1_synchronous_bpmind.sh $SIGN_IN_ID1 $PASSWORD1 $ACCOUNT_ID1 $SK_O1 $INFO1 $SIGN_IN_ID2 $PASSWORD2 $ACCOUNT_ID2 $SK_O2 $INFO2 $SIGN_IN_ID3 $PASSWORD3 $ACCOUNT_ID3 $SK_O3 $INFO3"

echo "----- 02_single1_synchronous_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "3-1 サインイン受付"
SIGN_IN_RES1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID1\", \"password\":\"$PASSWORD1\"}")
echo $SIGN_IN_RES1 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".access_token")
ID_TOKEN1=$(echo $SIGN_IN_RES1 | jq -r ".id_token")

if [ $ACCESS_TOKEN1 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN1 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成
SYNCHRONOUS_SIGNATURE1=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O1 $ACCOUNT_ID1 | grep "account signature:" | sed -r 's/^account signature: //')

echo "14-1 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE1\", \"info\":\"$INFO1\"}")
echo $SYNCHRONOUS_RES14_1 | jq .
DC_BANK_NUMBER1=$(echo $SYNCHRONOUS_RES14_1 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER1 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-1 共通領域用口座／付加領域用口座取得"
USER_RES6_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USER_RES6_1 | jq .
DC_BANK_NUMBER6_1=$(echo $USER_RES6_1 | jq -r ".dc_bank_number")
SIGN_IN_ID6_1=$(echo $USER_RES6_1 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_1" != "$DC_BANK_NUMBER1" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

if [ "$SIGN_IN_ID6_1" != "$SIGN_IN_ID1" ]; then
  echo "sign_in_idが異なります"
  exit 1
fi

echo "51-1 アカウントID取得"
USERS_RES51_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER1 -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USERS_RES51_1 | jq .
ACCOUNT_ID51_1=$(echo $USERS_RES51_1 | jq -r ".account_id")
if [ "$ACCOUNT_ID51_1" != "$ACCOUNT_ID1" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "3-2 サインイン受付"
SIGN_IN_RES2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID2\", \"password\":\"$PASSWORD2\"}")
echo $SIGN_IN_RES2 | jq .

# サインイン受付のレスポンスを変数に設定
ACCESS_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".access_token")
ID_TOKEN2=$(echo $SIGN_IN_RES2 | jq -r ".id_token")
if [ $ACCESS_TOKEN2 == null ]; then
  echo "access_tokenの取得に失敗しました"
  exit 1
fi

if [ $ID_TOKEN2 == null ]; then
  echo "id_tokenの取得に失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成
SYNCHRONOUS_SIGNATURE2=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O2 $ACCOUNT_ID2 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$SYNCHRONOUS_SIGNATURE2" ]; then
  echo "付加領域アカウント開設用アカウント署名作成に失敗しました"
  exit 1
fi

echo "14-2 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID2\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE2\", \"info\":\"$INFO2\"}")
echo $SYNCHRONOUS_RES14_2 | jq .
DC_BANK_NUMBER2=$(echo $SYNCHRONOUS_RES14_2 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER2 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "6-2 共通領域用口座／付加領域用口座取得"
USER_RES6_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN2" -H "Content-Type: application/json")
echo $USER_RES6_2 | jq .
DC_BANK_NUMBER6_2=$(echo $USER_RES6_2 | jq -r ".dc_bank_number")
SIGN_IN_ID6_2=$(echo $USER_RES6_2 | jq -r ".sign_in_id")
if [ "$DC_BANK_NUMBER6_2" != "$DC_BANK_NUMBER2" ]; then
  echo "dc_bank_numberが異なります"
  exit 1
fi

if [ "$SIGN_IN_ID6_2" != "$SIGN_IN_ID2" ]; then
  echo "sign_in_idが異なります"
  exit 1
fi

echo "51-2 アカウントID取得"
USERS_RES51_2=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/users/$DC_BANK_NUMBER2 -H "Authorization: $ID_TOKEN1" -H "Content-Type: application/json")
echo $USERS_RES51_2 | jq .
ACCOUNT_ID51_2=$(echo $USERS_RES51_2 | jq -r ".account_id")
if [ "$ACCOUNT_ID51_2" != "$ACCOUNT_ID2" ]; then
  echo "account_idの取得に失敗しました"
  exit 1
fi

echo "3-3 サインイン受付"
SIGN_IN_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
echo $SIGN_IN_RES3 | jq .

# サインイン受付のレスポンスを変数に設定
ID_TOKEN3=$(echo $SIGN_IN_RES3 | jq -r ".id_token")
if [ $ID_TOKEN3 == null ]; then
  echo "サインインに失敗しました"
  exit 1
fi

# 付加領域アカウント開設用アカウント署名作成
SYNCHRONOUS_SIGNATURE3=$($SIGNATURE_TOOLS/tools/synchronous_signature.sh $SK_O3 $ACCOUNT_ID3 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$SYNCHRONOUS_SIGNATURE3" ]; then
  echo "付加領域アカウント開設用アカウント署名作成に失敗しました"
  exit 1
fi

echo "14-3 付加領域アカウント開設受付チェック"
SYNCHRONOUS_RES14_3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/synchronous -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\", \"account_signature\":\"$SYNCHRONOUS_SIGNATURE3\", \"info\":\"$INFO3\"}")
echo $SYNCHRONOUS_RES14_3 | jq .
DC_BANK_NUMBER3=$(echo $SYNCHRONOUS_RES14_3 | jq -r ".dc_bank_number")
if [ $DC_BANK_NUMBER3 == null ]; then
  echo "付加領域アカウント開設受付チェックに失敗しました"
  exit 1
fi

echo "6-7 共通領域用口座／付加領域用口座取得"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/user -H "Authorization: $ID_TOKEN3" -H "Content-Type: application/json" | jq .
echo ""

echo ""
echo "以降のスクリプト実行前に、以下の変数をenv-bpmind.shにそれぞれ定義してください"
echo ""
echo "export SIGN_IN_ID1=$SIGN_IN_ID1"
echo "export SIGN_IN_ID2=$SIGN_IN_ID2"
echo "export SIGN_IN_ID3=$SIGN_IN_ID3"
echo ""

echo "----- 02_single1_synchronous_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`