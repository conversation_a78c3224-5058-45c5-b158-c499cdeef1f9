#!/bin/bash
# 付加領域シングルモードで停止、解約を行うシェル

# 環境変数の設定
source ./env-bpmind.sh

# 引数チェック
if [ $# -ne 4 ]; then
  echo "引数が指定されていません。以下のように指定してください。"
  echo ""
  echo "./$(basename $0) \$ADMIN_USER_NAME \$ADMIN_NEW_PASSWORD \$SIGN_IN_ID3 \$PASSWORD3"
  echo ""
  exit 9
fi

ADMIN_USER_NAME=$1
ADMIN_NEW_PASSWORD=$2
SIGN_IN_ID3=$3
PASSWORD3=$4

echo "----- 08_single1_terminated_bpmind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "事前準備 開始"

echo "サインイン受付"
SIGN_IN_RES3=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/auth/signin/check -H "Content-Type: application/json" -d "{\"sign_in_id\":\"$SIGN_IN_ID3\", \"password\":\"$PASSWORD3\"}")
echo $SIGN_IN_RES3 | jq .

# サインイン受付のレスポンスを変数に設定
DC_BANK_NUMBER3=$(echo $SIGN_IN_RES3 | jq -r ".dc_bank_number")

if [ $DC_BANK_NUMBER3 == null ]; then
  echo "dc_bank_numberの取得に失敗しました"
  exit 1
fi

echo "管理者サインイン"
ADMIN_SIGNIN_RES=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/signin -H "Content-Type: application/json" -d "{\"user_name\":\"$ADMIN_USER_NAME\", \"password\":\"$ADMIN_NEW_PASSWORD\"}")
echo $ADMIN_SIGNIN_RES | jq .

# 管理者サインインのレスポンスを変数に設定
ADMIN_ID_TOKEN=$(echo $ADMIN_SIGNIN_RES | jq -r ".id_token")

if [ $ADMIN_ID_TOKEN == null ]; then
  echo "管理者サインインに失敗しました"
  exit 1
fi

echo "事前準備 終了"
echo ""

echo "20-1 ユーザー認証停止/停止解除"
AUTH_STOP_RES20_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER3/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false, \"reason_code\":30000, \"reason_detail\":\"auth stop\"}")
echo $AUTH_STOP_RES20_1 | jq .
STOP_CANCEL_NUMBER=$(echo $AUTH_STOP_RES20_1 | jq -r ".stop_cancel_number")
if [ $STOP_CANCEL_NUMBER == null ]; then
  echo "ユーザー認証停止/停止解除に失敗しました"
  exit 1
fi

sleep 4

echo "20-2 ユーザー認証停止/停止解除"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X PUT $BASE_URL/management/user/$DC_BANK_NUMBER3/auth/stop -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000, \"reason_detail\":\"auth restart\", \"stop_cancel_number\":\"$STOP_CANCEL_NUMBER\"}" | jq .
echo ""

sleep 4

echo "26-1 デジタル通貨口座解約受付（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated/check -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

echo "26-2 デジタル通貨口座解約（管理画面用）"
curl -4 $API_PORT_FOWARD_SETTING -Ss -X POST $BASE_URL/management/user/$DC_BANK_NUMBER3/terminated -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json" -d "{\"reason_code\":90000}" | jq .
echo ""

sleep 4

echo "7-1 共通領域用口座／付加領域用口座取得（管理画面用）"
USER_RES7_1=$(curl -4 $API_PORT_FOWARD_SETTING -Ss -X GET $BASE_URL/management/user/$DC_BANK_NUMBER3 -H "Authorization: $ADMIN_ID_TOKEN" -H "Content-Type: application/json")
echo $USER_RES7_1 | jq .
TERMINATED7_1=$(echo $USER_RES7_1 | jq -r ".terminated")
if [ "$TERMINATED7_1" != "true" ]; then
  echo "共通領域用口座／付加領域用口座取得（管理画面用）に失敗しました"
  exit 1
fi

echo "----- 08_single1_terminated_bpmind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`