#!/bin/bash
function error_close() {
  $SCRIPTDIR/_disconnect_port_forward.sh
  echo "登録処理を中断します。"
  exit 1
}

# FizZoneへの金融機関登録にあたり、Issuer作成、Validator作成、サービスオーナー作成を行うシェル。
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
echo "FinZoneに金融機関登録を行います(BPM)"

# 環境変数の設定
ENV_FILE=./env/bpmfin.env
if [ ! -f "$ENV_FILE" ]; then
  echo $ENV_FILE"が未作成です。手順に則り作成をしてください。"
  exit 1
fi
source $ENV_FILE
if [ -z "$TOKEN_URL" ] || [ -z "$BASE_URL" ] || [ -z "$A_CLIENT_ID" ] || [ -z "$A_CLIENT_SECRET" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi
if [ -z "$DB_PORT" ] || [ -z "$USER" ] || [ -z "$DB_NAME" ] || [ -z "$PGPASSWORD" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi
if [ -z "$ISSUER_NAME" ] || [ -z "$VALIDATOR_NAME" ] || [ -z "$SERVICE_NAME" ] || [ -z "$SERVICE_ID" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi

# （Admin）ACCESS_TOKENを取得する
if [[ "$TOKEN_URL" == *"localhost"* ]]; then
    # ローカル環境で実施する場合のトークン取得処理
    A_ACCESS_TOKEN=$(curl -Ss -X GET $TOKEN_URL'/77ssadfadojr2rkf9hofbio8e');
else
    A_ACCESS_TOKEN=$(curl -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
fi

$SCRIPTDIR/_port_forward.sh true

# ローカル環境に実施する場合はエンドポイントを追加
if [ -z "$AWS_PROFILE" ]; then
    AWS_END_POINT=" --endpoint-url=http://localhost:44566 "
fi

# 発行者作成
echo "1-1 発行者作成"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/admin/issuer -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"issuer_name":"'${ISSUER_NAME}'", "bank_code": "'${BANK_CODE}'"}' )
echo ${RESULT}| jq .
ISSUER_ID=$(echo ${RESULT} | jq -r ".issuer_id")
ISSUER_CLIENT_ID=$(echo ${RESULT} | jq -r ".client_id")
ISSUER_CLIENT_SECRET=$(echo ${RESULT} | jq -r ".client_secret")
if [ "$ISSUER_ID" == null ]; then
  # 登録済みかを確認
  ISSUER_ID=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select entity_id from auth_client where entity_type = 'issuer'" 2>&1 | xargs)
  if [ "$ISSUER_ID" == "" ]; then
    echo "発行者作成に失敗しました。"
    error_close
  fi
  echo "発行者作成のAPI実行に失敗しましたが、DBに登録済みのため後続処理を実施します。"
  ISSUER_CLIENT_ID=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select client_id from auth_client where entity_type = 'issuer'" 2>&1 | xargs)
  echo "Issuer ID : "${ISSUER_ID}
  echo "Issuer OAuth Client ID : "${ISSUER_CLIENT_ID}
  ISSUER_CLIENT_SECRET=$(aws $AWS_END_POINT secretsmanager get-secret-value --secret-id "dcjpy/bpm_server/oauth2/core/issuer/"${ISSUER_ID} --region $AWS_REGION | jq -r .SecretString |jq -r .client_secret)
  echo "Issuer OAuth Client Secret : "$ISSUER_CLIENT_SECRET
fi

# 発行者取得
echo "1-2 発行者取得 1件取得できることを確認する"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/admin/issuer/$ISSUER_ID -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .

# バリデータ作成
echo "2-1 バリデータ作成"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/admin/validator -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"validator_name":"'${VALIDATOR_NAME}'","issuer_id":"'${ISSUER_ID}'"}')
echo ${RESULT}| jq .
VALIDATOR_ID=$(echo ${RESULT} | jq -r ".validator_id")
VALIDATOR_CLIENT_ID=$(echo ${RESULT} | jq -r ".client_id")
VALIDATOR_CLIENT_SECRET=$(echo ${RESULT} | jq -r ".client_secret")
if [ "$VALIDATOR_ID" == null ]; then
  # 登録済みかを確認
  VALIDATOR_ID=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select entity_id from auth_client where entity_type = 'validator'" 2>&1 | xargs)
  if [ "$VALIDATOR_ID" == "" ]; then
    echo "バリデータ作成に失敗しました。"
    error_close
  fi
  echo "発行者作成のAPI実行に失敗しましたが、DBに登録済みのため後続処理を実施します。"
  VALIDATOR_CLIENT_ID=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select client_id from auth_client where entity_type = 'validator'" 2>&1 | xargs)
  echo "Validator ID : "${VALIDATOR_ID}
  echo "Validator OAuth Client ID : "${VALIDATOR_CLIENT_ID}
  VALIDATOR_CLIENT_SECRET=$(aws secretsmanager $AWS_END_POINT get-secret-value --secret-id "dcjpy/bpm_server/oauth2/core/validator/"${VALIDATOR_ID} --region $AWS_REGION | jq -r .SecretString |jq -r .client_secret)
  echo "Validator OAuth Client Secret : "$VALIDATOR_CLIENT_SECRET
fi

# バリデータ取得
echo "2-2 バリデータ取得 1件取得できることを確認する"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/admin/validator/$VALIDATOR_ID -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .

# サービスオーナー作成
echo "3-1 サービスオーナー作成"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/admin/service_owner -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"service_id":"'"${SERVICE_ID}"'","service_name":"'"${SERVICE_NAME}"'","zone_id":"'${ZONE_ID}'","validator_id":"'${VALIDATOR_ID}'","issuer_id":"'${ISSUER_ID}'"}')
ERROR_CODE=$(echo ${RESULT} | jq -r ".error_code")
if [ "$ERROR_CODE" == null ]; then
    echo $RESULT| jq .
elif [[ "$ERROR_CODE" == "EUE1330" ]]; then
    echo "サービスオーナー作成は作成済みです。"
else
    echo "サービスオーナー作成時に想定外のエラーが発生しました。"
    echo ${RESULT}| jq .
    error_close
fi

# M91-02. 銀行/事業者 管理ユーザ作成
echo "銀行/事業者 管理ユーザ作成"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/admin/service_user -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"user_name":"'${ADMIN_USER_NAME}'"}')
ERROR_CODE=$(echo ${RESULT} | jq -r ".error_code")
if [ "$ERROR_CODE" == null ]; then
    echo $RESULT| jq .
    SIGN_IN_ID=$(echo ${RESULT} | jq -r ".sign_in_id")
    TEMPORARY_PASSWORD=$(echo ${RESULT} | jq -r ".temporary_password")
elif [[ "$ERROR_CODE" == "EUE1000" ]]; then
    echo "銀行/事業者 管理ユーザは作成済みのため作成を行いませんでした。"
    SIGN_IN_ID="作成済みのためスクリプト内での作成を行いませんでした。"
    TEMPORARY_PASSWORD="作成済みのためスクリプト内での作成を行いませんでした。"
else
    echo "銀行/事業者 管理ユーザ作成時に想定外のエラーが発生しました。"
    echo ${RESULT}| jq .
    error_close
fi

echo "登録処理が正常終了しました。"

# 登録内容の出力
echo "登録内容の出力を行います。"
echo "▼issuer関連情報▼"
echo "    Issuer ID                     : ${ISSUER_ID}"
echo "    Issuer OAuth Client ID        : ${ISSUER_CLIENT_ID}"
echo "    Issuer OAuth Client Secret    : ${ISSUER_CLIENT_SECRET}"
echo ""

echo "▼validator関連情報▼"
echo "    Validator ID                  : ${VALIDATOR_ID}"
echo "    Validator OAuth Client ID     : ${VALIDATOR_CLIENT_ID}"
echo "    Validator OAuth Client Secret : ${VALIDATOR_CLIENT_SECRET}"
echo ""

echo "▼管理ユーザ▼"
echo "    SIGN_IN_ID                    : ${SIGN_IN_ID}"
echo "    TEMPORARY_PASSWORD            : ${TEMPORARY_PASSWORD}"
echo ""

echo "▼サービスオーナー▼"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -c " select * from service_owner where service_id='$SERVICE_ID'"

echo "全ての処理が正常終了しました。"

$SCRIPTDIR/_disconnect_port_forward.sh