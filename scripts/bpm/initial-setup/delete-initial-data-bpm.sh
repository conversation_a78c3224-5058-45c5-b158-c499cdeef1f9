#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
# 環境の確認
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo $RELEASE_ENV"の削除処理を開始します。ゾーン:"$TARGET_ZONE

# テーブルデータの削除
$SCRIPTDIR/_01_delete_table.sh $1 $2
# SecretManagerの削除
$SCRIPTDIR/_02_delete_secret_manager.sh $1 $2
# Cognitoの削除
$SCRIPTDIR/_03_delete_cognito.sh $1 $2

echo $RELEASE_ENV"の削除処理が完了しました。ゾーン:"$TARGET_ZONE