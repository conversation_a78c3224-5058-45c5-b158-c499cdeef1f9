#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

directory=$SCRIPTDIR/"targets/secretmanager"
TEXT_PREFIX=delete_target_$TARGET_ZONE

echo "Cognitoの初期化を行います。"

init_folder $directory

if [ -n "$AWS_END_POINT" ]; then
    echo "ローカル環境ではCognitoを扱っていないため処理をスキップします。"
    exit 0
fi

# Cognito からデータを取得してテキストファイルに出力
aws $AWS_END_POINT cognito-idp --region "$AWS_REGION" list-users --user-pool-id "$USER_POOL_ID" --query 'Users[*].Username' --output json | jq -r '.[]' > "$directory/${TEXT_PREFIX}.txt"

# テキストファイルを分割
split_text_file "$directory/${TEXT_PREFIX}.txt" "$directory"

# テキストファイルを処理
for file in "$directory"/*.txt; do
    if [ ! -e "$file" ]; then
        echo "削除対象がないためスキップします。"
        continue
    fi

    echo "${file}のデータを削除します"
    absolute_path="$(realpath "$file")"

    # 実施コマンド
    command="export AWS_PROFILE="$AWS_PROFILE"; \
    while read target; do \
    aws "$AWS_END_POINT" cognito-idp --region "$AWS_REGION" admin-delete-user --user-pool-id "$USER_POOL_ID" --username \$target > /dev/null; \
    done < "$absolute_path"; \
    exit"
    execute_command_in_terminal "$command"
done