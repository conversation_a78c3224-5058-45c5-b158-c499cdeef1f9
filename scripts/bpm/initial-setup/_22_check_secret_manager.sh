#!/bin/bash
function check_client_secret() {
    command=$1
    region=$2
    secret_id=$3
    client_secret=$4
    SECRET_MANAGER_VALUE=$($command 2>&1)
    get_status=$?
    if [[ $get_status -ne 0 ]]; then
        check_error "$SECRET_MANAGER_VALUE" "$secret_id"
        return
    fi
    arn=$(echo $SECRET_MANAGER_VALUE | jq -r ".ARN")
    name=$(echo $SECRET_MANAGER_VALUE | jq -r ".Name")
    secretstring=$(echo $SECRET_MANAGER_VALUE | jq -r ".SecretString")
    echo "ARN:"$arn
    echo "Name:"$name
    echo "SecretString:"$secretstring
    is_contained "$arn" "$region"
    arn_status=$?
    is_equal "$name" "$secret_id"
    name_status=$?
    is_contained "$secretstring" "$client_secret"
    secretstring_status=$?
    if [ "$arn_status" -eq 0 ] && [ "$name_status" -eq 0 ] && [ "$secretstring_status" -eq 0 ]; then
        echo "${secret_id}の取得結果は想定通りです。"
    else
        echo "${secret_id}の登録結果が想定外です。 ARN:"$(replace_status "$arn_status")" NAME:"$(replace_status "$name_status")" SecretString:"$(replace_status "$secretstring_status")" 取得コマンド:"$command""
    fi
}

function is_contained() {
  actual_value="$1"
  expected_value="$2"
  if [[ "$actual_value" == *"$expected_value"* ]]; then
      return 0
  else
      return 1
  fi
}
function is_equal() {
  actual_value="$1"
  expected_value="$2"
  if [ "$actual_value" = "$expected_value" ]; then
      return 0
  else
      return 1
  fi
}
function replace_status() {
    case $1 in
        0)
            echo "想定通り"
            ;;
        1)
            echo "想定外"
            ;;
        *)
            echo "想定外"
            ;;
    esac
}
function check_error() {
  ERROR_MESSAGE=$1
  TARGET_NAME=$2
  if [ -z "$1" ]; then
      #正常終了しているので何もしない
      :
  elif [[ $ERROR_MESSAGE == *"RemoveRegionsFromReplication"* ]]; then
      echo "レプリケートの解除に失敗しました。レプリケーションが行われていない環境の場合は問題ないため、確認をお願いします。対象:"$TARGET_NAME
  elif [[ $ERROR_MESSAGE == *"ResourceNotFoundException"* ]]; then
      echo "対象のSecretIdはSecretManagerに存在しません。対象:"$TARGET_NAME
  else
      # 想定外のエラーが発生した場合
      echo $ERROR_MESSAGE
      exit 1
  fi
}
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo "◾◾◾secretsmanager登録内容◾◾◾"
echo "◾バッチ接続用のclient_secret"
client_id=${BPM_BATCH_CLIENT_ID}
secret_id="dcjpy/bpm_batch/client_credentials/$client_id"
command="aws $AWS_END_POINT secretsmanager get-secret-value --secret-id $secret_id --region $AWS_REGION"
check_client_secret "$command" "$AWS_REGION" "$secret_id" "$BPM_BATCH_CLIENT_SECRET"

echo "◾Core接続用のclient_secret"
secret_id="dcjpy/bpm_server/oauth2/core/admin/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
command="aws $AWS_END_POINT secretsmanager get-secret-value --secret-id $secret_id --region $AWS_REGION"
check_client_secret "$command" "$AWS_REGION" "$secret_id" "$CORE_ADMIN_CLIENT_SECRET"

if [ $TARGET_ZONE = 'bpmfin' ] && [ "$RELEASE_ENV" != 'dev' ]; then
    echo "◾レプリケート先の登録内容確認"
    echo "◾バッチ接続用のclient_secret"
    client_id=${BPM_BATCH_CLIENT_ID}
    secret_id="dcjpy/bpm_batch/client_credentials/$client_id"
    command="aws $AWS_END_POINT secretsmanager get-secret-value --secret-id $secret_id --region $REPLICA_TARGET_REGION"
    check_client_secret "$command" "$REPLICA_TARGET_REGION" "$secret_id" "$BPM_BATCH_CLIENT_SECRET"

    echo "◾Core接続用のclient_secret"
    secret_id="dcjpy/bpm_server/oauth2/core/admin/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
    command="aws $AWS_END_POINT secretsmanager get-secret-value --secret-id $secret_id --region $REPLICA_TARGET_REGION"
    check_client_secret "$command" "$REPLICA_TARGET_REGION" "$secret_id" "$CORE_ADMIN_CLIENT_SECRET"
fi