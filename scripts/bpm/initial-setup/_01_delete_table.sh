#!/bin/bash
function delete_sequence() {
    sequence_name=$1
    local exit_status
    ERROR_MESSAGE=$(psql -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "DROP SEQUENCE $sequence_name;" 2>&1)
    exit_status=$?
    # コマンドが失敗した場合の処理
    if [ $exit_status -ne 0 ]; then
        check_error "$ERROR_MESSAGE" $sequence_name
    else
        echo $sequence_name"を削除しました。"
    fi
}

function check_error() {
    ERROR_MESSAGE=$1
    TARGET_NAME=$2
    if [ -z "$1" ]; then
          #正常終了しているので何もしない
          :
    elif [[ $ERROR_MESSAGE == *"does not exist"* ]]; then
        echo $TARGET_NAME"は削除済です。"
    else
        # 想定外のエラーが発生した場合
        echo $ERROR_MESSAGE
        exit 1
    fi
}

SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

$SCRIPTDIR/_port_forward.sh

echo "### TBLデータの削除を行います。 ###"

MESSAGE=$(psql -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "DO \$\$ DECLARE r RECORD; BEGIN FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP EXECUTE 'DROP TABLE IF EXISTS public.' || quote_ident(r.tablename) || ' CASCADE'; END LOOP; END \$\$;" 2>&1)
RESULT=$?
# パスワードの設定誤りなどでエラーが発生していないかを確認
if [ $RESULT -ne 0 ]; then
    check_error "$MESSAGE" "DROP TABLE"
fi
if [[ "$MESSAGE" == "DO" ]]; then
    echo "テーブルは全て削除済です。"
else
    echo "テーブルは全て削除しました。"
fi
MESSAGE=$(psql -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "DROP FUNCTION IF EXISTS public.next_dc_bank_sequence;" 2>&1)
if [[ $MESSAGE != "DROP FUNCTION" ]]; then
    check_error "$MESSAGE" "next_dc_bank_sequence"
fi

delete_sequence "seq_dc_bank_number"
delete_sequence "batch_step_execution_seq"
delete_sequence "batch_job_execution_seq"
delete_sequence "batch_job_seq"

echo "### TBLデータの削除が完了しました。 ###"

$SCRIPTDIR/_disconnect_port_forward.sh
