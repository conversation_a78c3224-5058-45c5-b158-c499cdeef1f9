#!/bin/bash
BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source "${BASE}/../../bin/common.sh"

# Error handling function
handle_error() {
    local error_line=${BASH_LINENO[0]}      # エラーが発生した行番号を取得
    local script_name=$(basename "$0")      # スクリプト名を取得
    $SCRIPTDIR/_disconnect_port_forward.sh
    message "err" "想定外のエラーが発生したため処理を中断します。"
    exit 1
}
trap 'handle_error' ERR

check_env() {
  TARGET_ZONE=$1
  RELEASE_ENV=$2
  if [ -z "$TARGET_ZONE" ]; then
      echo "リリース先のゾーンを指定してください。(bpmfin, bpmbiz)"
      exit
  fi
  if [ -z "$RELEASE_ENV" ]; then
      echo "リリース先の環境を指定してください。(prod, stage, dev, ...)"
      exit
  fi

  if [ -z "$AWS_PROFILE" ] && [ "$RELEASE_ENV" != 'local' ]; then
      echo "AWSプロファイルが未設定です。awspにてプロファイル指定をしてください。"
      exit
  fi

  if [ "$RELEASE_ENV" = 'local' ]; then
#      echo "リリース先の環境がローカルのため、awsコマンドにendpointを指定します。"
      if [[ "$TARGET_ZONE" == "bpmfin" ]]; then
          export AWS_END_POINT=" --endpoint-url=http://localhost:44566 "
      elif [[ "$TARGET_ZONE" == "bpmbiz" ]]; then
          export AWS_END_POINT=" --endpoint-url=http://localhost:54566 "
      else
          # bpmfin, bpmbiz以外の場合は何も設定しない(本来起こり得ない想定)
          export AWS_END_POINT=""
      fi
  else
      export AWS_END_POINT=""
  fi
}

# コマンドをターミナルで実行する関数
init_folder() {
    directory=$1
    # フォルダが存在するか確認
    if [ -d "$directory" ]; then
        # フォルダが存在する場合、内容を全て削除
        rm -rf "${directory:?}/"*
    else
        # フォルダが存在しない場合、新規作成
        mkdir -p "$directory"
    fi
}

# テキストファイルを分割する関数
split_text_file() {
    local target_file="$1"
    local directory="$2"
    split -l 1000 "$target_file" "$directory/${TEXT_PREFIX}_"

    for file in "$directory/${TEXT_PREFIX}_"*; do
        if [ ! -e "$file" ]; then
            continue
        fi
        mv "$file" "$file.txt"
    done
    rm "$target_file"
}

# コマンドをターミナルで実行する関数
execute_command_in_terminal() {
    local command="$1"
    osascript <<EOF
tell application "Terminal"
    do script "$command"
end tell
EOF
}
