#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

$SCRIPTDIR/_port_forward.sh

echo "◾TBLの作成件数の確認"
output=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "\dt" 2>&1)
# テーブル行のみに絞り込み、行数をカウント
table_count=$(echo "$output" | awk '/^ public \|/{count++} END {print count}')
if [[ $table_count == 53 ]]; then
    echo "作成したテーブルの件数について想定通りです。("$table_count"件)"
else
    echo "テーブルの作成件数が想定外になっています。("$table_count"件) 右記の実行結果を確認してください。 psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "\dt""
fi
echo "◾シーケンスの初期値確認"
result=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select last_value from seq_dc_bank_number" 2>&1)
# 取得した値を変数に設定（前後の空白も除去）
value=$(echo "$result" | xargs)
if [[ $value == "********" ]]; then
    echo "シーケンス(dc_bank_number)の初期値は想定通りです。($value)"
else
    echo "シーケンス(dc_bank_number)の初期値が想定外です。($value) 右記の実行結果を確認してください。 psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select last_value from seq_dc_bank_number";"
fi

echo "◾service_admin登録内容"
result=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select service_id, admin_id from service_admin where service_id = '0'" 2>&1)
result_service_id=$(echo "$result" | cut -d '|' -f 1 | xargs)
result_admin_id=$(echo "$result" | cut -d '|' -f 2 | xargs)
if [[ $result_service_id == $SERVICE_ID ]] && [[ $result_admin_id == "zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz" ]]; then
    echo "service_admin登録内容は想定通りです。(service_id=$result_service_id, admin_id=$result_admin_id)"
else
    echo "service_admin登録内容が想定外です。期待値:service_id="$SERVICE_ID", admin_id=zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz、現在の登録値:service_id="$result_service_id", admin_id="$result_admin_id""
fi

echo "◾auth_client登録内容"
result=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select entity_id, entity_type, client_id from auth_client where entity_id = 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz'" 2>&1)
result_entity_id=$(echo "$result" | cut -d '|' -f 1 | xargs)
result_entity_type=$(echo "$result" | cut -d '|' -f 2 | xargs)
result_client_id=$(echo "$result" | cut -d '|' -f 3 | xargs)
if [[ $result_entity_id == "zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz" ]] && [[ $result_entity_type == "admin" ]] && [[ $result_client_id == $CORE_ADMIN_CLIENT_ID ]]; then
    echo "service_admin登録内容は想定通りです。(entity_id="$result_entity_id", entity_type="$result_entity_type", client_id="$result_client_id")"
else
    echo "service_admin登録内容が想定外です。期待値:entity_id=zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz, entity_type=admin, client_id="$CORE_ADMIN_CLIENT_ID"、現在の登録値:entity_id="$result_entity_id", entity_type="$result_entity_type", client_id="$result_client_id""
fi

$SCRIPTDIR/_disconnect_port_forward.sh
