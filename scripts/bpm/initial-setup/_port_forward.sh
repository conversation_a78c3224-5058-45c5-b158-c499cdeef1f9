#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)

is_api_port_forward=$1

if [ -z "$AWS_PROFILE" ] || [ "$RELEASE_ENV" = 'local' ]; then
  exit 0
fi

echo "◾ポートフォワードを開始します。"
export BASTION_ID=`aws ec2 describe-instances --region ap-northeast-1 --filters "Name=tag-key,Values=Name" "Name=tag-value,Values="${AWS_PROFILE}"-tokyo-ec2-bastion" | jq -r '.Reservations[0].Instances[0].InstanceId'`
export RDSHOST=`aws secretsmanager get-secret-value --region ap-northeast-1 --secret-id ${AWS_PROFILE}"-rds-credentials" | jq -r .SecretString | jq -r .host`
aws ssm start-session --region ap-northeast-1 --target ${BASTION_ID} --document-name AWS-StartPortForwardingSessionToRemoteHost --parameters '{"portNumber":["5432"], "localPortNumber":["5432"], "host":["'${RDSHOST}'"]}' > /dev/null 2>&1 &
sleep 1

if [ is_api_port_forward = true ] && ["$RELEASE_ENV" = 'prod']; then
  export INSTANCE_ID=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=*bastion" --query "Reservations[].Instances[].InstanceId" --output text)
  export ALB_DNS_NAME=$(aws elbv2 describe-load-balancers --query "LoadBalancers[?contains(LoadBalancerName, 'bpm-alb')].DNSName" --output text)
  aws ssm start-session --target "${INSTANCE_ID}" --document-name AWS-StartPortForwardingSessionToRemoteHost --parameters "{\"portNumber\":[\"443\"], \"localPortNumber\":[\"1080\"], \"host\":[\"${ALB_DNS_NAME}\"]}" > /dev/null 2>&1 &
  sleep 1
fi
echo "ポートフォワードのため5秒待機します。"
sleep 5
