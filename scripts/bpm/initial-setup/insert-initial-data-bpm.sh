#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
#対象のZONE名と環境名を取得
source $SCRIPTDIR/_set_env_zone.sh
# 環境の確認
check_env $TARGET_ZONE $RELEASE_ENV
source $SCRIPTDIR/env/$TARGET_ZONE.env

echo $RELEASE_ENV"の登録処理を開始します。ゾーン:"$TARGET_ZONE

# テーブルの作成
$SCRIPTDIR/_11_create_table.sh $TARGET_ZONE $RELEASE_ENV
# テーブルデータの登録
$SCRIPTDIR/_12_insert_data.sh $TARGET_ZONE $RELEASE_ENV
# SecretManagerの登録
$SCRIPTDIR/_13_create_secret_manager.sh $TARGET_ZONE $RELEASE_ENV

echo $RELEASE_ENV"の登録処理が完了しました。ゾーン:"$TARGET_ZONE