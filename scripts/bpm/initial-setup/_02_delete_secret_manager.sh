#!/bin/bash
# scretmnagerからの削除
delete_secret() {
    local secret_id="$1"
    local error_message
    local exit_status
    error_message=$(aws $AWS_END_POINT --region "$AWS_REGION" secretsmanager delete-secret --secret-id $secret_id --force-delete-without-recovery 2>&1)
    exit_status=$?
    # コマンドが失敗した場合の処理
    if [ $exit_status -ne 0 ]; then
      check_error "$error_message" "$secret_id"
    fi
}

# レプリケーションからのリージョン削除
function remove_regions_from_replication() {
    local secret_id="$1"
    local error_message
    local exit_status
    error_message=$(aws $AWS_END_POINT --region "$AWS_REGION" secretsmanager remove-regions-from-replication --secret-id "$secret_id" --remove-replica-regions "$REPLICA_TARGET_REGION" 2>&1)
    exit_status=$?
    # コマンドが失敗した場合の処理
    if [ $exit_status -ne 0 ]; then
      check_error "$error_message" "$secret_id"
    fi
}

function check_error() {
  ERROR_MESSAGE=$1
  TARGET_NAME=$2
  if [ -z "$1" ]; then
      #正常終了しているので何もしない
      :
  elif [[ $ERROR_MESSAGE == *"RemoveRegionsFromReplication"* ]]; then
      echo "レプリケートの解除に失敗しました。レプリケーションが行われていない環境の場合は問題ないため、確認をお願いします。対象:"$TARGET_NAME
  elif [[ $ERROR_MESSAGE == *"ResourceNotFoundException"* ]]; then
      echo "対象のSecretIdはSecretManagerに存在しません。対象:"$TARGET_NAME
  else
      # 想定外のエラーが発生した場合
      echo $ERROR_MESSAGE
      exit 1
  fi
}

SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

directory=$SCRIPTDIR/"targets/secretmanager"
TEXT_PREFIX=delete_target_$TARGET_ZONE

echo "### secretsmanagerの初期化を行います。 ###"

init_folder $directory

echo "業務で作成されたデータを secretsmanager から削除します。"

# AWSからデータを取得してテキストファイルに出力
aws $AWS_END_POINT secretsmanager list-secrets --region "${AWS_REGION}" --filter "Key=name,Values=dcjpy/bpm_server/token/" | jq -r ".SecretList[].Name" > "$directory/${TEXT_PREFIX}.txt"
aws $AWS_END_POINT secretsmanager list-secrets --region "${AWS_REGION}" --filter "Key=name,Values=dcjpy/bpm_server/signer/" | jq -r ".SecretList[].Name" >> "$directory/${TEXT_PREFIX}.txt"
aws $AWS_END_POINT secretsmanager list-secrets --region "${AWS_REGION}" --filter "Key=name,Values=dcjpy/bank_gw/ib_token/" | jq -r ".SecretList[].Name" >> "$directory/${TEXT_PREFIX}.txt"
aws $AWS_END_POINT secretsmanager list-secrets --region "${AWS_REGION}" --filter "Key=name,Values=dcjpy/bpm_server/authority/" | jq -r ".SecretList[].Name" >> "$directory/${TEXT_PREFIX}.txt"

# テキストファイルを分割
split_text_file "$directory/${TEXT_PREFIX}.txt" "$directory"

# テキストファイルを処理
for file in "$directory"/*.txt; do
    if [ ! -e "$file" ]; then
        echo "削除対象がないためスキップします。"
        continue
    fi

    echo "${file}のデータを削除します。新規ウィンドウを起動します。"
    absolute_path="$(realpath "$file")"

    # 実施コマンド
    command="export AWS_PROFILE="$AWS_PROFILE"; \
     while read target; do \
     if [ $TARGET_ZONE = 'bpmfin' ] && [ "$RELEASE_ENV" != 'dev' ]; then \
          aws "$AWS_END_POINT" secretsmanager --region "$AWS_REGION" remove-regions-from-replication --secret-id \$target --remove-replica-regions "$REPLICA_TARGET_REGION" > /dev/null; \
     fi; \
     aws "$AWS_END_POINT" secretsmanager --region "$AWS_REGION" delete-secret --secret-id \$target --force-delete-without-recovery > /dev/null; \
     done < "$absolute_path"; \
     exit"
    execute_command_in_terminal "$command"
done

echo "初期構築で登録したデータを削除します。"
if [ $TARGET_ZONE = 'bpmfin' ] && [ "$RELEASE_ENV" != 'dev' ]; then
    # レプリケートの解除（dev環境など、レプリケートが存在しない環境ではエラーになる）
    remove_regions_from_replication "dcjpy/bpm_batch/client_credentials/${BPM_BATCH_CLIENT_ID}"
    remove_regions_from_replication "dcjpy/bpm_server/oauth2/core/admin/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
fi

# secretsmanagerの削除
delete_secret "dcjpy/bpm_batch/client_credentials/${BPM_BATCH_CLIENT_ID}"
delete_secret "dcjpy/bpm_server/oauth2/core/admin/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"

echo "### secretsmanagerの初期化が完了しました。 ###"