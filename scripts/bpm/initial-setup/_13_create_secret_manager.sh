#!/bin/bash
function create_secret() {
    name="$1"
    client_secret_value="$2"
    local exit_status
    ERROR_MESSAGE=$(aws $AWS_END_POINT secretsmanager create-secret --region ${AWS_REGION} --name "$name" --kms-key-id "alias/app-resource-encryption-key"  --secret-string '{"client_secret" : "'$client_secret_value'"}' 2>&1)
    exit_status=$?
    if  [ $exit_status -ne 0 ]; then
        check_error "$ERROR_MESSAGE" "$name"
    else
        :
    fi
}

function replication_secret_to_regions() {
    secret_id="$1"
    local exit_status
    ERROR_MESSAGE=$(aws $AWS_END_POINT secretsmanager replicate-secret-to-regions --secret-id $secret_id --add-replica-regions Region=${REPLICA_TARGET_REGION},KmsKeyId="alias/app-resource-encryption-key" 2>&1)
    exit_status=$?
    if  [ $exit_status -ne 0 ]; then
        check_error "$ERROR_MESSAGE" "$secret_id"
    else
        :
    fi
}

function check_error() {
    ERROR_MESSAGE="$1"
    target="$2"
    if [ -z "$ERROR_MESSAGE" ]; then
          #正常終了しているので何もしない
          :
    elif [[ "$ERROR_MESSAGE" == *"already exists"* ]]; then
        echo $target"は登録済です。"
    elif [[ "$ERROR_MESSAGE" == *"duplicate key value"* ]]; then
        echo $target"は登録済です。"
    elif [[ $ERROR_MESSAGE == *"ReplicateSecretToRegions"* ]]; then
        echo "レプリケートの設定に失敗しました。レプリケーションが行われていない環境の場合は問題ないため、確認をお願いします。対象:"$target
    else
        # 想定外のエラーが発生した場合
        echo "$ERROR_MESSAGE"
        exit 1
    fi
}
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh

# 引数がない場合、AWS_PROFILEから取得
if [ $# -eq 0 ]; then
  source $SCRIPTDIR/_set_env_zone.sh
  set -- $TARGET_ZONE $RELEASE_ENV
fi

check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo "secretsmanagerの登録を行います。"

create_secret "dcjpy/bpm_batch/client_credentials/${BPM_BATCH_CLIENT_ID}" ${BPM_BATCH_CLIENT_SECRET}
create_secret "dcjpy/bpm_server/oauth2/core/admin/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz" ${CORE_ADMIN_CLIENT_SECRET}
echo "secretsmanagerの登録が完了しました。"

if [ $TARGET_ZONE = 'bpmfin' ] && [ "$RELEASE_ENV" != 'dev' ]; then
    echo "secretsmanagerのレプリケートを行います。"
    replication_secret_to_regions "dcjpy/bpm_batch/client_credentials/${BPM_BATCH_CLIENT_ID}"
    replication_secret_to_regions "dcjpy/bpm_server/oauth2/core/admin/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
    echo "secretsmanagerのレプリケートが完了しました。"
fi
