#!/bin/bash
function do_sql_file() {
    sql="$1"
    echo "SQLファイルを実行します。対象:"$sql
    local exit_status
    ERROR_MESSAGE=$(psql -f $sql -U $USER -d $DB_NAME -p $DB_PORT -h localhost 2>&1)
    exit_status=$?
    if [[ "$ERROR_MESSAGE" == "CREATE FUNCTION" ]]; then
        # 関数登録は重複実行してもエラーにならないためハンドリング
        :
    elif [ $exit_status -ne 0 ]; then
        check_error "$ERROR_MESSAGE" "$sql"
    else
        :
    fi
}

function check_error() {
    ERROR_MESSAGE="$1"
    target="$2"
    if [ -z "$ERROR_MESSAGE" ]; then
          #正常終了しているので何もしない
          :
    elif [[ "$ERROR_MESSAGE" == *"already exists"* ]]; then
        echo $target"は登録済です。"
    elif [[ "$ERROR_MESSAGE" == *"duplicate key value"* ]]; then
        echo $target"は登録済です。"
    elif [[ $ERROR_MESSAGE == *"ReplicateSecretToRegions"* ]]; then
        echo "レプリケートの設定に失敗しました。レプリケーションが行われていない環境の場合は問題ないため、確認をお願いします。対象:"$target
    else
        # 想定外のエラーが発生した場合
        echo "$ERROR_MESSAGE"
        exit 1
    fi
}
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh

# 引数がない場合、AWS_PROFILEから取得
if [ $# -eq 0 ]; then
  source $SCRIPTDIR/_set_env_zone.sh
  set -- $TARGET_ZONE $RELEASE_ENV
fi

check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

$SCRIPTDIR/_port_forward.sh

migration_folder=../../../../dcbg-dcjpy-bpm-db-migration/er
echo "TBL作成を行います。"
do_sql_file $migration_folder/V001_bpm-server.sql
echo "バッチで使用するTBL作成を行います。"
do_sql_file $migration_folder/V002_bpm-batch.sql
echo "マスタデータの登録を行います。"
do_sql_file $migration_folder/V101_init_master-data.sql
echo "関数の登録を行います。"
do_sql_file $migration_folder/V201_init_functions.sql

$SCRIPTDIR/_disconnect_port_forward.sh
