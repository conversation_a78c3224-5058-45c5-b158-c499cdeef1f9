#!/bin/bash

# 環境名とZONEを初期化
RELEASE_ENV=""
TARGET_ZONE=""

# fin または biz のチェック
if [[ "$AWS_PROFILE" == *"bpmfin"* ]]; then
    TARGET_ZONE="bpmfin"
elif [[ "$AWS_PROFILE" == *"bpmbiz"* ]]; then
    TARGET_ZONE="bpmbiz"
elif [[ "$AWS_PROFILE" == *"fin"* ]]; then
    TARGET_ZONE="fin"
elif [[ "$AWS_PROFILE" == *"biz"* ]]; then
    TARGET_ZONE="biz"
else
  echo "AWS_PROFILEからZONEを取得できませんでした。手動でZONEを入力してください。"
  read TARGET_ZONE
fi

# 環境名の判定
if [[ "$AWS_PROFILE" == *"dev"* ]]; then
    RELEASE_ENV="dev"
elif [[ "$AWS_PROFILE" == *"sandbox"* ]]; then
    RELEASE_ENV="sandbox"
elif [[ "$AWS_PROFILE" == *"prod"* ]]; then
    RELEASE_ENV="prod"
elif [[ "$AWS_PROFILE" == *"stage"* ]]; then
    RELEASE_ENV="stage"
else
  echo "AWS_PROFILEから環境名を取得できませんでした。手動で環境名を入力してください。"
  read RELEASE_ENV
fi

# 環境変数をエクスポート
export RELEASE_ENV
export TARGET_ZONE
