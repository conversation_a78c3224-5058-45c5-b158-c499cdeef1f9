#!/bin/bash
function do_sql() {
    sql="$1"
    local exit_status
    ERROR_MESSAGE=$(psql -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "$sql" 2>&1)
    exit_status=$?
    # コマンドが失敗した場合の処理
    if [ $exit_status -ne 0 ]; then
        check_error "$ERROR_MESSAGE" "$sql"
    else
        :
    fi
}
function check_error() {
    ERROR_MESSAGE="$1"
    target="$2"
    if [ -z "$ERROR_MESSAGE" ]; then
          #正常終了しているので何もしない
          :
    elif [[ "$ERROR_MESSAGE" == *"already exists"* ]]; then
        echo $target"は登録済です。"
    elif [[ "$ERROR_MESSAGE" == *"duplicate key value"* ]]; then
        echo $target"は登録済です。"
    elif [[ $ERROR_MESSAGE == *"ReplicateSecretToRegions"* ]]; then
        echo "レプリケートの設定に失敗しました。レプリケーションが行われていない環境の場合は問題ないため、確認をお願いします。対象:"$target
    else
        # 想定外のエラーが発生した場合
        echo "$ERROR_MESSAGE"
        exit 1
    fi
}
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh

# 引数がない場合、AWS_PROFILEから取得
if [ $# -eq 0 ]; then
  source $SCRIPTDIR/_set_env_zone.sh
  set -- $TARGET_ZONE $RELEASE_ENV
fi

check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

$SCRIPTDIR/_port_forward.sh

echo "service_admin、auth_clientの初期データを登録します。"
do_sql "INSERT INTO service_admin (service_id, admin_id) VALUES ('0', 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz')"
do_sql "INSERT INTO auth_client (entity_id, entity_type, client_id) VALUES('zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz', 'admin', '"$CORE_ADMIN_CLIENT_ID"')"

$SCRIPTDIR/_disconnect_port_forward.sh
