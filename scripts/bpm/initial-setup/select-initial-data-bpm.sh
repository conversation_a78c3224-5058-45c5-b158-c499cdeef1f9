#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
# 環境の確認
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo "◾◾◾取得スクリプト開始◾◾◾"
echo $RELEASE_ENV"の取得処理を開始します。ゾーン:"$TARGET_ZONE

# テーブルデータの取得と確認
$SCRIPTDIR/_21_check_db_data.sh $1 $2
# SecretManagerの取得と確認
$SCRIPTDIR/_22_check_secret_manager.sh $1 $2

echo $RELEASE_ENV"の取得処理が完了しました。ゾーン:"$TARGET_ZONE
echo "◾◾◾取得スクリプト終了◾◾◾"
