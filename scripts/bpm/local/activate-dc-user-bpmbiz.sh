#!/bin/bash
# 初期設定待ちのユーザをアクティブに変更します
# 初期設定待ち以外の状態だったら、スクリプトは終了

# 引数チェック
if [ $# -le 1 ]; then
    echo "[USAGE] ./activate-dc-user-biz.sh <sign_in_id> <password> [ <service_id> ]"
    exit 9
fi

BIZ_SIGN_IN_ID=$1
BIZ_PASSWORD=$2

if [ "$#" -eq 3 ]; then
    SERVICE_ID=$3
    CUSTOM_HEADER="-H X-DCJPY-SERVICE-ID:${SERVICE_ID}"
else
    SERVICE_ID="0"
    CUSTOM_HEADER=""
fi

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/bpmbiz.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi


#初回サインイン
echo "初回サインイン"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_in -d @- <<EOF
{"sign_in_id": "${BIZ_SIGN_IN_ID}", "password": "${BIZ_PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

BIZ_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

if [ $BIZ_ACCESS_TOKEN = null ]; then
    echo "初回サインイン : 失敗しました "$BIZ_SIGN_IN_ID
    exit 1
fi

#パスワード変更
echo "パスワード変更"
PASSWORD_RESULT=$(curl -sS -X PUT -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_user/password -d @- <<EOF
{"password": "${BIZ_PASSWORD}", "new_password": "Password1"}
EOF
)
echo ${PASSWORD_RESULT} | jq .
