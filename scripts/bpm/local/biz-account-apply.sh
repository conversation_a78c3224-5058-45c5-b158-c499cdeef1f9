#!/bin/bash
# FinZoneでBizZoneのアカウントの利用確定を行います

if [ $# -le 2 ]; then
    echo "[USAGE] ./biz-account-apply.sh <FIN_SIGN_IN_ID> <FIN_PASSWORD> <TOTP_SECRET> [ <SERVICE_ID> ]"
    exit 9
fi

FIN_SIGN_IN_ID=$1
FIN_PASSWORD=$2
TOTP_SECRET=$3

if [ "$#" -eq 4 ]; then
    SERVICE_ID=$4
    CUSTOM_HEADER="-H X-DCJPY-SERVICE-ID:${SERVICE_ID}"
else
    SERVICE_ID="0"
    SERVICE_ID=0
fi

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/bpmfin.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ./env/.env ]; then
  source ./env/.env
fi

# 04. (認証アプリ) サインイン (TOTPあり)
echo "04.(認証アプリ) サインイン (TOTPあり)開始"
TOTP=$(oathtool --totp -b $TOTP_SECRET)

SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/auth/sign_in -d @- <<EOF
{"sign_in_id": "${FIN_SIGN_IN_ID}", "password": "${FIN_PASSWORD}", "totp": "${TOTP}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

FIN_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)
if [ $FIN_ACCESS_TOKEN = null ]; then
    echo "04. (認証アプリ) サインイン (TOTPあり) : 失敗しました"
    exit 1
fi

#  ビジネスゾーンアカウント利用確定
echo "ビジネスゾーンアカウント利用確定実行"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/holders/business/apply -d @- <<EOF
{"zone_id": "3001"}
EOF
)
echo ${RESULT} | jq .
