#!/bin/bash
# FinZone用のアカウントを作成します
# アカウント作成後にそのまま紐づくユーザの作成も可能です

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/bpmfin.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ./env/.env ]; then
  source ./env/.env
fi

# 電話番号が未指定の場合はランダムで生成する
if [ -z "${PHONE_NUMBER}" ]; then
  PHONE_NUMBER=$(bash -c 'printf "090%08d" $(( RANDOM % 10000 ))$(( RANDOM % 10000 ))')
fi

if [ "$#" -eq 1 ]; then
    SERVICE_ID=$1
    CUSTOM_HEADER="-H X-DCJPY-SERVICE-ID:${SERVICE_ID}"
else
    SERVICE_ID="0"
    CUSTOM_HEADER=""
fi

# 01. サインアップ実施
echo "01. サインアップ開始"
SIGN_UP_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_up)
echo ${SIGN_UP_RESULT} | jq .

FIN_SIGN_IN_ID=$(echo ${SIGN_UP_RESULT} | jq -r .sign_in_id)
FIN_PASSWORD=$(echo ${SIGN_UP_RESULT} | jq -r .temporary_password)

if [ $FIN_SIGN_IN_ID = null ]; then
    echo "01. サインアップ実施結果 : 失敗しました"
    exit 1
fi


# 02. 認証アプリ初回サインイン (TOTPなし)
echo "02. 認証アプリ初回サインイン (TOTPなし)"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/auth/sign_in -d @- <<EOF
{"sign_in_id": "${FIN_SIGN_IN_ID}", "password": "${FIN_PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

FIN_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

if [ $FIN_ACCESS_TOKEN = null ]; then
    echo "02. 認証アプリ初回サインイン (TOTPなし) : 失敗しました"
    exit 1
fi


# 03. 認証アプリ電話番号登録
echo "03. 認証アプリ電話番号登録開始"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/auth/my_user/telephone -d @- <<EOF
{"phone_number": "${PHONE_NUMBER}"}
EOF
)
echo ${RESULT} | jq .

# CognitoLocalの電話番号を検証済の状態へ更新する
COGNITO_PHONE_NUMBER='+81'${PHONE_NUMBER:1}
COGNITO_USER_NAME='dc_user_'$FIN_SIGN_IN_ID

aws cognito-idp admin-update-user-attributes \
    --endpoint-url ${COGNITO_LOCAL_ENDPOINT} \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --user-attributes Name=phone_number,Value=${COGNITO_PHONE_NUMBER} Name=phone_number_verified,Value=true

# パスワードの強制変更
aws cognito-idp admin-set-user-password \
    --endpoint-url ${COGNITO_LOCAL_ENDPOINT} \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --password $PASSWORD --permanent

# TOTP鍵の生成
RANDOM_BYTES=$(openssl rand -base64 40 | head -c 40 | base64 -d | xxd -p)
TOTP_SECRET=$(echo $RANDOM_BYTES | xxd -r -p | base32)

# BPM DBへの登録
SQL_RESPONSE1=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -t -c \
  "INSERT INTO dc_user_phone (sign_in_id, phone_number) VALUES ('$FIN_SIGN_IN_ID','$PHONE_NUMBER')")
SQL_RESPONSE2=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -t -c \
  "INSERT INTO dc_user_totp (sign_in_id, totp_secret) VALUES ('$FIN_SIGN_IN_ID','$TOTP_SECRET')")
SQL_RESPONSE3=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -t -c \
  "UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = '$FIN_SIGN_IN_ID'")

# 04. (認証アプリ) サインイン (TOTPあり)
echo "04.(認証アプリ) サインイン (TOTPあり)開始"
TOTP=$(oathtool --totp -b $TOTP_SECRET)

SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/auth/sign_in -d @- <<EOF
{"sign_in_id": "${FIN_SIGN_IN_ID}", "password": "${PASSWORD}", "totp": "${TOTP}"}
EOF
)
echo "04. (認証アプリ) サインイン (TOTPあり)結果"
echo ${SIGN_IN_RESULT} | jq .

FIN_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)
FIN_ID_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .id_token)
if [ $FIN_ACCESS_TOKEN = null ]; then
    echo "04. (認証アプリ) サインイン (TOTPあり) : 失敗しました"
    exit 1
fi

# 05. IB認証開始
echo "05. IB認証開始"
REDIRECT_URI=http://xxxxxx.com/callback
RESULT=$(curl -i -sS -X POST -H "Content-Type: application/x-www-form-urlencoded" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/ib/auth/prepare \
  -d "id_token=${FIN_ID_TOKEN}&redirect_uri=${REDIRECT_URI}" \
)
echo ${RESULT} | grep "location:"

# Stateの取得
OAUTH_STATE=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -tA -c \
  "SELECT oauth_state FROM dc_user_oauth_state WHERE sign_in_id='$FIN_SIGN_IN_ID'")
STATE=$(echo "$OAUTH_STATE" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# 06. IB認可コード連携
echo "06. IB認可コード連携開始"
RESULT=$(curl -sS -X GET -H "Content-Type: application/x-www-form-urlencoded" $CUSTOM_HEADER \
  "${BPM_BASE_URL}/holders/ib/auth/complete?code=dummyValue&state=${STATE}&redirect_uri=${REDIRECT_URI}"
)

# 07. DC口座開設
echo "07. DC口座開設"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_account/bank_account )
echo ${RESULT} | jq .
DC_BANK_NUMBER=$(echo ${RESULT} | jq -r .dc_bank_number)

echo ""
echo "========================================================================"
echo " FinZone DCアカウント作成結果"
echo "    SIGN_IN_ID     : "$FIN_SIGN_IN_ID
echo "    PASSWORD       : "$PASSWORD
echo "    TOTP_SECRET    : "$TOTP_SECRET
echo "    DC_BANK_NUMBER : "$DC_BANK_NUMBER
echo "    PHONE_NUMBER   : "$PHONE_NUMBER
echo ""
echo "    FIN_ACCESS_TOKEN : "$FIN_ACCESS_TOKEN
echo ""
echo "========================================================================"

echo ""
echo "このまま作成したアカウントに紐づくユーザを作成しますか、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

if [ "${SERVICE_ID}" = "0" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-************
  APPROVER_ROLE_ID=********-0000-4000-0000-********0013
  OPERATOR_ROLE_ID=********-0000-4000-0000-********0012
elif [ "${SERVICE_ID}" = "0d367be2" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-0**********1
  APPROVER_ROLE_ID=********-0000-4000-0000-0**********3
  OPERATOR_ROLE_ID=********-0000-4000-0000-0**********2
elif [ "${SERVICE_ID}" = "43e4abef" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-**********11
  APPROVER_ROLE_ID=********-0000-4000-0000-**********13
  OPERATOR_ROLE_ID=********-0000-4000-0000-**********12
elif [ "${SERVICE_ID}" = "9946abd1" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-********2111
  APPROVER_ROLE_ID=********-0000-4000-0000-********2113
  OPERATOR_ROLE_ID=********-0000-4000-0000-********2112
fi


# ユーザ作成(ユーザ管理者)
USER_OWNER_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/users -d @- <<EOF
{"user_name": "${USER_OWNER_NAME}", "role_id": "${USER_OWNER_ROLE_ID}"}
EOF
)

USER_OWNER_SIGN_IN_ID=$(echo ${USER_OWNER_RESULT} | jq -r .sign_in_id)
USER_OWNER_TEMPORARY_PASSWORD=$(echo ${USER_OWNER_RESULT} | jq -r .temporary_password)
USER_OWNER_USER_NAME=$(echo ${USER_OWNER_RESULT} | jq -r .user_name)

# ユーザ作成(承認者)
APPROVER_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/users -d @- <<EOF
{"user_name": "${APPROVER_NAME}", "role_id": "${APPROVER_ROLE_ID}"}
EOF
)

APPROVER_SIGN_IN_ID=$(echo ${APPROVER_RESULT} | jq -r .sign_in_id)
APPROVER_TEMPORARY_PASSWORD=$(echo ${APPROVER_RESULT} | jq -r .temporary_password)
APPROVER_USER_NAME=$(echo ${APPROVER_RESULT} | jq -r .user_name)

# ユーザ作成(担当者)
OPERATOR_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER  \
  ${BPM_BASE_URL}/holders/users -d @- <<EOF
{"user_name": "${OPERATOR_NAME}", "role_id": "${OPERATOR_ROLE_ID}"}
EOF
)

OPERATOR_SIGN_IN_ID=$(echo ${OPERATOR_RESULT} | jq -r .sign_in_id)
OPERATOR_TEMPORARY_PASSWORD=$(echo ${OPERATOR_RESULT} | jq -r .temporary_password)
OPERATOR_USER_NAME=$(echo ${OPERATOR_RESULT} | jq -r .user_name)

USER_OWNER_PASSWORD=Password1
APPROVER_PASSWORD=Password1
OPERATOR_PASSWORD=Password1

echo ""
echo " ユーザ管理者"
echo "    USER_OWNER_SIGN_IN_ID : "$USER_OWNER_SIGN_IN_ID
echo "    USER_OWNER_PASSWORD   : Password1"
echo "    USER_OWNER_USER_NAME  : "$USER_OWNER_USER_NAME
#ユーザをアクティブへ更新
if [ "${SERVICE_ID}" = "0" ]; then
  ./activate-dc-user-bpmfin.sh $USER_OWNER_SIGN_IN_ID $USER_OWNER_TEMPORARY_PASSWORD
else
  ./activate-dc-user-bpmfin.sh $USER_OWNER_SIGN_IN_ID $USER_OWNER_TEMPORARY_PASSWORD $SERVICE_ID
fi

echo "  業務承認者"
echo "    APPROVER_SIGN_IN_ID   : "$APPROVER_SIGN_IN_ID
echo "    APPROVER_PASSWORD     : Password1 "
echo "    APPROVER_USER_NAME    : "$APPROVER_USER_NAME
#ユーザをアクティブへ更新
if [ "${SERVICE_ID}" = "0" ]; then
  ./activate-dc-user-bpmfin.sh $APPROVER_SIGN_IN_ID $APPROVER_TEMPORARY_PASSWORD
else
  ./activate-dc-user-bpmfin.sh $APPROVER_SIGN_IN_ID $APPROVER_TEMPORARY_PASSWORD $SERVICE_ID
fi

echo "  業務担当者"
echo "    OPERATOR_SIGN_IN_ID   : "$OPERATOR_SIGN_IN_ID
echo "    OPERATOR_PASSWORD     : Password1"
echo "    OPERATOR_USER_NAME    : "$OPERATOR_USER_NAME
#ユーザをアクティブへ更新
if [ "${SERVICE_ID}" = "0" ]; then
  ./activate-dc-user-bpmfin.sh $OPERATOR_SIGN_IN_ID $OPERATOR_TEMPORARY_PASSWORD
else
  ./activate-dc-user-bpmfin.sh $OPERATOR_SIGN_IN_ID $OPERATOR_TEMPORARY_PASSWORD $SERVICE_ID
fi
echo ""
echo "========================================================================"
echo ""

echo ""
echo "このまま作成したアカウントに紐づく BizZone のDCアカウントを作成しますか、続行する場合はyを入力してください"
echo "【注意】事前に BizZone のバリデータにアカウントを紐づける必要があります。"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

if [ "${SERVICE_ID}" = "0" ]; then
    ./create-account-bpmbiz.sh $FIN_SIGN_IN_ID $PASSWORD $TOTP_SECRET
else
    ./create-account-bpmbiz.sh $FIN_SIGN_IN_ID $PASSWORD $TOTP_SECRET $SERVICE_ID
fi
