#!/bin/bash

# BPM 初期構築スクリプト
# 1. Core Admin API を呼び出すための 接続情報登録
#   a.BPM DB の初期データ登録
#     - service_admin
#     - auth_client
#   b. BPM Secret Manager への登録
# 2. イシュア登録 (FinZone のみ)
# 3. バリデータ登録
# 4. サービスオーナ作成
# 5. 銀行/事業者 管理ユーザ登録
# 以下の要件を満たすデータを作成
# https://decurret.atlassian.net/wiki/spaces/DIG/pages/3271163973/IT#%E3%83%AD%E3%83%BC%E3%82%AB%E3%83%ABIT%E6%99%82%E3%81%AE%E6%A7%8B%E6%88%90

# 引数チェック
if [ $# -eq 0 ]; then
    echo "[USAGE] ./initialize-bpm.sh < bpmfin | bpmbiz > MULTI_TENANT_FLAG"
    exit 9
fi

# エラーが発生したら、直ちに終了する
set -e

ZONE_TYPE=$1
BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/${ZONE_TYPE}.env

if [ $# -eq 2 ]; then
    CUSTOM_HEADER_SERVICE_ID1="X-DCJPY-SERVICE-ID: ${SERVICE_ID1}"
    CUSTOM_HEADER_SERVICE_ID2="X-DCJPY-SERVICE-ID: ${SERVICE_ID2}"
    CUSTOM_HEADER_SERVICE_ID3="X-DCJPY-SERVICE-ID: ${SERVICE_ID3}"
    MULTI_TENANT_FLAG=true
else
    CUSTOM_HEADER_SERVICE_ID1=""
    SERVICE_ID1="0"
    MULTI_TENANT_FLAG=false
fi

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi

echo "BPM 初期構築を開始します"

function post_secret_manager() {
  local secret_id=$1
  local secret_json=$2
  aws --endpoint-url=${LOCALSTACK_URL} \
    secretsmanager create-secret  \
    --region ${AWS_REGION} \
    --name ${secret_id}  \
    --secret-string "${secret_json}"
}

# ADMIN 情報の登録
RESULT=$(psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -tA -c \
  "SELECT 1 FROM service_admin WHERE service_id = '$SERVICE_ID1'" \
  | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

if [ -z "${RESULT}" ]; then

  ./create-master-db.sh $ZONE_TYPE $MULTI_TENANT_FLAG

  post_secret_manager "dcjpy/bpm_server/oauth2/core/admin/${ADMIN_ID1}" '{"client_secret" : "admin_secret"}'

  if [ "${ZONE_TYPE}" = "bpmbiz" ] && [ ${MULTI_TENANT_FLAG} = true ]; then
    post_secret_manager "dcjpy/bpm_server/oauth2/core/admin/${ADMIN_ID2}" '{"client_secret" : "admin_secret"}'
  fi
  echo "ADMIN 情報を登録しました"
fi

ACCESS_TOKEN=$(curl -Ss -X GET http://localhost:${IDP_BPM_MOCK_PORT}/token/${ADMIN_CLIENT_ID})

if [ "${ZONE_TYPE}" = "bpmfin" ]; then

  # イシュア作成
  RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID1" \
    -X POST ${BPM_BASE_URL}/admin/issuer -d @- <<EOF
    {"bank_code": "${BANK_CODE1}", "issuer_name": "${ISSUER_NAME1}"}
EOF
  )

  ISSUER_ID1=$(echo ${RESULT} | jq -r ".issuer_id")
  ISSUER_CLIENT_ID1=$(echo ${RESULT} | jq -r ".client_id")
  ISSUER_CLIENT_SECRET1=$(echo ${RESULT} | jq -r ".client_secret")

  if [ ${ISSUER_ID1} = null ]; then
    echo "イシュア1作成 : 失敗しました"
    exit 1
  fi
  echo "イシュア1を作成しました"

  if [ ${MULTI_TENANT_FLAG} = true ]; then
  # イシュア作成
    RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID2" \
      -X POST ${BPM_BASE_URL}/admin/issuer -d @- <<EOF
      {"bank_code": "${BANK_CODE2}", "issuer_name": "${ISSUER_NAME2}"}
EOF
    )

    ISSUER_ID2=$(echo ${RESULT} | jq -r ".issuer_id")
    ISSUER_CLIENT_ID2=$(echo ${RESULT} | jq -r ".client_id")
    ISSUER_CLIENT_SECRET2=$(echo ${RESULT} | jq -r ".client_secret")

    if [ ${ISSUER_ID2} = null ]; then
      echo "イシュア2作成 : 失敗しました"
      exit 1
    fi
    echo "イシュア2を作成しました"

    # イシュア作成
        RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID3" \
          -X POST ${BPM_BASE_URL}/admin/issuer -d @- <<EOF
          {"bank_code": "${BANK_CODE3}", "issuer_name": "${ISSUER_NAME3}"}
EOF
        )

        ISSUER_ID3=$(echo ${RESULT} | jq -r ".issuer_id")
        ISSUER_CLIENT_ID3=$(echo ${RESULT} | jq -r ".client_id")
        ISSUER_CLIENT_SECRET3=$(echo ${RESULT} | jq -r ".client_secret")

        if [ ${ISSUER_ID3} = null ]; then
          echo "イシュア3作成 : 失敗しました"
          exit 1
        fi
        echo "イシュア3を作成しました"
      fi
fi

#バリデータ作成

if [ "${ZONE_TYPE}" = "bpmfin" ]; then
  RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID1" \
      -X POST ${BPM_BASE_URL}/admin/validator -d @- <<EOF
      {"validator_name": "${VALIDATOR_NAME1}", "issuer_id" : "${ISSUER_ID1}"}
EOF
    )

    VALIDATOR_ID1=$(echo ${RESULT} | jq -r ".validator_id")
    VALIDATOR_CLIENT_ID1=$(echo ${RESULT} | jq -r ".client_id")
    VALIDATOR_CLIENT_SECRET1=$(echo ${RESULT} | jq -r ".client_secret")

    if [ ${VALIDATOR_ID1} = null ]; then
      echo "バリデータ1作成 : 失敗しました"
      exit 1
    fi

    if [ ${MULTI_TENANT_FLAG} = true ]; then
    RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID2" \
      -X POST ${BPM_BASE_URL}/admin/validator -d @- <<EOF
      {"validator_name": "${VALIDATOR_NAME2}", "issuer_id" : "${ISSUER_ID2}"}
EOF
    )

    VALIDATOR_ID2=$(echo ${RESULT} | jq -r ".validator_id")
    VALIDATOR_CLIENT_ID2=$(echo ${RESULT} | jq -r ".client_id")
    VALIDATOR_CLIENT_SECRET2=$(echo ${RESULT} | jq -r ".client_secret")

    if [ ${VALIDATOR_ID2} = null ]; then
      echo "バリデータ2作成 : 失敗しました"
      exit 1
    fi

    RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID3" \
      -X POST ${BPM_BASE_URL}/admin/validator -d @- <<EOF
      {"validator_name": "${VALIDATOR_NAME3}", "issuer_id" : "${ISSUER_ID3}"}
EOF
    )

    VALIDATOR_ID3=$(echo ${RESULT} | jq -r ".validator_id")
    VALIDATOR_CLIENT_ID3=$(echo ${RESULT} | jq -r ".client_id")
    VALIDATOR_CLIENT_SECRET3=$(echo ${RESULT} | jq -r ".client_secret")

    if [ ${VALIDATOR_ID3} = null ]; then
      echo "バリデータ3作成 : 失敗しました"
    fi
  fi
else
  RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID1" \
        -X POST ${BPM_BASE_URL}/admin/validator -d @- <<EOF
        {"validator_name": "${VALIDATOR_NAME1}"}
EOF
      )

      VALIDATOR_ID1=$(echo ${RESULT} | jq -r ".validator_id")
      VALIDATOR_CLIENT_ID1=$(echo ${RESULT} | jq -r ".client_id")
      VALIDATOR_CLIENT_SECRET1=$(echo ${RESULT} | jq -r ".client_secret")

      if [ ${VALIDATOR_ID1} = null ]; then
        echo "バリデータ1作成 : 失敗しました"
        exit 1
      fi

      if [ ${MULTI_TENANT_FLAG} = true ]; then
      RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID2" \
        -X POST ${BPM_BASE_URL}/admin/validator -d @- <<EOF
        {"validator_name": "${VALIDATOR_NAME2}"}
EOF
      )

      VALIDATOR_ID2=$(echo ${RESULT} | jq -r ".validator_id")
      VALIDATOR_CLIENT_ID2=$(echo ${RESULT} | jq -r ".client_id")
      VALIDATOR_CLIENT_SECRET2=$(echo ${RESULT} | jq -r ".client_secret")

      if [ ${VALIDATOR_ID2} = null ]; then
        echo "バリデータ2作成 : 失敗しました"
        exit 1
      fi

      RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID3" \
        -X POST ${BPM_BASE_URL}/admin/validator -d @- <<EOF
        {"validator_name": "${VALIDATOR_NAME3}"}
EOF
      )

      VALIDATOR_ID3=$(echo ${RESULT} | jq -r ".validator_id")
      VALIDATOR_CLIENT_ID3=$(echo ${RESULT} | jq -r ".client_id")
      VALIDATOR_CLIENT_SECRET3=$(echo ${RESULT} | jq -r ".client_secret")

      if [ ${VALIDATOR_ID3} = null ]; then
        echo "バリデータ3作成 : 失敗しました zone=3002はありません"
      fi
  fi
fi



echo "バリデータを作成しました"

# サービスオーナ作成
if [ "${ZONE_TYPE}" = "bpmfin" ]; then
  curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID1" \
    -X POST ${BPM_BASE_URL}/admin/service_owner -d @- <<EOF
    {
      "service_id": "${SERVICE_ID1}",
      "service_name": "${VALIDATOR_NAME1}",
      "zone_id": "${ZONE_ID1}",
      "validator_id": "${VALIDATOR_ID1}",
      "issuer_id": "${ISSUER_ID1}"
    }
EOF

  if [ ${MULTI_TENANT_FLAG} = true ]; then
  curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID2" \
      -X POST ${BPM_BASE_URL}/admin/service_owner -d @- <<EOF
      {
        "service_id": "${SERVICE_ID2}",
        "service_name": "${VALIDATOR_NAME2}",
        "zone_id": "${ZONE_ID2}",
        "validator_id": "${VALIDATOR_ID2}",
        "issuer_id": "${ISSUER_ID2}"
      }
EOF

  curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID3" \
        -X POST ${BPM_BASE_URL}/admin/service_owner -d @- <<EOF
        {
          "service_id": "${SERVICE_ID3}",
          "service_name": "${VALIDATOR_NAME3}",
          "zone_id": "${ZONE_ID3}",
          "validator_id": "${VALIDATOR_ID3}",
          "issuer_id": "${ISSUER_ID3}"
        }
EOF
  fi

else

  curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID1" \
    -X POST ${BPM_BASE_URL}/admin/service_owner -d @- <<EOF
    {
      "service_id": "${SERVICE_ID1}",
      "service_name": "${VALIDATOR_NAME1}",
      "zone_id": "${ZONE_ID1}",
      "validator_id": "${VALIDATOR_ID1}"
    }
EOF

  if [ ${MULTI_TENANT_FLAG} = true ]; then
  curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID2" \
    -X POST ${BPM_BASE_URL}/admin/service_owner -d @- <<EOF
    {
      "service_id": "${SERVICE_ID2}",
      "service_name": "${VALIDATOR_NAME2}",
      "zone_id": "${ZONE_ID2}",
      "validator_id": "${VALIDATOR_ID2}"
    }
EOF

  if [ ${VALIDATOR_ID3} != null ]; then
  curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID3" \
    -X POST ${BPM_BASE_URL}/admin/service_owner -d @- <<EOF
    {
      "service_id": "${SERVICE_ID3}",
      "service_name": "${VALIDATOR_NAME3}",
      "zone_id": "${ZONE_ID3}",
      "validator_id": "${VALIDATOR_ID3}"
    }
EOF
  fi
  fi
fi

  # 銀行/事業者 管理ユーザ作成
    RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID1" \
        -X POST ${BPM_BASE_URL}/admin/service_user -d @- <<EOF
        {"user_name": "${ADMIN_USER_NAME1}"}
EOF
    )

    SIGN_IN_ID1=$(echo ${RESULT} | jq -r ".sign_in_id")
    TEMPORARY_PASSWORD1=$(echo ${RESULT} | jq -r ".temporary_password")

    if [ ${SIGN_IN_ID1} = null ]; then
      echo "銀行/事業者 管理ユーザ1作成 : 失敗しました"
      exit 1
    fi

    if [ ${MULTI_TENANT_FLAG} = true ]; then
    # 銀行/事業者 管理ユーザ作成
    RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID2" \
          -X POST ${BPM_BASE_URL}/admin/service_user -d @- <<EOF
          {"user_name": "${ADMIN_USER_NAME2}"}
EOF
    )

    SIGN_IN_ID2=$(echo ${RESULT} | jq -r ".sign_in_id")
    TEMPORARY_PASSWORD2=$(echo ${RESULT} | jq -r ".temporary_password")

    if [ ${SIGN_IN_ID2} = null ]; then
        echo "銀行/事業者 管理ユーザ2作成 : 失敗しました"
        exit 1
    fi

  if [ ${VALIDATOR_ID3} != null ]; then
    # 銀行/事業者 管理ユーザ作成
    RESULT=$(curl -Ss -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -H "$CUSTOM_HEADER_SERVICE_ID3" \
              -X POST ${BPM_BASE_URL}/admin/service_user -d @- <<EOF
              {"user_name": "${ADMIN_USER_NAME3}"}
EOF
    )

    SIGN_IN_ID3=$(echo ${RESULT} | jq -r ".sign_in_id")
    TEMPORARY_PASSWORD3=$(echo ${RESULT} | jq -r ".temporary_password")

    if [ ${SIGN_IN_ID3} = null ]; then
        echo "銀行/事業者 管理ユーザ3作成 : 失敗しました"
        exit 1
    fi
  fi
fi

echo "銀行/事業者 管理ユーザを作成しました"

echo ""
echo "BPM 初期構築が完了しました"
echo ""
echo "========================================================================"
echo "      SERVICE_ID1             : ${SERVICE_ID1}"
echo "      ZONE_ID1                : ${ZONE_ID1}"
if [ "${ZONE_TYPE}" = "bpmfin" ]; then
  echo " イシュア"
  echo "    ISSUER_ID1               : ${ISSUER_ID1}"
  echo "    ISSUER_CLIENT_ID1        : ${ISSUER_CLIENT_ID1}"
  echo "    ISSUER_CLIENT_SECRET1    : ${ISSUER_CLIENT_SECRET1}"
  echo ""
fi
echo " バリデータ"
echo "    VALIDATOR_ID1            : ${VALIDATOR_ID1}"
echo "    VALIDATOR_CLIENT_ID1     : ${VALIDATOR_CLIENT_ID1}"
echo "    VALIDATOR_CLIENT_SECRET1 : ${VALIDATOR_CLIENT_SECRET1}"
echo ""
echo " 管理ユーザ"
echo "    SIGN_IN_ID1              : ${SIGN_IN_ID1}"
echo "    TEMPORARY_PASSWORD1      : ${TEMPORARY_PASSWORD1}"
echo "========================================================================"

if [ "${MULTI_TENANT_FLAG}" = true ]; then
echo ""
echo "========================================================================"
echo "      SERVICE_ID2             : ${SERVICE_ID2}"
echo "      ZONE_ID2                : ${ZONE_ID2}"
if [ "${ZONE_TYPE}" = "bpmfin" ]; then
  echo " イシュア"
  echo "    ISSUER_ID2               : ${ISSUER_ID2}"
  echo "    ISSUER_CLIENT_ID2        : ${ISSUER_CLIENT_ID2}"
  echo "    ISSUER_CLIENT_SECRET2    : ${ISSUER_CLIENT_SECRET2}"
  echo ""
fi
echo " バリデータ"
echo "    VALIDATOR_ID2            : ${VALIDATOR_ID2}"
echo "    VALIDATOR_CLIENT_ID2     : ${VALIDATOR_CLIENT_ID2}"
echo "    VALIDATOR_CLIENT_SECRET2 : ${VALIDATOR_CLIENT_SECRET2}"
echo ""
echo " 管理ユーザ"
echo "    SIGN_IN_ID2              : ${SIGN_IN_ID2}"
echo "    TEMPORARY_PASSWORD2      : ${TEMPORARY_PASSWORD2}"
echo "========================================================================"
echo ""
echo ""
echo "========================================================================"
echo "      SERVICE_ID3             : ${SERVICE_ID3}"
echo "      ZONE_ID3                : ${ZONE_ID3}"
if [ "${ZONE_TYPE}" = "bpmfin" ]; then
  echo " イシュア"
  echo "    ISSUER_ID3               : ${ISSUER_ID3}"
  echo "    ISSUER_CLIENT_ID3        : ${ISSUER_CLIENT_ID3}"
  echo "    ISSUER_CLIENT_SECRET3    : ${ISSUER_CLIENT_SECRET3}"
  echo ""
fi
echo " バリデータ"
echo "    VALIDATOR_ID3            : ${VALIDATOR_ID3}"
echo "    VALIDATOR_CLIENT_ID3     : ${VALIDATOR_CLIENT_ID3}"
echo "    VALIDATOR_CLIENT_SECRET3 : ${VALIDATOR_CLIENT_SECRET3}"
echo ""
echo " 管理ユーザ"
echo "    SIGN_IN_ID3              : ${SIGN_IN_ID3}"
echo "    TEMPORARY_PASSWORD3      : ${TEMPORARY_PASSWORD3}"
echo "========================================================================"
echo ""
fi


