#!/bin/bash
# BizZone用のアカウントを作成します
# アカウント作成後にそのまま紐づくユーザの作成も可能です

if [ $# -le 2 ]; then
    echo ""
    echo "    [USAGE] ./create-account-bpmbiz.sh <FIN_SIGN_IN_ID> <FIN_PASSWORD> <TOTP_SECRET> [ <SERVICE_ID> ]"
    echo ""
    exit 9
fi

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
FIN_SIGN_IN_ID=$1
FIN_PASSWORD=$2
TOTP_SECRET=$3
if [ "$#" -eq 4 ]; then
    SERVICE_ID=$4
    CUSTOM_HEADER="-H X-DCJPY-SERVICE-ID:${SERVICE_ID}"
else
    SERVICE_ID="0"
    CUSTOM_HEADER=""
fi

source ${BASE}/env/bpmbiz.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ./env/.env ]; then
  source ./env/.env
fi

# ランダムな文字列を生成する関数
generate_random_string() {
    local length=$1
    local charset=$2
    LC_ALL=C tr -dc "$charset" < /dev/urandom | fold -w ${length} | head -n 1
}

# ランダムなメールアドレスを生成する関数
generate_random_email() {
    local local_part_length=$1
    local domain_length=$2
    local tld_length=$3

    # ローカルパートの最初の文字は英小文字または数字
    local local_part_first_char=$(generate_random_string 1 'a-z0-9')
    # ローカルパートの残りの文字は英小文字、数字、ハイフン、ドット、アンダースコア
    local local_part_remaining=$(generate_random_string $(($local_part_length - 1)) 'a-z0-9-._')
    local local_part="$local_part_first_char$local_part_remaining"

    # ドメインパートは英小文字、数字、ハイフン、ドット
    local domain=$(generate_random_string $domain_length 'a-z0-9-.')

    # TLD（トップレベルドメイン）は英小文字
    local tld=$(generate_random_string $tld_length 'a-z')

    echo "$local_part@$domain.$tld"
}

# 使用例：ローカルパート10文字、ドメイン10文字、TLD3文字
EMAIL=$(generate_random_email 10 10 3)

# 01. サインアップ実施
echo "01. サインアップ開始"
SIGN_UP_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_up)
echo ${SIGN_UP_RESULT} | jq .

BIZ_SIGN_IN_ID=$(echo ${SIGN_UP_RESULT} | jq -r .sign_in_id)
BIZ_PASSWORD=$(echo ${SIGN_UP_RESULT} | jq -r .temporary_password)

if [ $BIZ_SIGN_IN_ID = null ]; then
    echo "01. サインアップ実施結果 : 失敗しました"
    exit 1
fi


# 02. 認証アプリなしサインイン
echo "02. 認証アプリなしサインイン"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_in -d @- <<EOF
{"sign_in_id": "${BIZ_SIGN_IN_ID}", "password": "${BIZ_PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

BIZ_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

if [ $BIZ_ACCESS_TOKEN = null ]; then
    echo "02. 認証アプリなしサインイン : 失敗しました"
    exit 1
fi


# 03. パスワード変更
echo "03. パスワード変更"
PASSWORD_RESULT=$(curl -sS -X PUT -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_user/password -d @- <<EOF
{"password": "${BIZ_PASSWORD}", "new_password": "${PASSWORD}"}
EOF
)
echo ${PASSWORD_RESULT} | jq .


# 04. 認証アプリなしサインイン
echo "04. 認証アプリなしサインイン"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_in -d @- <<EOF
{"sign_in_id": "${BIZ_SIGN_IN_ID}", "password": "${PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

BIZ_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)
BIZ_ID_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .id_token)

if [ $BIZ_ACCESS_TOKEN = null ]; then
    echo "04. 認証アプリなしサインイン : 失敗しました"
    exit 1
fi

# 05. メールアドレス設定受付
echo "05. メールアドレス設定受付"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_user/email/prepare -d @- <<EOF
{"email_address": "${EMAIL}"}
EOF
)
echo ${RESULT} | jq .

# CognitoLocalのEMAILを検証済の状態へ更新する
COGNITO_USER_NAME='dc_user_'$BIZ_SIGN_IN_ID

aws cognito-idp admin-update-user-attributes \
    --endpoint-url ${COGNITO_LOCAL_ENDPOINT} \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --user-attributes Name=email,Value=${EMAIL} Name=email_verified,Value=true

# ユーザ属性作成
ATTRIBUTE='{"email_address" : "'$EMAIL'"}'

# BPM DBへの登録
SQL_RESPONSE1=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -t -c \
  "INSERT INTO dc_user_attribute (sign_in_id, attribute_detail) VALUES ('$BIZ_SIGN_IN_ID','$ATTRIBUTE')")


# 06. Signer 認証開始
echo "06. Signer 認証開始"
REDIRECT_URI=dummyValue
RESULT=$(curl -i -sS -X POST -H "Content-Type: application/x-www-form-urlencoded" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/signer/token \
  -d "bank_code=0310&id_token=${BIZ_ID_TOKEN}&redirect_uri=${REDIRECT_URI}" \
)
echo ${RESULT} | grep "location:"

# Stateの取得
OAUTH_STATE=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -tA -c \
  "SELECT oauth_state FROM dc_user_oauth_state WHERE sign_in_id='$BIZ_SIGN_IN_ID'")
STATE=$(echo "$OAUTH_STATE" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# 07. Signerサインイン受付
echo "07. Signerサインイン受付"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${SIGNER_URL}/auth/sign_in/prepare -d @- <<EOF
{"state_code": "${STATE}","redirect_uri":"${REDIRECT_URI}"}
EOF
)
echo ${RESULT} | jq .

QR_TOKEN=$(echo ${RESULT} | jq -r .qr_token)


# 08. (認証アプリ) サインイン (TOTPあり)
echo "08.(認証アプリ) サインイン (TOTPあり)開始"
TOTP=$(oathtool --totp -b $TOTP_SECRET)

FIN_SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/auth/sign_in -d @- <<EOF
{"sign_in_id": "${FIN_SIGN_IN_ID}", "password": "${FIN_PASSWORD}", "totp": "${TOTP}"}
EOF
)
echo ${FIN_SIGN_IN_RESULT} | jq .

FIN_ACCESS_TOKEN=$(echo ${FIN_SIGN_IN_RESULT} | jq -r .access_token)
FIN_ID_TOKEN=$(echo ${FIN_SIGN_IN_RESULT} | jq -r .id_token)


# 09. 認証アプリ QRデータ 連携
echo "09. 認証アプリ QRデータ 連携"
TOTP=$(oathtool --totp -b $TOTP_SECRET)

RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/auth/qr_token -d @- <<EOF
{"qr_token": "${QR_TOKEN}", "totp": "${TOTP}"}
EOF
)
echo ${RESULT} | jq .

# 10. Signer サインインQRコード処理状況確認
echo "10. Signer サインインQRコード処理状況確認"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${SIGNER_URL}/auth/sign_in/qr_token -d @- <<EOF
{"state_code": "${STATE}","qr_token":"${QR_TOKEN}"}
EOF
)
echo ${RESULT} | jq .

REDIRECT_URI=$(echo ${RESULT} | jq -r .redirect_uri)
# state_codeを抽出
STATE_CODE=$(echo "$REDIRECT_URI" | sed -n 's/.*state_code=\([^&]*\).*/\1/p')
# auth_codeを抽出
AUTH_CODE=$(echo "$REDIRECT_URI" | sed -n 's/.*auth_code=\(.*\)/\1/p')


# 11. Signer トークン発行
echo "11. Signer トークン発行"
RESULT=$(curl -sS -X GET -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/signer/token?state_code=$STATE_CODE\&code=$AUTH_CODE)
echo ${RESULT} | jq .
DC_BANK_NUMBER=$(echo ${RESULT} | jq -r .dc_bank_number)



# 12. ビジネスゾーンアカウント 開設(精算条件あり)
echo "12. ビジネスゾーンアカウント 開設(精算条件あり)	"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_account/synchronous/with_settlement -d @- <<EOF
{"dc_bank_number": "${DC_BANK_NUMBER}","account_name":"ビジネスゾーンアカウント","settlement":{"settlement_type":"immediate"}}
EOF
)
echo ${RESULT} | jq .

# 利用確定できるようにスリープを入れる
echo "relayerの連携を待つためここから10秒間スリープします。"
echo "環境状態によっては10秒以上かかる場合があるのでその場合はエラーとなります"
sleep 10

# 13. ビジネスゾーンアカウント利用確定
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/holders/business/apply -d @- <<EOF
{"zone_id": "3001"}
EOF
)
echo ${RESULT} | jq .
ZONE_ID=$(echo ${RESULT} | jq -r .zone_id)

if [ $ZONE_ID = null ]; then
    echo "FinZoneの利用確定が完了していないので再度時間をおいて以下のシェルを実行してください"
    echo "./biz-account-apply.sh ${SERVICE_ID} ${FIN_SIGN_IN_ID} ${FIN_PASSWORD} ${TOTP_SECRET}"
    echo ""
    echo ""
else
  echo "ビジネスゾーン利用確定に成功しました。"
fi


echo "========================================================================"
echo " BizZone DCアカウント作成結果"
echo "    BIZ_SIGN_IN_ID : "$BIZ_SIGN_IN_ID
echo "    PASSWORD       : "$PASSWORD
echo "    DC_BANK_NUMBER : "$DC_BANK_NUMBER
echo "    EMAIL          : "$EMAIL
echo ""
echo "    BIZ_ACCESS_TOKEN : "$BIZ_ACCESS_TOKEN
echo ""
echo "========================================================================"
echo ""

echo ""
echo "このまま作成したアカウントに紐づくユーザを作成しますか、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

if [ "${SERVICE_ID}" = "0" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-********0011
  APPROVER_ROLE_ID=********-0000-4000-0000-********0013
  OPERATOR_ROLE_ID=********-0000-4000-0000-********0012
elif [ "${SERVICE_ID}" = "0d367be2" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-********0111
  APPROVER_ROLE_ID=********-0000-4000-0000-********0113
  OPERATOR_ROLE_ID=********-0000-4000-0000-********0112
elif [ "${SERVICE_ID}" = "43e4abef" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-********1111
  APPROVER_ROLE_ID=********-0000-4000-0000-********1113
  OPERATOR_ROLE_ID=********-0000-4000-0000-********1112
elif [ "${SERVICE_ID}" = "9946abd1" ]; then
  USER_OWNER_ROLE_ID=********-0000-4000-0000-********2111
  APPROVER_ROLE_ID=********-0000-4000-0000-********2113
  OPERATOR_ROLE_ID=********-0000-4000-0000-********2112
fi

# ユーザ作成(ユーザ管理者)
USER_OWNER_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/users -d @- <<EOF
{"user_name": "${USER_OWNER_NAME}", "role_id": "${USER_OWNER_ROLE_ID}"}
EOF
)

USER_OWNER_SIGN_IN_ID=$(echo ${USER_OWNER_RESULT} | jq -r .sign_in_id)
USER_OWNER_TEMPORARY_PASSWORD=$(echo ${USER_OWNER_RESULT} | jq -r .temporary_password)
USER_OWNER_USER_NAME=$(echo ${USER_OWNER_RESULT} | jq -r .user_name)

# ユーザ作成(承認者)
APPROVER_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/users -d @- <<EOF
{"user_name": "${APPROVER_NAME}", "role_id": "${APPROVER_ROLE_ID}"}
EOF
)

APPROVER_SIGN_IN_ID=$(echo ${APPROVER_RESULT} | jq -r .sign_in_id)
APPROVER_TEMPORARY_PASSWORD=$(echo ${APPROVER_RESULT} | jq -r .temporary_password)
APPROVER_USER_NAME=$(echo ${APPROVER_RESULT} | jq -r .user_name)

# ユーザ作成(担当者)
OPERATOR_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/users -d @- <<EOF
{"user_name": "${OPERATOR_NAME}", "role_id": "${OPERATOR_ROLE_ID}"}
EOF
)

OPERATOR_SIGN_IN_ID=$(echo ${OPERATOR_RESULT} | jq -r .sign_in_id)
OPERATOR_TEMPORARY_PASSWORD=$(echo ${OPERATOR_RESULT} | jq -r .temporary_password)
OPERATOR_USER_NAME=$(echo ${OPERATOR_RESULT} | jq -r .user_name)


echo ""
echo " ユーザ管理者"
echo "    USER_OWNER_SIGN_IN_ID : "$USER_OWNER_SIGN_IN_ID
echo "    USER_OWNER_PASSWORD   : Password1"
echo "    USER_OWNER_USER_NAME  : "$USER_OWNER_USER_NAME
#ユーザをアクティブへ更新
if [ "${SERVICE_ID}" = "0" ]; then
  ./activate-dc-user-bpmbiz.sh $USER_OWNER_SIGN_IN_ID $USER_OWNER_TEMPORARY_PASSWORD
else
  ./activate-dc-user-bpmbiz.sh $USER_OWNER_SIGN_IN_ID $USER_OWNER_TEMPORARY_PASSWORD $SERVICE_ID
fi

echo "  業務承認者"
echo "    APPROVER_SIGN_IN_ID   : "$APPROVER_SIGN_IN_ID
echo "    APPROVER_PASSWORD     : Password1 "
echo "    APPROVER_USER_NAME    : "$APPROVER_USER_NAME
#ユーザをアクティブへ更新
if [ "${SERVICE_ID}" = "0" ]; then
  ./activate-dc-user-bpmbiz.sh $APPROVER_SIGN_IN_ID $APPROVER_TEMPORARY_PASSWORD
else
  ./activate-dc-user-bpmbiz.sh $APPROVER_SIGN_IN_ID $APPROVER_TEMPORARY_PASSWORD $SERVICE_ID
fi

echo "  業務担当者"
echo "    OPERATOR_SIGN_IN_ID   : "$OPERATOR_SIGN_IN_ID
echo "    OPERATOR_PASSWORD     : Password1"
echo "    OPERATOR_USER_NAME    : "$OPERATOR_USER_NAME
#ユーザをアクティブへ更新
if [ "${SERVICE_ID}" = "0" ]; then
  ./activate-dc-user-bpmbiz.sh $OPERATOR_SIGN_IN_ID $OPERATOR_TEMPORARY_PASSWORD
else
  ./activate-dc-user-bpmbiz.sh $OPERATOR_SIGN_IN_ID $OPERATOR_TEMPORARY_PASSWORD $SERVICE_ID
fi
echo ""
echo "========================================================================"
echo ""
