#!/bin/bash

# BPM 初期構築スクリプト
# 1. Core Admin API を呼び出すための 接続情報登録
#   a.BPM DB の初期データ登録
#     - service_admin
#     - auth_client
#   b. BPM Secret Manager への登録
# 2. イシュア登録 (FinZone のみ)
# 3. バリデータ登録
# 4. サービスオーナ作成
# 5. 銀行/事業者 管理ユーザ登録
# 以下の要件を満たすデータを作成
# https://decurret.atlassian.net/wiki/spaces/DIG/pages/3271163973/IT#%E3%83%AD%E3%83%BC%E3%82%AB%E3%83%ABIT%E6%99%82%E3%81%AE%E6%A7%8B%E6%88%90

# 引数チェック
if [ $# -ne 2 ]; then
    echo "[USAGE] ./create-master-db.sh < bpmfin | bpmbiz > MULTI_TENANT_FLAG"
    exit 9
fi

# エラーが発生したら、直ちに終了する
set -e

ZONE_TYPE=$1
BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/${ZONE_TYPE}.env

MULTI_TENANT_FLAG=$2
if [ ${MULTI_TENANT_FLAG} = false ]; then
    SERVICE_ID1="0"
fi

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi


psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
    "INSERT INTO service_admin (service_id, admin_id) VALUES ('$SERVICE_ID1', '$ADMIN_ID1')"
psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
    "INSERT INTO service_admin (service_id, admin_id) VALUES ('$SERVICE_ID2', '$ADMIN_ID1')"
psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
    "INSERT INTO auth_client (entity_id, entity_type, client_id) VALUES ('$ADMIN_ID1', 'admin', 'admin_id1')"

# platform_arn の作成
create_platform_arn() {
  local NAME=$1
  local PLATFORM=$2
  aws sns create-platform-application \
    --name ${NAME} \
    --platform ${PLATFORM} \
    --attributes '{"PlatformCredential":"API_KEY"}' \
    --endpoint-url=http://localhost:44566
}

create_platform_arn APNS1App APNS1
create_platform_arn FCM1App FCM1
create_platform_arn APNS2App APNS2
create_platform_arn FCM2App FCM2
create_platform_arn APNS3App APNS3
create_platform_arn FCM3App FCM3

psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
    "INSERT INTO service_device (service_id, os_type, platform_arn)
    VALUES ('$SERVICE_ID1', 'ios', 'arn:aws:sns:ap-northeast-1:********0000:app/APNS1/APNS1App'),
           ('$SERVICE_ID1', 'android', 'arn:aws:sns:ap-northeast-1:********0000:app/FCM1/FCM1App'),
           ('$SERVICE_ID2', 'ios', 'arn:aws:sns:ap-northeast-1:********0000:app/APNS2/APNS2App'),
           ('$SERVICE_ID2', 'android', 'arn:aws:sns:ap-northeast-1:********0000:app/FCM2/FCM2App'),
           ('$SERVICE_ID3', 'ios', 'arn:aws:sns:ap-northeast-1:********0000:app/APNS3/APNS3App'),
           ('$SERVICE_ID3', 'android', 'arn:aws:sns:ap-northeast-1:********0000:app/FCM3/FCM3App');"

psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
      "INSERT INTO dc_user_role (role_id, role_name, role_type, service_id, created_at)
       VALUES ('********-0000-4000-0000-********0100', '個人ユーザ', 'individual', '$SERVICE_ID1', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********0110', '口座管理者', 'account_owner', '$SERVICE_ID1', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********0111', 'ユーザ管理者', 'user_owner', '$SERVICE_ID1', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********0112', '業務担当者', 'operator', '$SERVICE_ID1', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********0113', '業務承認者', 'reviewer', '$SERVICE_ID1', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********1100', '個人ユーザ', 'individual', '$SERVICE_ID2', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********1110', '口座管理者', 'account_owner', '$SERVICE_ID2', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********1111', 'ユーザ管理者', 'user_owner', '$SERVICE_ID2', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********1112', '業務担当者', 'operator', '$SERVICE_ID2', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********1113', '業務承認者', 'reviewer', '$SERVICE_ID2', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********2100', '個人ユーザ', 'individual', '$SERVICE_ID3', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********2110', '口座管理者', 'account_owner', '$SERVICE_ID3', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********2111', 'ユーザ管理者', 'user_owner', '$SERVICE_ID3', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********2112', '業務担当者', 'operator', '$SERVICE_ID3', CURRENT_TIMESTAMP),
              ('********-0000-4000-0000-********2113', '業務承認者', 'reviewer', '$SERVICE_ID3', CURRENT_TIMESTAMP);"

psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
        "INSERT INTO dc_user_role_authority (role_id, authority_key)
         VALUES ('********-0000-4000-0000-********0110', 'mint'),
                ('********-0000-4000-0000-********0110', 'burn'),
                ('********-0000-4000-0000-********0110', 'transfer'),
                ('********-0000-4000-0000-********0110', 'charge'),
                ('********-0000-4000-0000-********0110', 'discharge'),
                ('********-0000-4000-0000-********0110', 'view_account'),
                ('********-0000-4000-0000-********0110', 'view_transactions'),
                ('********-0000-4000-0000-********0110', 'change_account'),
                ('********-0000-4000-0000-********0112', 'mint'),
                ('********-0000-4000-0000-********0112', 'burn'),
                ('********-0000-4000-0000-********0112', 'transfer'),
                ('********-0000-4000-0000-********0112', 'charge'),
                ('********-0000-4000-0000-********0112', 'discharge'),
                ('********-0000-4000-0000-********0112', 'view_account'),
                ('********-0000-4000-0000-********0112', 'view_transactions'),
                ('********-0000-4000-0000-********0112', 'change_account'),
                ('********-0000-4000-0000-********0113', 'mint'),
                ('********-0000-4000-0000-********0113', 'burn'),
                ('********-0000-4000-0000-********0113', 'transfer'),
                ('********-0000-4000-0000-********0113', 'charge'),
                ('********-0000-4000-0000-********0113', 'discharge'),
                ('********-0000-4000-0000-********0113', 'view_account'),
                ('********-0000-4000-0000-********0113', 'view_transactions'),
                ('********-0000-4000-0000-********0113', 'change_account'),
                ('********-0000-4000-0000-********1110', 'mint'),
                ('********-0000-4000-0000-********1110', 'burn'),
                ('********-0000-4000-0000-********1110', 'transfer'),
                ('********-0000-4000-0000-********1110', 'charge'),
                ('********-0000-4000-0000-********1110', 'discharge'),
                ('********-0000-4000-0000-********1110', 'view_account'),
                ('********-0000-4000-0000-********1110', 'view_transactions'),
                ('********-0000-4000-0000-********1110', 'change_account'),
                ('********-0000-4000-0000-********1112', 'mint'),
                ('********-0000-4000-0000-********1112', 'burn'),
                ('********-0000-4000-0000-********1112', 'transfer'),
                ('********-0000-4000-0000-********1112', 'charge'),
                ('********-0000-4000-0000-********1112', 'discharge'),
                ('********-0000-4000-0000-********1112', 'view_account'),
                ('********-0000-4000-0000-********1112', 'view_transactions'),
                ('********-0000-4000-0000-********1112', 'change_account'),
                ('********-0000-4000-0000-********1113', 'mint'),
                ('********-0000-4000-0000-********1113', 'burn'),
                ('********-0000-4000-0000-********1113', 'transfer'),
                ('********-0000-4000-0000-********1113', 'charge'),
                ('********-0000-4000-0000-********1113', 'discharge'),
                ('********-0000-4000-0000-********1113', 'view_account'),
                ('********-0000-4000-0000-********1113', 'view_transactions'),
                ('********-0000-4000-0000-********1113', 'change_account'),
                ('********-0000-4000-0000-********2110', 'mint'),
                ('********-0000-4000-0000-********2110', 'burn'),
                ('********-0000-4000-0000-********2110', 'transfer'),
                ('********-0000-4000-0000-********2110', 'charge'),
                ('********-0000-4000-0000-********2110', 'discharge'),
                ('********-0000-4000-0000-********2110', 'view_account'),
                ('********-0000-4000-0000-********2110', 'view_transactions'),
                ('********-0000-4000-0000-********2110', 'change_account'),
                ('********-0000-4000-0000-********2112', 'mint'),
                ('********-0000-4000-0000-********2112', 'burn'),
                ('********-0000-4000-0000-********2112', 'transfer'),
                ('********-0000-4000-0000-********2112', 'charge'),
                ('********-0000-4000-0000-********2112', 'discharge'),
                ('********-0000-4000-0000-********2112', 'view_account'),
                ('********-0000-4000-0000-********2112', 'view_transactions'),
                ('********-0000-4000-0000-********2112', 'change_account'),
                ('********-0000-4000-0000-********2113', 'mint'),
                ('********-0000-4000-0000-********2113', 'burn'),
                ('********-0000-4000-0000-********2113', 'transfer'),
                ('********-0000-4000-0000-********2113', 'charge'),
                ('********-0000-4000-0000-********2113', 'discharge'),
                ('********-0000-4000-0000-********2113', 'view_account'),
                ('********-0000-4000-0000-********2113', 'view_transactions'),
                ('********-0000-4000-0000-********2113', 'change_account');"

psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
        "INSERT INTO service_user_role (role_id, role_name, role_type, service_id, created_at)
         VALUES ('********-0000-4000-0000-********0220', 'サービス管理者', 'service_owner', '$SERVICE_ID1', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********0221', 'ユーザ管理者', 'user_owner', '$SERVICE_ID1', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********0222', '業務担当者', 'operator', '$SERVICE_ID1', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********0223', '業務承認者', 'reviewer', '$SERVICE_ID1', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********1020', 'サービス管理者', 'service_owner', '$SERVICE_ID2', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********1021', 'ユーザ管理者', 'user_owner', '$SERVICE_ID2', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********1022', '業務担当者', 'operator', '$SERVICE_ID2', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********1023', '業務承認者', 'reviewer', '$SERVICE_ID2', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********2220', 'サービス管理者', 'service_owner', '$SERVICE_ID3', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********2221', 'ユーザ管理者', 'user_owner', '$SERVICE_ID3', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********2222', '業務担当者', 'operator', '$SERVICE_ID3', CURRENT_TIMESTAMP),
                ('********-0000-4000-0000-********2223', '業務承認者', 'reviewer', '$SERVICE_ID3', CURRENT_TIMESTAMP);"
  
psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
          "INSERT INTO service_user_role_authority (role_id, authority_key)
           VALUES ('********-0000-4000-0000-********0220', 'view_accounts'),
                  ('********-0000-4000-0000-********0220', 'view_account_detail'),
                  ('********-0000-4000-0000-********0220', 'view_transactions'),
                  ('********-0000-4000-0000-********0220', 'change_account'),
                  ('********-0000-4000-0000-********0220', 'reset_authentication'),
                  ('********-0000-4000-0000-********0220', 'user_suspended'),
                  ('********-0000-4000-0000-********0220', 'user_activated'),
                  ('********-0000-4000-0000-********0220', 'account_frozen'),
                  ('********-0000-4000-0000-********0220', 'account_activated'),
                  ('********-0000-4000-0000-********0220', 'account_force_burned'),
                  ('********-0000-4000-0000-********0220', 'account_force_terminated'),
                  ('********-0000-4000-0000-********0220', 'view_information'),
                  ('********-0000-4000-0000-********0220', 'change_information'),
                  ('********-0000-4000-0000-********0222', 'view_accounts'),
                  ('********-0000-4000-0000-********0222', 'view_account_detail'),
                  ('********-0000-4000-0000-********0222', 'view_transactions'),
                  ('********-0000-4000-0000-********0222', 'change_account'),
                  ('********-0000-4000-0000-********0222', 'reset_authentication'),
                  ('********-0000-4000-0000-********0222', 'user_suspended'),
                  ('********-0000-4000-0000-********0222', 'user_activated'),
                  ('********-0000-4000-0000-********0222', 'account_frozen'),
                  ('********-0000-4000-0000-********0222', 'account_activated'),
                  ('********-0000-4000-0000-********0222', 'account_force_burned'),
                  ('********-0000-4000-0000-********0222', 'account_force_terminated'),
                  ('********-0000-4000-0000-********0222', 'view_information'),
                  ('********-0000-4000-0000-********0222', 'change_information'),
                  ('********-0000-4000-0000-********0223', 'view_accounts'),
                  ('********-0000-4000-0000-********0223', 'view_account_detail'),
                  ('********-0000-4000-0000-********0223', 'view_transactions'),
                  ('********-0000-4000-0000-********0223', 'change_account'),
                  ('********-0000-4000-0000-********0223', 'reset_authentication'),
                  ('********-0000-4000-0000-********0223', 'user_suspended'),
                  ('********-0000-4000-0000-********0223', 'user_activated'),
                  ('********-0000-4000-0000-********0223', 'account_frozen'),
                  ('********-0000-4000-0000-********0223', 'account_activated'),
                  ('********-0000-4000-0000-********0223', 'account_force_burned'),
                  ('********-0000-4000-0000-********0223', 'account_force_terminated'),
                  ('********-0000-4000-0000-********0223', 'view_information'),
                  ('********-0000-4000-0000-********0223', 'change_information'),
                  ('********-0000-4000-0000-********1020', 'view_accounts'),
                  ('********-0000-4000-0000-********1020', 'view_account_detail'),
                  ('********-0000-4000-0000-********1020', 'view_transactions'),
                  ('********-0000-4000-0000-********1020', 'change_account'),
                  ('********-0000-4000-0000-********1020', 'reset_authentication'),
                  ('********-0000-4000-0000-********1020', 'user_suspended'),
                  ('********-0000-4000-0000-********1020', 'user_activated'),
                  ('********-0000-4000-0000-********1020', 'account_frozen'),
                  ('********-0000-4000-0000-********1020', 'account_activated'),
                  ('********-0000-4000-0000-********1020', 'account_force_burned'),
                  ('********-0000-4000-0000-********1020', 'account_force_terminated'),
                  ('********-0000-4000-0000-********1020', 'view_information'),
                  ('********-0000-4000-0000-********1020', 'change_information'),
                  ('********-0000-4000-0000-********1022', 'view_accounts'),
                  ('********-0000-4000-0000-********1022', 'view_account_detail'),
                  ('********-0000-4000-0000-********1022', 'view_transactions'),
                  ('********-0000-4000-0000-********1022', 'change_account'),
                  ('********-0000-4000-0000-********1022', 'reset_authentication'),
                  ('********-0000-4000-0000-********1022', 'user_suspended'),
                  ('********-0000-4000-0000-********1022', 'user_activated'),
                  ('********-0000-4000-0000-********1022', 'account_frozen'),
                  ('********-0000-4000-0000-********1022', 'account_activated'),
                  ('********-0000-4000-0000-********1022', 'account_force_burned'),
                  ('********-0000-4000-0000-********1022', 'account_force_terminated'),
                  ('********-0000-4000-0000-********1022', 'view_information'),
                  ('********-0000-4000-0000-********1022', 'change_information'),
                  ('********-0000-4000-0000-********1023', 'view_accounts'),
                  ('********-0000-4000-0000-********1023', 'view_account_detail'),
                  ('********-0000-4000-0000-********1023', 'view_transactions'),
                  ('********-0000-4000-0000-********1023', 'change_account'),
                  ('********-0000-4000-0000-********1023', 'reset_authentication'),
                  ('********-0000-4000-0000-********1023', 'user_suspended'),
                  ('********-0000-4000-0000-********1023', 'user_activated'),
                  ('********-0000-4000-0000-********1023', 'account_frozen'),
                  ('********-0000-4000-0000-********1023', 'account_activated'),
                  ('********-0000-4000-0000-********1023', 'account_force_burned'),
                  ('********-0000-4000-0000-********1023', 'account_force_terminated'),
                  ('********-0000-4000-0000-********1023', 'view_information'),
                  ('********-0000-4000-0000-********1023', 'change_information'),
                  ('********-0000-4000-0000-********2220', 'view_accounts'),
                  ('********-0000-4000-0000-********2220', 'view_account_detail'),
                  ('********-0000-4000-0000-********2220', 'view_transactions'),
                  ('********-0000-4000-0000-********2220', 'change_account'),
                  ('********-0000-4000-0000-********2220', 'reset_authentication'),
                  ('********-0000-4000-0000-********2220', 'user_suspended'),
                  ('********-0000-4000-0000-********2220', 'user_activated'),
                  ('********-0000-4000-0000-********2220', 'account_frozen'),
                  ('********-0000-4000-0000-********2220', 'account_activated'),
                  ('********-0000-4000-0000-********2220', 'account_force_burned'),
                  ('********-0000-4000-0000-********2220', 'account_force_terminated'),
                  ('********-0000-4000-0000-********2220', 'view_information'),
                  ('********-0000-4000-0000-********2220', 'change_information'),
                  ('********-0000-4000-0000-********2222', 'view_accounts'),
                  ('********-0000-4000-0000-********2222', 'view_account_detail'),
                  ('********-0000-4000-0000-********2222', 'view_transactions'),
                  ('********-0000-4000-0000-********2222', 'change_account'),
                  ('********-0000-4000-0000-********2222', 'reset_authentication'),
                  ('********-0000-4000-0000-********2222', 'user_suspended'),
                  ('********-0000-4000-0000-********2222', 'user_activated'),
                  ('********-0000-4000-0000-********2222', 'account_frozen'),
                  ('********-0000-4000-0000-********2222', 'account_activated'),
                  ('********-0000-4000-0000-********2222', 'account_force_burned'),
                  ('********-0000-4000-0000-********2222', 'account_force_terminated'),
                  ('********-0000-4000-0000-********2222', 'view_information'),
                  ('********-0000-4000-0000-********2222', 'change_information'),
                  ('********-0000-4000-0000-********2223', 'view_accounts'),
                  ('********-0000-4000-0000-********2223', 'view_account_detail'),
                  ('********-0000-4000-0000-********2223', 'view_transactions'),
                  ('********-0000-4000-0000-********2223', 'change_account'),
                  ('********-0000-4000-0000-********2223', 'reset_authentication'),
                  ('********-0000-4000-0000-********2223', 'user_suspended'),
                  ('********-0000-4000-0000-********2223', 'user_activated'),
                  ('********-0000-4000-0000-********2223', 'account_frozen'),
                  ('********-0000-4000-0000-********2223', 'account_activated'),
                  ('********-0000-4000-0000-********2223', 'account_force_burned'),
                  ('********-0000-4000-0000-********2223', 'account_force_terminated'),
                  ('********-0000-4000-0000-********2223', 'view_information'),
                  ('********-0000-4000-0000-********2223', 'change_information');"

if [ "${ZONE_TYPE}" = "bpmfin" ]; then
    psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
        "INSERT INTO service_admin (service_id, admin_id) VALUES ('$SERVICE_ID3', '$ADMIN_ID1')"
else
    psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
          "INSERT INTO service_admin (service_id, admin_id) VALUES ('$SERVICE_ID3', '$ADMIN_ID2')"
    psql -h localhost -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
          "INSERT INTO auth_client (entity_id, entity_type, client_id) VALUES ('$ADMIN_ID2', 'admin', 'admin_id2')"
fi