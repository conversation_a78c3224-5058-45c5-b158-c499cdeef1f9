#!/bin/bash
# バリデータに紐づくアカウントを作成します。
# 精算条件付きの BizZone アカウントを作成するためには、事前にこのスクリプトを実行する必要があります。

if [ $# -le 4 ]; then
    echo ""
    echo "    [USAGE] ./create-validator-account-bpmbiz.sh <FIN_SIGN_IN_ID> <FIN_PASSWORD> <TOTP_SECRET> <BIZ_SERVICE_OWNER_SIGN_IN_ID> <BIZ_SERVICE_OWNER_PASSWORD> [ <SERVICE_ID> ]"
    echo ""
    echo "    バリデータに紐づくアカウントを作成します。"
    echo "    精算条件付きの BizZone アカウントを作成するためには、事前にこのスクリプトを実行する必要があります。"
    echo ""
    exit 9
fi

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
FIN_SIGN_IN_ID=$1
FIN_PASSWORD=$2
TOTP_SECRET=$3
BIZ_SERVICE_OWNER_SIGN_IN_ID=$4
BIZ_SERVICE_OWNER_PASSWORD=$5

if [ "$#" -eq 6 ]; then
    SERVICE_ID=$6
    CUSTOM_HEADER="-H X-DCJPY-SERVICE-ID:${SERVICE_ID}"
else
    SERVICE_ID="0"
    CUSTOM_HEADER=""
fi

source ${BASE}/env/bpmbiz.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi

# ランダムな文字列を生成する関数
generate_random_string() {
    local length=$1
    local charset=$2
    LC_ALL=C tr -dc "$charset" < /dev/urandom | fold -w ${length} | head -n 1
}

# ランダムなメールアドレスを生成する関数
generate_random_email() {
    local local_part_length=$1
    local domain_length=$2
    local tld_length=$3

    # ローカルパートの最初の文字は英小文字または数字
    local local_part_first_char=$(generate_random_string 1 'a-z0-9')
    # ローカルパートの残りの文字は英小文字、数字、ハイフン、ドット、アンダースコア
    local local_part_remaining=$(generate_random_string $(($local_part_length - 1)) 'a-z0-9-._')
    local local_part="$local_part_first_char$local_part_remaining"

    # ドメインパートは英小文字、数字、ハイフン、ドット
    local domain=$(generate_random_string $domain_length 'a-z0-9-.')

    # TLD（トップレベルドメイン）は英小文字
    local tld=$(generate_random_string $tld_length 'a-z')

    echo "$local_part@$domain.$tld"
}

# 使用例：ローカルパート10文字、ドメイン10文字、TLD3文字
EMAIL=$(generate_random_email 10 10 3)

# 00. BZ 事前準備 サービスオーナーサインイン
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/services/sign_in -d @- <<EOF
{"sign_in_id": "${BIZ_SERVICE_OWNER_SIGN_IN_ID}", "password": "${BIZ_SERVICE_OWNER_PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

BIZ_SERVICE_OWNER_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

if [ $BIZ_SERVICE_OWNER_ACCESS_TOKEN = null ]; then
    echo "00. サービスオーナーのサインインに失敗しましたCognitoLocalのUserPoolの状態を確認してください"
    exit 1
fi


# 01. BZ サインアップ実施
echo "01. BZ サインアップ開始"
SIGN_UP_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_up)
echo ${SIGN_UP_RESULT} | jq .

BIZ_SIGN_IN_ID=$(echo ${SIGN_UP_RESULT} | jq -r .sign_in_id)
BIZ_PASSWORD=$(echo ${SIGN_UP_RESULT} | jq -r .temporary_password)

if [ $BIZ_SIGN_IN_ID = null ]; then
    echo "01. サインアップ実施結果 : 失敗しました"
    exit 1
fi


# 02. BZ 認証アプリなしサインイン
echo "02. BZ 認証アプリなしサインイン"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_in -d @- <<EOF
{"sign_in_id": "${BIZ_SIGN_IN_ID}", "password": "${BIZ_PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

BIZ_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

if [ $BIZ_ACCESS_TOKEN = null ]; then
    echo "02. 認証アプリなしサインイン : 失敗しました"
    exit 1
fi


# 03. BZ パスワード変更
echo "03. BZ パスワード変更"
PASSWORD_RESULT=$(curl -sS -X PUT -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_user/password -d @- <<EOF
{"password": "${BIZ_PASSWORD}", "new_password": "${PASSWORD}"}
EOF
)
echo ${PASSWORD_RESULT} | jq .


# 04. BZ 認証アプリなしサインイン
echo "04. BZ 認証アプリなしサインイン"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/sign_in -d @- <<EOF
{"sign_in_id": "${BIZ_SIGN_IN_ID}", "password": "${PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

BIZ_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)
BIZ_ID_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .id_token)

if [ $BIZ_ACCESS_TOKEN = null ]; then
    echo "04. BZ 認証アプリなしサインイン : 失敗しました"
    exit 1
fi

# 05. BZ メールアドレス設定受付
echo "05. BZ メールアドレス設定受付"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_user/email/prepare -d @- <<EOF
{"email_address": "${EMAIL}"}
EOF
)
echo ${RESULT} | jq .

# CognitoLocalのEMAILを検証済の状態へ更新する
COGNITO_USER_NAME='dc_user_'$BIZ_SIGN_IN_ID

aws cognito-idp admin-update-user-attributes \
    --endpoint-url ${COGNITO_LOCAL_ENDPOINT} \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --user-attributes Name=email,Value=${EMAIL} Name=email_verified,Value=true

# ユーザ属性作成
ATTRIBUTE='{"email_address" : "'$EMAIL'"}'

# BPM DBへの登録
SQL_RESPONSE1=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -t -c \
  "INSERT INTO dc_user_attribute (sign_in_id, attribute_detail) VALUES ('$BIZ_SIGN_IN_ID','$ATTRIBUTE')")


# 06. BZ Signer 認証開始
echo "06. BZ Signer 認証開始"
REDIRECT_URI=dummyValue
RESULT=$(curl -i -sS -X POST -H "Content-Type: application/x-www-form-urlencoded" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/signer/token \
  -d "bank_code=0310&id_token=${BIZ_ID_TOKEN}&redirect_uri=${REDIRECT_URI}" \
)
echo ${RESULT} | grep "location:"

# Stateの取得
OAUTH_STATE=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -tA -c \
  "SELECT oauth_state FROM dc_user_oauth_state WHERE sign_in_id='$BIZ_SIGN_IN_ID'")
STATE=$(echo "$OAUTH_STATE" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# 07. Signer サインイン受付
echo "07. Signer サインイン受付"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${SIGNER_URL}/auth/sign_in/prepare -d @- <<EOF
{"state_code": "${STATE}","redirect_uri":"${REDIRECT_URI}"}
EOF
)
echo ${RESULT} | jq .

QR_TOKEN=$(echo ${RESULT} | jq -r .qr_token)


# 08. FZ (認証アプリ) サインイン (TOTPあり)
echo "08. FZ (認証アプリ) サインイン (TOTPあり)開始"
TOTP=$(oathtool --totp -b $TOTP_SECRET)

FIN_SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/auth/sign_in -d @- <<EOF
{"sign_in_id": "${FIN_SIGN_IN_ID}", "password": "${FIN_PASSWORD}", "totp": "${TOTP}"}
EOF
)
echo ${FIN_SIGN_IN_RESULT} | jq .

FIN_ACCESS_TOKEN=$(echo ${FIN_SIGN_IN_RESULT} | jq -r .access_token)
FIN_ID_TOKEN=$(echo ${FIN_SIGN_IN_RESULT} | jq -r .id_token)


# 09. FZ 認証アプリ QRデータ 連携
echo "09. FZ 認証アプリ QRデータ 連携"
TOTP=$(oathtool --totp -b $TOTP_SECRET)

RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/auth/qr_token -d @- <<EOF
{"qr_token": "${QR_TOKEN}", "totp": "${TOTP}"}
EOF
)
echo ${RESULT} | jq .

# 10. Signer サインインQRコード処理状況確認
echo "10. Signer サインインQRコード処理状況確認"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" $CUSTOM_HEADER \
  ${SIGNER_URL}/auth/sign_in/qr_token -d @- <<EOF
{"state_code": "${STATE}","qr_token":"${QR_TOKEN}"}
EOF
)
echo ${RESULT} | jq .

REDIRECT_URI=$(echo ${RESULT} | jq -r .redirect_uri)
# state_codeを抽出
STATE_CODE=$(echo "$REDIRECT_URI" | sed -n 's/.*state_code=\([^&]*\).*/\1/p')
# auth_codeを抽出
AUTH_CODE=$(echo "$REDIRECT_URI" | sed -n 's/.*auth_code=\(.*\)/\1/p')


# 11. Signer トークン発行
echo "11. Signer トークン発行"
RESULT=$(curl -sS -X GET -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/signer/token?state_code=$STATE_CODE\&code=$AUTH_CODE )
echo ${RESULT} | jq .
DC_BANK_NUMBER=$(echo ${RESULT} | jq -r .dc_bank_number)


# 12. BZ ビジネスゾーンアカウント 開設
echo "12. BZ ビジネスゾーンアカウント 開設	"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/holders/my_account/synchronous -d @- <<EOF
{"dc_bank_number": "${DC_BANK_NUMBER}","account_name":"バリデータアカウント"}
EOF
)
echo ${RESULT} | jq .

# 利用確定できるようにスリープを入れる
echo "relayerの連携を待つためここから10秒間スリープします。"
echo "環境状態によっては10秒以上かかる場合があるのでその場合はエラーとなります"
sleep 10

# 13. FZ ビジネスゾーンアカウント利用確定
echo "13. FZ ビジネスゾーンアカウント利用確定"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${FIN_BASE_URL}/holders/business/apply -d @- <<EOF
{"zone_id": "3001"}
EOF
)
echo ${RESULT} | jq .
ZONE_ID=$(echo ${RESULT} | jq -r .zone_id)

if [ $ZONE_ID = null ]; then
    echo "FinZoneの利用確定が完了していないので再度時間をおいて以下のシェルを実行してください"
    echo "./biz-account-apply ${FIN_SIGN_IN_ID} ${FIN_PASSWORD} ${TOTP_SECRET}"
    echo ""
    echo ""
fi

# 14. BZ 事業者向け 保有アカウント紐づけ
echo "14. BZ 事業者向け 保有アカウント紐づけ	"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${BIZ_SERVICE_OWNER_ACCESS_TOKEN}" $CUSTOM_HEADER \
  ${BPM_BASE_URL}/services/validator/account -d @- <<EOF
{"id_token": "${BIZ_ID_TOKEN}"}
EOF
)
echo ${RESULT} | jq .


echo "========================================================================"
echo " BizZone バリデータに紐づくアカウント作成結果"
echo "    BIZ_SIGN_IN_ID : "$BIZ_SIGN_IN_ID
echo "    PASSWORD       : "$PASSWORD
echo "    DC_BANK_NUMBER : "$DC_BANK_NUMBER
echo "    EMAIL          : "$EMAIL
echo ""
echo "    BIZ_ACCESS_TOKEN : "$BIZ_ACCESS_TOKEN
echo ""
echo "========================================================================"
echo ""
