# この設定ファイルは原則編集しないこと。
# 個別の設定が必要な場合は、同一ディレクトリに .env ファイルを作成し、そちらに記載すること。

# ISSUER_NAME="GMOあおぞらネット銀行"
ISSUER_NAME1="GMOAozoraNetBank"
VALIDATOR_NAME1="GMOAozoraNetBank"
BANK_CODE1="0310"
ADMIN_USER_NAME1="ギンコウ　カンリシャ"

ISSUER_NAME2="TENANT2Bank"
VALIDATOR_NAME2="TENANT2Bank"
BANK_CODE2="0990"
ADMIN_USER_NAME2="ギンコウ　カンリシャ"

ISSUER_NAME3="TENANT3Bank"
VALIDATOR_NAME3="TENANT3Bank"
BANK_CODE3="1990"
ADMIN_USER_NAME3="ギンコウ　カンリシャ"

ADMIN_ID1="zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"

ZONE_ID1="3000"
SERVICE_ID1="0d367be2"

ZONE_ID2="3000"
SERVICE_ID2="43e4abef"

ZONE_ID3="3000"
SERVICE_ID3="9946abd1"

ADMIN_CLIENT_ID="77ssadfadojr2rkf9hofbio8e"

# DBの設定
export DB=localhost
export BPM_DB_PORT=45432
export DB_USER=bpm_user
export DB_NAME=bpm_db
export PGPASSWORD=bpm_password

export BPM_BASE_URL=http://localhost:48180
export FIN_BASE_URL=http://localhost:48180

# Signerの BASEURL
export SIGNER_URL=http://localhost:48010

export IDP_BPM_MOCK_PORT=48280
export USER_POOL_ID=USER_local-cognito-user-pool
export COGNITO_LOCAL_ENDPOINT=http://localhost:49329

export LOCALSTACK_PORT=44566
export LOCALSTACK_URL=http://localhost:${LOCALSTACK_PORT}
export AWS_REGION=ap-northeast-1


##############################################################################
# アカウント登録

# 電話番号 (固定する場合に 0x0 始まりでハイフンなしの11桁の数値を指定 : 例 07011112222)
export PHONE_NUMBER=
# 変更後のPASSWORD
export PASSWORD=Password1

# ユーザ作成で利用する変数
# ユーザ管理者の名前
export USER_OWNER_NAME=ユーザカンリシャ
# 業務承認者の名前
export APPROVER_NAME=テストギョウムショウニンシャ
# 業務担当者の名前
export OPERATOR_NAME=テストギョウムタントウシャ
