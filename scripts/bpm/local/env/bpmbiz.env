# この設定ファイルは原則編集しないこと。
# 個別の設定が必要な場合は、同一ディレクトリに .env ファイルを作成し、そちらに記載すること。

ISSUER_NAME1=
VALIDATOR_NAME1="IIJ-DC"
BANK_CODE1=
ADMIN_USER_NAME1="ジギョウ　カンリシャ"

ISSUER_NAME2=
VALIDATOR_NAME2="TENANT2"
BANK_CODE2=
ADMIN_USER_NAME2="ジギョウ　カンリシャ"

ISSUER_NAME3=
VALIDATOR_NAME3="TENANT3"
BANK_CODE3=
ADMIN_USER_NAME3="ジギョウ　カンリシャ"

ADMIN_ID1="zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
ADMIN_ID2="yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy"

ZONE_ID1="3001"
SERVICE_ID1="0d367be2"

ZONE_ID2="3001"
SERVICE_ID2="43e4abef"

ZONE_ID3="3002"
SERVICE_ID3="9946abd1"

ADMIN_CLIENT_ID="77ssadfadojr2rkf9hofbio8e"

# DBの設定
export DB=localhost
export BPM_DB_PORT=55432
export DB_USER=bpm_user
export DB_NAME=bpm_db
export PGPASSWORD=bpm_password

export BPM_BASE_URL=http://localhost:58180
export FIN_BASE_URL=http://localhost:48180

# Signerの BASEURL
export SIGNER_URL=http://localhost:48010

export IDP_BPM_MOCK_PORT=58280
export USER_POOL_ID=USER_local-cognito-user-pool
export COGNITO_LOCAL_ENDPOINT=http://localhost:59329

export LOCALSTACK_PORT=54566
export LOCALSTACK_URL=http://localhost:${LOCALSTACK_PORT}
export AWS_REGION=ap-northeast-1

##############################################################################
# アカウント登録

# 変更後のPASSWORD
export PASSWORD=Password1

# ユーザ作成で利用する変数
# ユーザ管理者の名前
export USER_OWNER_NAME=ユーザカンリシャ
# 業務承認者の名前
export APPROVER_NAME=テストギョウムショウニンシャ
# 業務担当者の名前
export OPERATOR_NAME=テストギョウムタントウシャ
