#!/bin/bash
# 初期設定待ちのユーザをアクティブに変更します
# 初期設定待ち以外の状態だったら、スクリプトは終了

# 引数チェック
if [ $# -ne 3 ]; then
    echo ""
    echo "    [USAGE] ./activate-dc-user-bpmfin.sh [sign_in_id] [password] [dc_bank_number]"
    echo ""
    exit 9
fi

FIN_SIGN_IN_ID=$1
FIN_PASSWORD=$2
DC_BANK_NUMBER=$3

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/bpmfin.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi


#初回サインイン
echo "初回サインイン開始"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" \
  ${BPM_BASE_URL}/auth/sign_in -d @- <<EOF
{"sign_in_id": "${FIN_SIGN_IN_ID}", "password": "${FIN_PASSWORD}"}
EOF
)
echo ${SIGN_IN_RESULT}
FIN_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

if [ $FIN_ACCESS_TOKEN = null ]; then
    echo "認証アプリ初回サインイン (TOTPなし) : 失敗しました "$FIN_SIGN_IN_ID
    exit 1
fi

#ユーザステータス取得
echo "ユーザステータス取得開始"
USER_STATE=$(psql -h $DB -p $BPM_DB_PORT -U $DB_USER -d $DB_NAME -tA -c \
  "SELECT user_status FROM dc_user WHERE sign_in_id='$FIN_SIGN_IN_ID'")
STATUS=$(echo ${USER_STATE} | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

if [ $STATUS != 'initializing' ]; then
    echo "初期設定待ちではないのでスクリプトを終了します"
    exit 0
fi

#任意の電話番号を登録
PHONE_NUMBER=$(bash -c 'printf "090%08d" $(( RANDOM % 10000 ))$(( RANDOM % 10000 ))')
PASSWORD=Password1

echo "電話番号登録開始"
RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}" \
  ${BPM_BASE_URL}/auth/my_user/telephone -d @- <<EOF
{"phone_number": "${PHONE_NUMBER}"}
EOF
)
echo ${RESULT} | jq .

COGNITO_PHONE_NUMBER='+81'${PHONE_NUMBER:1}
COGNITO_USER_NAME='dc_user_'$FIN_SIGN_IN_ID

if [ -z "${COGNITO_LOCAL_ENDPOINT}" ]; then
  # local環境以外
  # CognitoLocalの電話番号を検証済の状態へ更新する
  aws cognito-idp admin-update-user-attributes \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --user-attributes Name=phone_number,Value=${COGNITO_PHONE_NUMBER} Name=phone_number_verified,Value=true

  # パスワードの強制変更
  aws cognito-idp admin-set-user-password \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --password $PASSWORD --permanent
else
  # local環境
  # CognitoLocalの電話番号を検証済の状態へ更新する
  aws cognito-idp admin-update-user-attributes \
    --endpoint-url ${COGNITO_LOCAL_ENDPOINT} \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --user-attributes Name=phone_number,Value=${COGNITO_PHONE_NUMBER} Name=phone_number_verified,Value=true

  # パスワードの強制変更
  aws cognito-idp admin-set-user-password \
    --endpoint-url ${COGNITO_LOCAL_ENDPOINT} \
    --user-pool-id ${USER_POOL_ID} \
    --username ${COGNITO_USER_NAME} \
    --password $PASSWORD --permanent
fi

# TOTP鍵の生成
RANDOM_BYTES=$(openssl rand -base64 40 | head -c 40 | base64 -d | xxd -p)
TOTP_SECRET=$(echo $RANDOM_BYTES | xxd -r -p | base32)

# BPM DBへの登録
SQL_RESPONSE1=$(psql -h $DB -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
  "INSERT INTO dc_user_phone (sign_in_id, phone_number) VALUES ('$FIN_SIGN_IN_ID','$PHONE_NUMBER')")
SQL_RESPONSE2=$(psql -h $DB -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
  "INSERT INTO dc_user_totp (sign_in_id, totp_secret) VALUES ('$FIN_SIGN_IN_ID','$TOTP_SECRET')")
SQL_RESPONSE3=$(psql -h $DB -p ${BPM_DB_PORT} -U ${DB_USER} -d ${DB_NAME} -t -c \
  "UPDATE dc_user SET user_status = 'active' WHERE sign_in_id = '$FIN_SIGN_IN_ID'")

# CSV出力
# 出力項目1: サインインID, パスワード、TOTPシークレット
# 出力項目2: サインインID, DC口座番号
echo "${FIN_SIGN_IN_ID},${PASSWORD},${TOTP_SECRET}" >> ~/tmp/user.csv
echo "${FIN_SIGN_IN_ID},${DC_BANK_NUMBER}" >> ~/tmp/account.csv

echo "    TOTP_SECRET           : "$TOTP_SECRET
