#!/bin/bash

TOTP_SECRET=$1
SIGN_IN_ID=$2

# サインイン
echo "サインイン開始"
STATE_CODE=$(date "+%Y%m%d%H%M%S")
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" \
  $BPM_BASE_URL/holders/sign_in/prepare -d @- <<EOF
{"state_code":"${STATE_CODE}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .
QR_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r ".qr_token")


# 認証アプリサインイン
echo "認証アプリサインイン開始"
TOTP=$(oathtool --totp -b $TOTP_SECRET)
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" \
  $BPM_BASE_URL/auth/sign_in -d @- <<EOF
{"sign_in_id": "${SIGN_IN_ID}", "password": "${PASSWORD}", "totp": "${TOTP}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

FIN_AUTH_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .access_token)

# QRコード読み込み
echo "QRコード読み込み開始"
TOTP=$(oathtool --totp -b $TOTP_SECRET)
curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${FIN_AUTH_ACCESS_TOKEN}" \
  $BPM_BASE_URL/auth/qr_token -d @- <<EOF | jq .
{"qr_token": "${QR_TOKEN}", "totp": "${TOTP}"}
EOF

# サインインQRコード処理状況確認
echo "サインインQRコード処理状況確認開始"
SIGN_IN_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" \
  $BPM_BASE_URL/holders/qr_token/${QR_TOKEN}/sign_in -d @- <<EOF
{"state_code":"${STATE_CODE}"}
EOF
)
echo ${SIGN_IN_RESULT} | jq .

# DCJPY WEB 用のアクセストークン、およびIDトークン
FIN_ACCESS_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .sign_in_detail.access_token)
# FIN_ID_TOKEN=$(echo ${SIGN_IN_RESULT} | jq -r .sign_in_detail.id_token)

# DCJPY発行実行(承認なし)
echo "DCJPY発行実行(承認なし)開始"
REQUEST_ID=request-$(date "+%Y%m%d%H%M%S")

MINT_RESULT=$(curl -sS -X POST -H "Content-Type: application/json"  -H "Authorization: Bearer ${FIN_ACCESS_TOKEN}"\
    $BPM_BASE_URL/holders/transactions/mint -d @- <<EOF
{"request_id": "${REQUEST_ID}", "mint_amount":"99999"}
EOF
)
echo ${MINT_RESULT}

MINT_AMOUNT=$(echo "${MINT_RESULT}" | grep -o '"mint_amount":[0-9]*' | awk -F: '{print $2}')
echo "${SIGN_IN_ID}に${MINT_AMOUNT}発行しました。"