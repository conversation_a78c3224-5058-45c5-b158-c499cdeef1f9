#!/bin/bash
# 性能試験用のデータを作成します。
# CSVファイルを~/tmp/に出力します。
# 出力されるファイルは、下記2種類となります。
# user.csv, account.csv

# 使い方
# nohup bash shell名 > ログファイルのパス  &

# CSVファイルの作成
if [ ! -f ~/tmp/user.csv ]; then
    touch "${HOME}/tmp/user.csv"
    echo "sign_in_id,password,totp_secret" >> ~/tmp/user.csv
fi

if [ ! -f ~/tmp/account.csv ]; then
    touch "${HOME}/tmp/account.csv"
    echo "sign_in_id,dc_bank_number" >> ~/tmp/account.csv
fi


#for i in {1..1000}; do
for i in {1..1}; do
    ./create-account-bpmfin.sh "{$LOG_DIR}"
done