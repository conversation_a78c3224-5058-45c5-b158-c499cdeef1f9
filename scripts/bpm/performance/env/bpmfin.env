# この設定ファイルは原則編集しないこと。
# 個別の設定が必要な場合は、同一ディレクトリに .env ファイルを作成し、そちらに記載すること。

ZONE_ID="3000"
SERVICE_ID="0"

# DBの設定
export DB=localhost
export BPM_DB_PORT=5432

# AWS環境情報一覧(Dev)の、RDS-UserNameの値
export DB_USER=Administrator
export DB_NAME=bpm_db

# AWS環境情報一覧(Dev)の、RDS-Passwordの値
export PGPASSWORD="JKZCBpe_5HR*CF]8"

# AWS環境情報一覧(Dev)の、ALB-EKS-BPM-M-URLの値
export BPM_BASE_URL=https://api.dev-bpmfin.dc-labo.com
export FIN_BASE_URL=https://api.dev-bpmfin.dc-labo.com

# AWS環境情報一覧(Dev)の、Cognito-UserPoolIDの値
export USER_POOL_ID="ap-northeast-1_G7NZ6E1Mw"

# local環境の場合のみ指定。
export COGNITO_LOCAL_ENDPOINT=""

##############################################################################
# アカウント登録

# 電話番号 (固定する場合に 0x0 始まりでハイフンなしの11桁の数値を指定 : 例 07011112222)
export PHONE_NUMBER=
# 変更後のPASSWORD
export PASSWORD=Password1

# ユーザ作成で利用する変数
# ユーザ管理者の名前
export USER_OWNER_NAME=ユーザカンリシャ
export USER_OWNER_ROLE_ID=00000000-0000-4000-0000-000000000011
# 業務承認者の名前
export APPROVER_NAME=テストギョウムショウニンシャ
export APPROVER_ROLE_ID=00000000-0000-4000-0000-000000000013
# 業務担当者の名前
export OPERATOR_NAME=テストギョウムタントウシャ
export OPERATOR_ROLE_ID=00000000-0000-4000-0000-000000000012
