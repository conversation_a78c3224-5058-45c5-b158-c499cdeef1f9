#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

read -p "Enter a repository name: " name
REPO_NAME="${name}"
choice 'Wanna register by "'${REPO_NAME}'"?'

message "info" "Registering a new repository."
PARAMS=$(
  cat <<EOS
{
  "name": "${REPO_NAME}",
  "auto_init": true,
  "private": true
}
EOS
)
curl -i -H "Authorization: token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/orgs/${ORG}/repos

message "info" "Updateing repository settings."
PARAMS=$(
  cat <<EOS
{
  "delete_branch_on_merge": true
}
EOS
)

curl \
  -X PATCH \
  -H "Authorization:token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/repos/${OWNER}/${REPO_NAME}

# Updating repository permission
NO_CONFIRMATION="true" "$BASE"/../teams/grant-repo-permission-to-team.sh dev-team ${REPO_NAME} push
NO_CONFIRMATION="true" "$BASE"/../teams/grant-repo-permission-to-team.sh read-only-team ${REPO_NAME} pull

message "info" "Updating develop branch protection rule."
PARAMS=$(
  cat <<EOS
{
  "required_pull_request_reviews": {
    "dismiss_stale_reviews": true,
    "require_code_owner_reviews": false,
    "required_approving_review_count": 1
  },
  "required_status_checks": null,
  "enforce_admins": false,
  "restrictions": {
    "users": ["kenichi-shimizu-dc"],
    "teams": []
  }
}
EOS
)
curl \
  -X PUT \
  -H "Accept: application/vnd.github.luke-cage-preview+json" \
  -H "Authorization: token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/repos/${OWNER}/${REPO_NAME}/branches/develop/protection

message "info" "Create production branch."
SHA=$(curl -s -H "Authorization: token ${TOKEN}" https://api.github.com/repos/${OWNER}/${REPO_NAME}/git/refs/heads/develop | jq -r .object.sha)

PARAMS=$(
  cat <<EOS
{
  "ref": "refs/heads/production",
  "sha": "${SHA}"
}
EOS
)
curl \
  -X POST \
  -s -H "Authorization: token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/repos/${OWNER}/${REPO_NAME}/git/refs

message "info" "Updating production branch protection rule."
PARAMS=$(
  cat <<EOS
{
  "required_pull_request_reviews": {
    "dismiss_stale_reviews": true,
    "require_code_owner_reviews": false,
    "required_approving_review_count": 1
  },
  "required_status_checks": null,
  "enforce_admins": false,
  "restrictions": {
    "users": ["kenichi-shimizu-dc"],
    "teams": []
  }
}
EOS
)
curl \
  -X PUT \
  -H "Accept: application/vnd.github.luke-cage-preview+json" \
  -H "Authorization: token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/repos/${OWNER}/${REPO_NAME}/branches/production/protection

message "info" "Successful!"
