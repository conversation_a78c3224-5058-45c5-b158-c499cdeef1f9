#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

if [[ $# -ne 3 ]]; then
  message "err" "three params are required. '$0 [team name] [repository name] [permission]'"
  exit 1
fi

TEAM_NAME=$1
REPOSITORY_NAME=$2
PERMISSION=$3

if [[ -z ${NO_CONFIRMATION} ]]; then
  choice "Wanna grant a '${REPOSITORY_NAME}' '${PERMISSION}' permisson to '${TEAM_NAME}'"
fi

message "info" "Updating repository permission. (team name: ${TEAM_NAME}, repository name: ${REPOSITORY_NAME}, permission: ${PERMISSION})"

PARAMS=$(
  cat <<EOS
{
  "permission": "${PERMISSION}"
}
EOS
)

curl \
  -X PUT \
  -H "Authorization: token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/orgs/${ORG}/teams/${TEAM_NAME}/repos/${OWNER}/${REPOSITORY_NAME}
