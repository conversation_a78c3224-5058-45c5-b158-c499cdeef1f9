#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

if [[ $# -ne 2 ]]; then
  message "err" "two params are required. '$0 [team name] [file path]'"
  exit 1
fi

TEAM_NAME=$1
REPOSITORY_FILE_PATH=$(
  cd "$(dirname "$2")"
  pwd -P
)/$(basename "$2")"" # to absolute path

choice "Wanna revoke the permisson of repositories listed in '$2' from '${TEAM_NAME}'"

cat ${REPOSITORY_FILE_PATH} | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  NO_CONFIRMATION="true" "$BASE/revoke-permission-from-team.sh" ${TEAM_NAME} ${REPO}
done
