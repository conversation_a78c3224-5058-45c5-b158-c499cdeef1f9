#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

if [[ $# -ne 3 ]]; then
  message "err" "three params are required. '$0 [team name] [permission] [file path]'"
  exit 1
fi

TEAM_NAME=$1
PERMISSION=$2
REPOSITORY_FILE_PATH=$(
  cd "$(dirname "$3")"
  pwd -P
)/$(basename "$3")"" # to absolute path

choice "Wanna grant a '${PERMISSION}' permisson of repositories listed in '$3' to '${TEAM_NAME}'"

cat ${REPOSITORY_FILE_PATH} | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  NO_CONFIRMATION="true" "$BASE/grant-repo-permission-to-team.sh" ${TEAM_NAME} ${REPO} ${PERMISSION}
done
