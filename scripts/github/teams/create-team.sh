#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

if [[ $# -ne 2 ]]; then
  message "err" "two param are required. '$0 [team name] [description]'"
  exit 1
fi

TEAM_NAME=$1
DESCRIPTION=$2

choice "Wanna create a team '${TEAM_NAME}'?"

message "info" "Creating a team. (team name: ${TEAM_NAME}, description: ${DESCRIPTION})"

PARAMS=$(
  cat <<EOS
{
  "name": "${TEAM_NAME}",
  "description": "${DESCRIPTION}",
  "notification_setting": "notifications_enabled",
  "privacy": "secret"
}
EOS
)

curl \
  -X POST \
  -H "Authorization: token ${TOKEN}" \
  -d "${PARAMS}" \
  https://api.github.com/orgs/${ORG}/teams
