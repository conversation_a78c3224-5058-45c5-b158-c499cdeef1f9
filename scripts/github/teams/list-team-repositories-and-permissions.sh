#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

if [[ $# -ne 1 ]]; then
  message "err" "one param is required. '$0 [team name]'"
  exit 1
fi

TEAM_NAME=$1
message "info" "Listing the team repositories and permissions. (team name: ${TEAM_NAME})"

status=$(curl \
  -s \
  -w "%{http_code}" \
  -o /dev/null \
  -H "Authorization: token ${TOKEN}" \
  https://api.github.com/orgs/${ORG}/teams/${TEAM_NAME}/repos)

if [[ $status -eq 404 ]]; then
  message "warn" "No team found. (team name: ${TEAM_NAME})"
  exit
fi

declare -a all_repos=()
page=1
while true; do
  response=$(curl \
    -s \
    -H "Authorization: token ${TOKEN}" \
    https://api.github.com/orgs/${ORG}/teams/${TEAM_NAME}/repos?per_page=100\&page=${page})
  
  length=$(echo $response | jq '. | length')
  if [[ $length -eq 0 ]]; then
    break
  fi

  all_repos+=$response
  
  page=$((page + 1))
done

echo '"name","role_name","permissions.admin","permissions.maintain","permissions.push","permissions.triage","permissions.pull"'
echo ${all_repos[@]} | jq -s -c flatten | jq -r '.[] | [.name, .role_name, .permissions.admin, .permissions.maintain, .permissions.push, .permissions.triage, .permissions.pull] | @csv'
