#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../.env
source "$BASE"/../../bin/common.sh

if [[ $# -ne 2 ]]; then
  message "err" "two params are required. '$0 [team name] [repository name]'"
  exit 1
fi

TEAM_NAME=$1
REPOSITORY_NAME=$2

if [[ -z ${NO_CONFIRMATION} ]]; then
  choice "Wanna revoke the '${REPOSITORY_NAME}' permisson from '${TEAM_NAME}'"
fi

message "info" "Removing repository permission. (team name: ${TEAM_NAME}, repository name: ${REPOSITORY_NAME})"

curl \
  -X DELETE \
  -H "Authorization: token ${TOKEN}" \
  https://api.github.com/orgs/${ORG}/teams/${TEAM_NAME}/repos/${OWNER}/${REPOSITORY_NAME}
