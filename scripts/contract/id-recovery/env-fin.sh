export DB=localhost
echo "DB:$DB"
export DB_PORT=5432
echo "DB_PORT:$DB_PORT"
export DB_USER=Administrator
echo "DB_USER:$DB_USER"
export DB_NAME=postgres
echo "DB_NAME:$DB_NAME"
export PGPASSWORD=password
echo "PGPASSWORD:$PGPASSWORD"
export KEY_ADMIN=d66412a45af07b3e319ad680fb361e90c7d41ccdb6f8002d7c6a5c46f949e04a
echo "KEY_ADMIN:$KEY_ADMIN"
export NETWORK=main
echo "NETWORK:$NETWORK"
export CONTRACT_PATH=~/dcbg-dcf-contract-sandbox
echo "CONTRACT_PATH:$CONTRACT_PATH"
