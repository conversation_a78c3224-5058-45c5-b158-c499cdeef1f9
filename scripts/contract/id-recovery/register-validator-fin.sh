#!/bin/bash

# CLIENT IDの確認
if [ $# -ne 1 ]; then
    echo "[引数なし] ./register-validator-fin.sh [client_id]"
    exit 9
fi
CLIENT_ID=$1

# 環境変数
source ./env-fin.sh
cd $CONTRACT_PATH
echo "#### ここまで環境変数 ####"

SQL_RESPONCE=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select issuer_id from identity_authorities where sub='$CLIENT_ID'"`
ISSUER_ID=`echo $SQL_RESPONCE`
if [ -z $ISSUER_ID ]; then
    echo "identity_authoritiesのissuer_idが見つかりません"
    exit 9
fi
SQL_RESPONCE=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$CLIENT_ID'"`
VALIDATOR_ID=`echo $SQL_RESPONCE`
if [ -z $VALIDATOR_ID ]; then
    echo "identity_authoritiesのvalidator_idが見つかりません"
    exit 9
fi

SQL_RESPONCE=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select private_key from external_signers where signer_id='$ISSUER_ID'"`
ISSUER_PK=`echo $SQL_RESPONCE`
if [ -z "$ISSUER_PK" ]; then
   echo "external_signersにissuer_idのレコードがありません"
   exit 9
fi
SQL_RESPONCE=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select private_key from external_signers where signer_id='$VALIDATOR_ID'"`
VALIDATOR_PK=`echo $SQL_RESPONCE`
if [ -z "$VALIDATOR_PK" ]; then
   echo "external_signersにvalidator_idのレコードがありません"
   exit 9
fi

# コントラクトのシェル実行
ISSUER_NAME=issuer-name
tools/registerIssuer.sh $ISSUER_ID $ISSUER_NAME $ISSUER_PK $KEY_ADMIN

VALIDATOR_NAME=validator-name
tools/registerValid.sh $VALIDATOR_ID $ISSUER_ID $VALIDATOR_NAME $VALIDATOR_PK $KEY_ADMIN
