#!/bin/bash

# CLIENT IDの確認
if [ $# -ne 1 ]; then
    echo "[引数なし] ./register-validator-ind.sh [client_id]"
    exit 9
fi
CLIENT_ID=$1

# 環境変数
source ./env-ind.sh
cd $CONTRACT_PATH
echo "#### ここまで環境変数 ####"

ISSUER_COUNT=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select count(issuer_id) from identity_authorities where issuer_id is not null"`
if [ 0 != $ISSUER_COUNT ]; then
    echo "共通領域で実行出来ません $ISSUER_COUNT"
    exit 9
fi
SQL_RESPONCE=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$CLIENT_ID'"`
VALIDATOR_ID=`echo $SQL_RESPONCE`
if [ -z $VALIDATOR_ID ]; then
    echo "identity_authoritiesのvalidator_idが見つかりません"
    exit 9
fi

SQL_RESPONCE=`psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select private_key from external_signers where signer_id='$VALIDATOR_ID'"`
VALIDATOR_PK=`echo $SQL_RESPONCE`
if [ -z $VALIDATOR_PK ]; then
   echo "external_signersにvalidator_idのレコードがありません"
   exit 9
fi

# コントラクトのシェル実行
VALIDATOR_NAME=validator-name
tools/registerValid.sh $VALIDATOR_ID 0 $VALIDATOR_NAME $VALIDATOR_PK $KEY_ADMIN
