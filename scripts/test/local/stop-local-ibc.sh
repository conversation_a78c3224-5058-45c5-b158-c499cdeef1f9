#!/bin/bash -e

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

cat "${BASE}/repos/ibc-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done

# call subscript
ENV=ibc "${BASE}/subscripts/stop-ibc.sh"
