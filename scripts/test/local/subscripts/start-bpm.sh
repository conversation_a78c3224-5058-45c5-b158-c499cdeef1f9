#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

# validation
## validate params
if [[ -z "${ENV}" ]]; then
  echo "'ENV' is not set."
  exit 1
fi

# --build でビルドを実行する
PARAM=$1
if [[ $PARAM = "--build" ]]; then
  BUILD_FLAG=$1
fi

# local-environment
LOCAL_ENV_REPO="${REPO_PATH}/dcbg-dcjpy-bpm-local-environment"
pushd "${LOCAL_ENV_REPO}" >/dev/null

cp ../dcbg-dcjpy-bpm-server/bpm-local-env/bpm-db/* ./docker/bpm-db/
rm ./docker/bpm-db/V9*.sql

docker compose -f docker-compose-local.yml --profile "${ENV}" --env-file "./docker/local/env/${ENV}.env" -p "${ENV}" up -d

LOCALSTACK_PORT="4566"
if [[ "${ENV}" == "bpmfin" ]]; then
  LOCALSTACK_PORT="44566"
fi
if [[ "${ENV}" == "bpmbiz" ]]; then
  LOCALSTACK_PORT="54566"
fi

while true; do
  echo "[${ENV}] waiting for the localstack up and running. LOCALSTACK_PORT:${LOCALSTACK_PORT}"
  status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:${LOCALSTACK_PORT}/_localstack/health)
  if [[ "${status}" == "200" ]]; then
    break
  fi
  sleep 5
done
popd >/dev/null

# application repos
while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  repo="${REPO_PATH}/${REPO}"
  pushd "${repo}" >/dev/null

  if [ -f ./docker/local/prebuild.sh ]; then
    # 標準入力の消費を防ぐため、/dev/nullからの入力を渡す (これをしないと、prebuild.shの実行が中断される)
    < /dev/null ./docker/local/prebuild.sh ${ENV} ${BUILD_FLAG}
  fi
  docker compose -f docker-compose-local.yml --profile "${ENV}" --env-file "./docker/local/env/${ENV}.env" -p "${ENV}" up -d ${BUILD_FLAG}

  popd >/dev/null
done < "${BASE}/../repos/bpm-repositories.txt"
