#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

OPTIONS="$@"

REPO_PATH="${REPO_PATH:-$BASE/../../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

# validation
## validate params
if [[ -z "${ENV}" ]]; then
  echo "'ENV' is not set."
  exit 1
fi

# --build でビルドを実行する
PARAM=$1
if [[ $PARAM = "--build" ]]; then
  BUILD_FLAG=$1
fi

# local-environment
LOCAL_ENV_REPO="${REPO_PATH}/dcbg-dcjpy-core-local-environment"
pushd "${LOCAL_ENV_REPO}" >/dev/null
docker compose -f docker-compose-local.yml --profile "${ENV}" --env-file "./docker/local/env/${ENV}.env" -p "${ENV}" up -d

LOCALSTACK_PORT="4566"
if [[ "${ENV}" == "fin" ]]; then
  LOCALSTACK_PORT="14566"
fi
if [[ "${ENV}" == "biz" ]]; then
  LOCALSTACK_PORT="24566"
fi

while true; do
  echo "[${ENV}] waiting for the localstack up and running. LOCALSTACK_PORT:${LOCALSTACK_PORT}"
  status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:${LOCALSTACK_PORT}/_localstack/health)
  if [[ "${status}" == "200" ]]; then
    break
  fi
  sleep 5
done

QUEUE_NAME=dcjpy_bcclient_queue_send-transaction.fifo
while true; do
  echo "[${ENV}] waiting for the SQS are ready: QUEUE_NAME: ${QUEUE_NAME} / LOCALSTACK_PORT: ${LOCALSTACK_PORT}"
  status=$(aws --endpoint-url=http://localhost:${LOCALSTACK_PORT} \
    sqs get-queue-url --queue-name ${QUEUE_NAME} --output text --query 'QueueUrl' 2>/dev/null)
  if [[ -n "${status}" ]]; then
    break
  fi
  sleep 15
done

popd >/dev/null

# bcmonitoring-stream (lambdaをlocalstackにアップロードするための事前準備)
BCMONITOING_STREAM_REPO="${REPO_PATH}/dcbg-dcjpy-bcmonitoring-stream"
pushd "${BCMONITOING_STREAM_REPO}" >/dev/null
./scripts/create-function-zip.sh
popd >/dev/null

# application repos
while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  repo="${REPO_PATH}/${REPO}"
  pushd "${repo}" >/dev/null

  if [ -f ./docker/local/prebuild.sh ]; then
    # 標準入力の消費を防ぐため、/dev/null からの入力を渡す (これをしないと、prebuild.shの実行が中断される)
    < /dev/null ./docker/local/prebuild.sh ${ENV} ${BUILD_FLAG}
  fi
  docker compose -f docker-compose-local.yml --profile "${ENV}" --env-file "./docker/local/env/${ENV}.env" -p "${ENV}" up -d ${BUILD_FLAG}

  popd >/dev/null
done < "${BASE}/../repos/core-repositories.txt"
