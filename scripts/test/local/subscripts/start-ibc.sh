#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

# validation
## validate params
if [[ -z "${ENV}" ]]; then
  echo "'ENV' is not set."
  exit 1
fi

# repositories
BESU_REPO="${REPO_PATH}/dcbg-dcf-besu-template/besu-template-multiple-zone-static-docker-local"
RELAYER_REPO="${REPO_PATH}/dcbg-dcjpy-relayer"
BIZ_ZONE_COUNT=1

# -biz N でマルチテナント指定
PARAM_NAME=$1
if [[ $PARAM_NAME = "-biz" ]]; then
  BIZ_ZONE_COUNT=$2
fi
LOCAL_ENV_REPO="${REPO_PATH}/dcbg-dcjpy-core-local-environment"

# BESU
pushd "${BESU_REPO}" >/dev/null
docker compose up -d
popd >/dev/null

while true; do
  echo "waiting for the besu network up and running"
  fin_status=$(curl -s -o /dev/null -w "%{http_code}" localhost:18451/liveness)
  biz_status=$(curl -s -o /dev/null -w "%{http_code}" localhost:28451/liveness)
  if [[ "${fin_status}" == "200" && "${biz_status}" == "200" ]]; then
    break
  fi
  sleep 5
done

# Relayer
## Handshake
pushd "${RELAYER_REPO}" >/dev/null

### Packet Relay Daemon
if [ "${BIZ_ZONE_COUNT}" -lt 2 ]; then
  ENV=ibc docker compose -f docker-compose-local.yml --profile "${ENV}" -p "${ENV}" up -d relayer-account-sync-1 relayer-balance-sync-1 relayer-token-transfer-1
else
  ENV=ibc docker compose -f docker-compose-local.yml --profile "${ENV}" -p "${ENV}" up -d relayer-account-sync-1 relayer-balance-sync-1 relayer-token-transfer-1 relayer-account-sync-2 relayer-balance-sync-2 relayer-token-transfer-2
fi
popd >/dev/null
