#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする
LOCAL_ENV_REPO="${REPO_PATH}/dcbg-dcjpy-core-local-environment"

# validation
## validate params
if [[ -z "${ENV}" ]]; then
  echo "'ENV' is not set."
  exit 1
fi

# repositories
BESU_REPO="${REPO_PATH}/dcbg-dcf-besu-template/besu-template-multiple-zone-static-docker-local"
RELAYER_REPO="${REPO_PATH}/dcbg-dcjpy-relayer"

# BESU
pushd "${BESU_REPO}" >/dev/null
docker compose down
popd >/dev/null

# Relayer
pushd "${RELAYER_REPO}" >/dev/null
docker compose -f docker-compose-local.yml --profile "${ENV}" -p "${ENV}" down
popd >/dev/null

# stop localStack
pushd "${LOCAL_ENV_REPO}" >/dev/null
docker compose -f docker-compose-local.yml --profile fin -p fin down
docker compose -f docker-compose-local.yml --profile biz -p biz down