#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

# validation
## validate params
if [[ -z "${ENV}" ]]; then
  echo "'ENV' is not set."
  exit 1
fi

# all bpm repos
cat "${BASE}/../repos/bpm-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  repo="${REPO_PATH}/${REPO}"
  pushd "${repo}" >/dev/null
  docker compose -f docker-compose-local.yml --profile "${ENV}" --env-file "./docker/local/env/${ENV}.env" -p "${ENV}" down
  popd >/dev/null
done
