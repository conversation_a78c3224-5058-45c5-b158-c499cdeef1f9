#!/bin/bash -e

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

cat "${BASE}/repos/ibc-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done
cat "${BASE}/repos/core-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done
cat "${BASE}/repos/bpm-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done

# BZ
ENV=biz "${BASE}/subscripts/stop-core.sh" # bctrackerをCore環境に寄せている一方でDBがBPM環境にあり依存関係が一部逆転しているため、システムの外側から停止する原則を無視する
ENV=bpmbiz "${BASE}/subscripts/stop-bpm.sh"

# FZ
ENV=fin "${BASE}/subscripts/stop-core.sh" # bctrackerをCore環境に寄せている一方でDBがBPM環境にあり依存関係が一部逆転しているため、システムの外側から停止する原則を無視する
ENV=bpmfin "${BASE}/subscripts/stop-bpm.sh"

# IBC
ENV=ibc "${BASE}/subscripts/stop-ibc.sh"
