#!/bin/bash

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

# Check Besu Start Status
function checkBesuStatus() {
  http_codes=$1

  all_status=true
  for http_code in "${http_codes[@]}"; do
    if [ "$http_code" != "200" ]; then
      all_status=false
      echo ${all_status}
      break
    else
      echo ${all_status}
    fi
  done
}

# copy function of abi.json.
function copyAbiJson() {
  network=$1
  zone_id=$2

  cp -pr ./deployments/"${network}"/*.json ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${zone_id}"
  rm ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${zone_id}"/*Lib*
  rm ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${zone_id}"/*_*
}

# set bridge contract
function setIBCApps() {
  network=$1

  pushd "${MAIN_CONTRACT_REPO}" >/dev/null

  output=$(npx hardhat deployConfirmation_ibc --network "${network}")
  ACCOUNT_SYNC_BRIDGE=$(echo "${output}" | awk '$1=="AccountSyncBridge"{print $2}')
  BALANCEC_SYNC_BRIDGE=$(echo "${output}" | awk '$1=="BalanceSyncBridge"{print $2}')
  TOKEN_TRANSFER_BRIDGE=$(echo "${output}" | awk '$1=="JPYTokenTransferBridge"{print $2}')

  npx hardhat setIBCApp --network "${network}" \
    --ibcapp-name AccountSyncBridge \
    --ibcapp "${ACCOUNT_SYNC_BRIDGE}"

  npx hardhat setIBCApp --network "${network}" \
    --ibcapp-name BalanceSyncBridge \
    --ibcapp "${BALANCEC_SYNC_BRIDGE}"

  npx hardhat setIBCApp --network "${network}" \
    --ibcapp-name JPYTokenTransferBridge \
    --ibcapp "${TOKEN_TRANSFER_BRIDGE}"

  popd >/dev/null
}

# channel-idの組み合わせを生成する関数
function generate_channel_combinations() {
  local network=$1
  local zone_id=$2
  local base_channel=$3
  local combinations=()
  local channel_types=("accountSync" "balanceSync" "tokenTransfer")
  
  for i in "${!channel_types[@]}"; do
    local channel_id="channel-$((base_channel + i))"
    combinations+=("${network},${zone_id},${channel_types[$i]},${channel_id}")
  done
  
  echo "${combinations[@]}"
}

# channel-idの組み合わせごとにchannel情報を設定する関数
function set_channels() {
  local combinations=("$@")

  pushd "${MAIN_CONTRACT_REPO}" >/dev/null

  for combo in "${combinations[@]}"; do
    IFS=',' read -r NETWORK ZONE_ID BRIDGE CHANNEL_ID <<< "$combo"

    npx hardhat setChannel --network "${NETWORK}" \
      --zone-id "${ZONE_ID}" \
      --bridge "${BRIDGE}" \
      --channel-id "${CHANNEL_ID}"
  done

  popd >/dev/null
}

start_localstack_service() {
  local env=$1
  local env_file=$2
  local project_name=$3

  docker compose -f docker-compose-local.yml --profile "${env}" --env-file "${env_file}" -p "${project_name}" up -d core-localstack

  LOCALSTACK_PORT="4566"
  if [[ "${env}" == "fin" ]]; then
  LOCALSTACK_PORT="14566"
  fi
  if [[ "${env}" == "biz" ]]; then
    LOCALSTACK_PORT="24566"
  fi

  while true; do
    echo "waiting for the localstack for ${env} up and running"
    status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:${LOCALSTACK_PORT}/health)
    if [[ "${status}" == "200" ]]; then
      break
    fi
    sleep 5
  done
}

source "${BASE}/../../bin/common.sh"

REPO_PATH="${REPO_PATH:-$BASE/../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

cat "${BASE}/repos/dlt-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    message "err" "does not exists. ${REPO_PATH}/${REPO}" 1>&2
    exit 1
  fi
done

# Dockerの稼働状況をチェック
if ! docker info >/dev/null 2>&1; then
  message "err" "Docker is not running. Please start Docker and try again." 1>&2
  exit 1
fi

choice "this operation will initialize the data in besu-template-multiple-zone-static-docker-local data. Do you continue?If you want to generate multiple Biz zones, please specify the number of Biz zones with the parameter. example:'initialize-dlt.sh 2'"

BIZ_ZONE_COUNT=1
FIN_BESU_PORT=18451
BIZ_BESU_PORT=28451
FIN_ZONE_ID=3000

# -biz N でマルチテナント指定
PARAM_NAME=$1
if [[ $PARAM_NAME = "-biz" ]]; then
  if [[ $2 =~ ^[0-9]+$ ]]; then
    BIZ_ZONE_COUNT=$2
    echo "BIZ_ZONE_COUNT: ${BIZ_ZONE_COUNT}"
  else
    message "err" "Please set a number for the number of Biz zones." 1>&2
    exit 1
  fi
fi

# repositories
BESU_REPO="${REPO_PATH}/dcbg-dcf-besu-template/besu-template-multiple-zone-static-docker-local"
MAIN_CONTRACT_REPO="${REPO_PATH}/dcbg-dcjpy-contract"
RELAYER_REPO="${REPO_PATH}/dcbg-dcjpy-relayer"
LOCAL_ENV_REPO="${REPO_PATH}/dcbg-dcjpy-core-local-environment"

# initialize besu (2 zone 1 node)
pushd "${BESU_REPO}" >/dev/null

rm -rf ${BESU_REPO}/zone*
rm -rf ${BESU_REPO}/docker-compose.yml

./scripts/generate_multi.sh 2 1
docker compose up -d

popd >/dev/null

while true; do
  message "info" "waiting for the besu network up and running"
  fin_status=$(curl -s -o /dev/null -w "%{http_code}" localhost:${FIN_BESU_PORT}/liveness)
  biz_status=$(curl -s -o /dev/null -w "%{http_code}" localhost:${BIZ_BESU_PORT}/liveness)
  if [[ "${fin_status}" == "200" && "${biz_status}" == "200" ]]; then
    break
  fi
  sleep 5
done

# Delete Relayer local config
pushd "${RELAYER_REPO}" >/dev/null

rm -rf .local/*
rm -rf docker/dcjpy/env/handshake-*.env
rm -rf docker/dcjpy/env/common-*.env
rm -rf docker/dcjpy/env/ibc-address-*.env

popd >/dev/null

# start localStack
pushd "${LOCAL_ENV_REPO}" >/dev/null

start_localstack_service "fin" "./docker/local/env/fin.env" "fin"
start_localstack_service "biz" "./docker/local/env/biz.env" "biz"

popd >/dev/null

# main contract migration
pushd "${MAIN_CONTRACT_REPO}" >/dev/null

rm -fr ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/* >/dev/null
mkdir -p ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${FIN_ZONE_ID}"

while (( j++ < "${BIZ_ZONE_COUNT}" )); do
  BIZ_ZONE_ID=$((FIN_ZONE_ID+j))
  mkdir -p ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${BIZ_ZONE_ID}"
done

npm i
export NETWORK=localFin
export MODE_PROD=1
export KMS_ENDPOINT_URL=http://localhost:14566

rm -rf ../dcbg-dcjpy-contract/.kms
./bin/local/_generate_kms_key.sh localFin
./bin/local/_generate_kms_key.sh localBiz
./bin/local/migrate_main.sh localFin

export FIN_TOKEN_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="Token"{print $2}')
export FIN_IBC_TOKEN_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="IBCToken"{print $2}')
export FIN_VALIDATOR_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="Validator"{print $2}')
export FIN_ACCOUNT_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="Account"{print $2}')
export FIN_ACCESS_CTRL_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="AccessCtrl"{print $2}')
export FIN_BUSINESS_ZONE_ACCOUNT_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="BusinessZoneAccount"{print $2}')
export FIN_PROVIDER_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localFin | awk '$1=="Provider"{print $2}')

# Fin ibc contract migration
./bin/local/1_migrate_ibc.sh localFin

copyAbiJson localFin "${FIN_ZONE_ID}"

setIBCApps localFin

# シングルテナントの場合のcombinationsを生成
fin_combinations=($(generate_channel_combinations "localFin" "3001" "0"))
if [ "${BIZ_ZONE_COUNT}" -ge 2 ]; then
  #　マルチテナントの場合はさらにcombinationsを生成して追加
  fin_combinations+=($(generate_channel_combinations "localFin" "3002" "6"))
fi

set_channels "${fin_combinations[@]}"

output=$(npx hardhat deployConfirmation_ibc --network localFin)
FIN_IBC_HANDLER_ADDRESS=$(echo "${output}" | awk '$1=="OwnableIBCHandler"{print $2}')

# Biz Zone
if [ "${BIZ_ZONE_COUNT}" -ge 1 ]; then

  export NETWORK=localBiz
  export KMS_ENDPOINT_URL=http://localhost:24566

  while (( k++ < "${BIZ_ZONE_COUNT}" )); do
    BIZ_ZONE_ID=$((FIN_ZONE_ID+k))

    message "info" "BIZ_ZONE_ID: ${BIZ_ZONE_ID}."


    # Biz main contract migration
    ./bin/local/migrate_main.sh localBiz

    export BIZ_TOKEN_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="Token"{print $2}')
    export BIZ_IBC_TOKEN_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="IBCToken"{print $2}')
    export BIZ_VALIDATOR_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="Validator"{print $2}')
    export BIZ_ACCOUNT_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="Account"{print $2}')
    export BIZ_ACCESS_CTRL_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="AccessCtrl"{print $2}')
    export BIZ_BUSINESS_ZONE_ACCOUNT_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="BusinessZoneAccount"{print $2}')
    export BIZ_PROVIDER_ADDRESS=$(npx hardhat getIbcDeploymentParam --network localBiz | awk '$1=="Provider"{print $2}')

    # Biz ibc contract migration
    ./bin/local/1_migrate_ibc.sh localBiz

    copyAbiJson localBiz "${BIZ_ZONE_ID}"

    # BizZone から FinDLT に参照する abi.json のコピー
    cp -f ./deployments/localFin/FinancialCheck.json ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${BIZ_ZONE_ID}"/
    cp -f ./deployments/localFin/Issuer.json ../dcbg-dcjpy-bcclient/docker/minio/export/abijson/"${BIZ_ZONE_ID}"/

    setIBCApps localBiz

    base_channel=$(( (k-1) * 3 ))    
    # チャネルの組み合わせを生成
    biz_combinations=($(generate_channel_combinations "localBiz" "3000" "${base_channel}"))
    
    # 生成された組み合わせに対してチャネルを設定
    set_channels "${biz_combinations[@]}"

    output=$(npx hardhat deployConfirmation_ibc --network localBiz)
    BIZ_IBC_HANDLER_ADDRESS=$(echo "${output}" | awk '$1=="OwnableIBCHandler"{print $2}')

    message "info" "[Fin][${FIN_ZONE_ID}] OwnableIBCHandler Address: ${FIN_IBC_HANDLER_ADDRESS}."
    message "info" "[Biz][${BIZ_ZONE_ID}] OwnableIBCHandler Address: ${BIZ_IBC_HANDLER_ADDRESS}."

    # Relayer Config Update
    pushd "${RELAYER_REPO}" >/dev/null

    # handshake-${N}.env Update
    sed -e "s|_BIZ_BESU_PORT|$BIZ_BESU_PORT|g" \
        ./docker/dcjpy/env/tpl/handshake.tpl > ./docker/dcjpy/env/handshake-${k}.env

    # common-${N}.env Update
    CHAIN_1_CLIENT_ID=""
    CHAIN_1_CONNECTION_ID=""

    CHAIN_1_AS_CHANNEL_ID=""
    CHAIN_1_BS_CHANNEL_ID=""
    CHAIN_1_TT_CHANNEL_ID=""

    CHAIN_2_AS_CHANNEL_ID=""
    CHAIN_2_BS_CHANNEL_ID=""
    CHAIN_2_TT_CHANNEL_ID=""

    sed -e "s|_CHAIN_1_CLIENT_ID|$CHAIN_1_CLIENT_ID|g" \
        -e "s|_CHAIN_1_CONNECTION_ID|$CHAIN_1_CONNECTION_ID|g" \
        -e "s|_CHAIN_1_AS_CHANNEL_ID|${CHAIN_1_AS_CHANNEL_ID}|g" \
        -e "s|_CHAIN_2_AS_CHANNEL_ID|${CHAIN_2_AS_CHANNEL_ID}|g" \
        -e "s|_CHAIN_1_BS_CHANNEL_ID|${CHAIN_1_BS_CHANNEL_ID}|g" \
        -e "s|_CHAIN_2_BS_CHANNEL_ID|${CHAIN_2_BS_CHANNEL_ID}|g" \
        -e "s|_CHAIN_1_TT_CHANNEL_ID|${CHAIN_1_TT_CHANNEL_ID}|g" \
        -e "s|_CHAIN_2_TT_CHANNEL_ID|${CHAIN_2_TT_CHANNEL_ID}|g" \
        -e "s|_BIZ_BESU_PORT|$BIZ_BESU_PORT|g" \
        ./docker/dcjpy/env/tpl/common.tpl > ./docker/dcjpy/env/common-${k}.env

    sed -e "s|_FIN_IBC_HANDLER_ADDRESS|$FIN_IBC_HANDLER_ADDRESS|g" \
        -e "s|_BIZ_IBC_HANDLER_ADDRESS|$BIZ_IBC_HANDLER_ADDRESS|g" \
        ./docker/dcjpy/env/tpl/ibc-address.tpl > ./docker/dcjpy/env/ibc-address-${k}.env

    if [ $k -eq 1 ]; then
      IS_MULTI_TENANT=false
      echo "services:" > ./docker-compose-local.yml
    else
      IS_MULTI_TENANT=true
    fi

    sed -e "s|_IS_MULTI_TENANT|$IS_MULTI_TENANT|g" \
        -e "s|_RLY_CONTAINER_SEQ|$k|g" \
        ./docker/dcjpy/tpl/handshake-container.tpl >> ./docker-compose-local.yml

    if [ $k -eq 1 ]; then
      cat ./docker/dcjpy/tpl/healthcheck.tpl >> ./docker-compose-local.yml
    else
      cat ./docker/dcjpy/tpl/depends-on.tpl >> ./docker-compose-local.yml
    fi

    sed -e "s|_RLY_CONTAINER_SEQ|$k|g" \
      ./docker/dcjpy/tpl/relay-container.tpl >> ./docker-compose-local.yml

    popd >/dev/null
  done

# Financial Zone Only
else
  message "info" "Financial Zone Only." 1>&2
fi

popd >/dev/null

## Relayer Handshake
pushd "${RELAYER_REPO}" >/dev/null

# Delete .local config
rm -rf .local/*

relayer_handshake_containers=()
relayer_relay_containers=(
  relayer-account-sync
  relayer-balance-sync
  relayer-token-transfer
)

for i in $(seq "${BIZ_ZONE_COUNT}")
do
  relayer_handshake_container="relayer-handshake-$i"

  # Create Config Dir
  mkdir -p .local/"${relayer_handshake_container}"/config/
  for relayer_relay_container in "${relayer_relay_containers[@]}" ; do
    mkdir -p .local/"${relayer_relay_container}-${i}"/config/
  done

  # Copy Default Config
  cp -p .relayer/config/config.json .local/"${relayer_handshake_container}"/config/

  relayer_handshake_containers+=("${relayer_handshake_container}")
done

message "info" "relayer_handshake_containers: ${relayer_handshake_containers[*]}" 1>&2

# Relayer Handshake
ENV=ibc docker compose -f docker-compose-local.yml --profile "${ENV}" -p "${ENV}" up ${relayer_handshake_containers[*]}

# Copy Handshaked Config
for i in $(seq "${BIZ_ZONE_COUNT}")
do
  relayer_handshake_container="relayer-handshake-$i"
  for relayer_relay_container in "${relayer_relay_containers[@]}" ; do
    cp -p .local/"${relayer_handshake_container}"/config/config.json .local/"${relayer_relay_container}-${i}"/config/
  done
done

popd >/dev/null

# shutdown BESU
pushd "${BESU_REPO}" >/dev/null

docker compose down

popd >/dev/null

message "info" "Initialized DLT."