#!/bin/bash -e

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

OPTIONS="$@"

REPO_PATH="${REPO_PATH:-$BASE/../../../..}" # REPO_PATHが設定されていない場合、'devops-tools'と同じ階層をデフォルトとする

cat "${BASE}/repos/ibc-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done
cat "${BASE}/repos/core-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done
cat "${BASE}/repos/bpm-repositories.txt" | while IFS= read -r REPO || [[ -n "${REPO}" ]]; do
  if [[ ! -e "${REPO_PATH}/${REPO}" ]]; then
    echo "does not exists. ${REPO_PATH}/${REPO}"
    exit 1
  fi
done

# IBC
ENV=ibc "${BASE}/subscripts/start-ibc.sh" ${OPTIONS}

# FZ
# bctrackerをCore環境に寄せている一方でDBがBPM環境にあり依存関係が一部逆転しているため、システムの内側から起動する原則を無視する
ENV=bpmfin "${BASE}/subscripts/start-bpm.sh" ${OPTIONS}
ENV=fin "${BASE}/subscripts/start-core.sh" ${OPTIONS}
