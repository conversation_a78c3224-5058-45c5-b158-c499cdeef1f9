////
// 最終更新: Tue Mar 11 08:46:31 JST 2025
// GASプロジェクト(AWS環境情報一覧): https://docs.google.com/spreadsheets/d/1-VJDY4uMZUDjSBSmxdYdk1wGAVl08bI6bB7324oq_yk
// 最新デプロイバージョン: https://script.google.com/macros/s/AKfycbziEGKecsrw7YABXqwa4I3cu8XaBjoD-MhBPUDhYm04zlqXo_g1itdq5zIUP5jsVWPb/exec
// 実行ユーザー： <EMAIL>(GithubActionsから認証なしでリクエストできるようにするため)
////

// GET リクエストに対する処理
function doGet(e) {
    return message("Error: no parameters");
}

// POST リクエストに対する処理
function doPost(e) {

    if (!e.parameters.filename || !e.parameters.file) {
        return message("Error: Bad parameters");
    } else {

        // Script Properties取得
        var scriptProperties = PropertiesService.getScriptProperties();
        const dataFolderId = scriptProperties.getProperty("DATA_FOLDER_ID");
        const docFolderId = scriptProperties.getProperty("DOC_FOLDER_ID");

        // 過去 CSV ファイル削除
        if (isExistsFile(dataFolderId, e.parameters.filename)) {
            deleteFileByName(dataFolderId, e.parameters.filename)
        }

        const awsEnvName = getAwsEnvName(String(e.parameters.filename));
        const docName = getDocName(awsEnvName)

        const csvFileId = createCsvFile(e.parameters.file, e.parameters.filename);
        const csvData = readCsvFile(csvFileId);

        let docId = "";

        // 更新
        if (isExistsFile(docFolderId, docName)) {

            const targetFile = DriveApp.getFolderById(docFolderId).getFilesByName(docName);
            docId = targetFile.next().getId();

            if (isExistsSheet(awsEnvName, docId)) {
                // CSVデータで上書き
                updateSpreadsheetData(awsEnvName, docId, csvData);
            } else {
                // テンプレートからシートコピー
                copySheet(awsEnvName, docId);

                // SpreadSheet へ CSV データ書き込み
                writeSpreadsheetData(awsEnvName, docId, csvData);
            }
        // 新規
        } else {
            // 空の Spreadsheet 作成
            docId = createSpreadsheetInFolder(docFolderId, docName);

            // テンプレートからシートコピー
            copySheet(awsEnvName, docId);

            // SpreadSheet へ CSV データ書き込み
            writeSpreadsheetData(awsEnvName, docId, csvData);
        }
        // シートソート
        sortSheetByName(docId);

        // 書式・罫線セット
        // @ref https://841-biboroku.info/entry/gas-auto-grouping-borders/
        setCellFormat(docId, awsEnvName);

        return message("success!");
    }
}

function message(msg) {
    return ContentService.createTextOutput(JSON.stringify({result: msg})).setMimeType(ContentService.MimeType.TEXT);
}

// CSVファイル作成
function createCsvFile(file, fileName) {
    var scriptProperties = PropertiesService.getScriptProperties();
    const dataFolderId = scriptProperties.getProperty("DATA_FOLDER_ID");
    const data = Utilities.base64Decode(file, Utilities.Charset.UTF_8);
    const blob = Utilities.newBlob(data, MimeType.CSV, fileName);
    const csvFile = DriveApp.getFolderById(dataFolderId).createFile(blob);

    return csvFile.getId();
}

// CSVファイル読み込み
function readCsvFile(csvFileId) {
    const blob = DriveApp.getFileById(csvFileId).getBlob();
    const csv = blob.getDataAsString();
    const csvData = Utilities.parseCsv(csv);

    return csvData;
}

// ファイル存在チェック
function isExistsFile(dataFolderId, fileName) {
    const targetFile = DriveApp.getFolderById(dataFolderId).getFilesByName(fileName);
    if (targetFile.hasNext()) {
        return true
    }
    return false
}

// ファイル削除
function deleteFileByName(folderId, fileName) {
    const fileData = DriveApp.getFolderById(folderId).getFilesByName(fileName);
    if (fileData.hasNext()) {
        fileData.next().setTrashed(true);
    }
}

// AWS環境名取得
function getAwsEnvName(fileName) {

    let awsEnvName = "";
    if (! fileName.includes("devops")) {
      awsEnvName = fileName.slice(0, -4);
    } else {
      awsEnvName = "docs";
    }

    // シート名を現状維持" (region)"の場合
    const tempArray = awsEnvName.split("-");
    const region = tempArray.pop(); // 最後の要素を取り出す
    awsEnvName = `${tempArray.join("-")} (${region})`; // 新しいフォーマットで結合

    console.log("awsEnvName: %s", awsEnvName);

    return awsEnvName;
}

// AWS環境一覧 Spreadsheet 名取得
function getDocName(fileName) {
    let docName = "";
    if (fileName.includes('dev-')) {
        docName = "AWS環境情報一覧(Dev)";
    } else if ((fileName.includes('sandbox-'))) {
        docName = "AWS環境情報一覧(Sandbox)";
    } else if ((fileName.includes('stage-'))) {
        docName = "AWS環境情報一覧(Staging)";
    } else if ((fileName.includes('prod-'))) {
        docName = "AWS環境情報一覧(Production)";
    } else {
        docName = "AWS環境情報一覧(Docs API仕様書)";
    }
    return docName;
}

// テンプレート sheet 名取得
function getTemplateSheetName(fileName) {
    let sheetName = "";
    if (fileName.includes('bpmfin')) {
        sheetName = "【テンプレート】xxx-bpmfin";
    } else if ((fileName.includes('bpmbiz'))) {
        sheetName = "【テンプレート】xxx-bpmbiz";
    } else if ((fileName.includes('fin'))) {
        sheetName = "【テンプレート】xxx-fin";
    } else if ((fileName.includes('biz'))) {
        sheetName = "【テンプレート】xxx-biz";
    } else if ((fileName.includes('ibc'))) {
        sheetName = "【テンプレート】xxx-ibc";
    } else {
        sheetName = "【テンプレート】xxx-docs";
    }
    return sheetName;
}

// Spreadsheet 作成
function createSpreadsheetInFolder(folderId, docName) {

    const createdSpreadsheet = SpreadsheetApp.create(docName);
    const file = DriveApp.getFileById(createdSpreadsheet.getId());
    const dstFolder = DriveApp.getFolderById(folderId);

    // @ref https://developers.google.com/sheets/api/guides/create?hl=ja#work_with_folders
    // 指定したドライブフォルダ内に直接スプレッドシートを作成するオプションはないため マイドライブ -> 指定したドライブフォルダ
    file.moveTo(dstFolder);

    return createdSpreadsheet.getId();
}

// シートコピー（テンプレート -> 指定Spreadsheet）
function copySheet(awsEnvName, docId) {
    var scriptProperties = PropertiesService.getScriptProperties();
    const templateDocId = scriptProperties.getProperty("TEMPLATE_DOC_ID");

    // コピー元
    const srcSpreadsheet = SpreadsheetApp.openById(templateDocId);
    const srcSheet = srcSpreadsheet.getSheetByName(getTemplateSheetName(awsEnvName));

    // コピー先
    const dstSheet = SpreadsheetApp.openById(docId);

    // シートコピー
    const newCopySheet = srcSheet.copyTo(dstSheet);

    // シート名リネーム
    newCopySheet.setName(awsEnvName);

    // デフォルトで作成されるシート削除
    if (isExistsSheet("シート1", docId)) {
        // "シート1" 削除
        let targetSheet = dstSheet.getSheetByName("シート1").activate();
        dstSheet.deleteSheet(targetSheet);
    }
}

// シート存在チェック
function isExistsSheet(sheetName, docId) {
    const targetSpreadsheet = SpreadsheetApp.openById(docId);
    const targetSheet = targetSpreadsheet.getSheetByName(sheetName);

    if (targetSheet) {
        return true
    }
    return false
}

// シートソート
function sortSheetByName(docId) {
    const envOrderRule = {
        fin: 10,
        biz: 20,
        ibc: 30,
        bpmfin: 40,
        bpmbiz: 50,
        docs: 60,
    };
    const regionOrderRule = {
        tokyo: 1,
        osaka: 2,
    };    
    const targetSpreadsheet = SpreadsheetApp.openById(docId);
    const sheetNames = targetSpreadsheet.getSheets();
    let sheetJson = new Array();

    for (let i = 0; i < sheetNames.length; i++) {
        let sheetJsonObj = new Object();
        let env = sheetNames[i].getName().split('-')[0];
        let layer = sheetNames[i].getName().split('-')[1];
        let region = '';

        if (env == 'prod' || env == 'stage') {
            if (layer != 'ibc') {
                region = sheetNames[i].getName().split('-')[3];
            } else {
                region = sheetNames[i].getName().split('-')[2];
            }
        } else {
            region = sheetNames[i].getName().split('-')[2];
        }
        sheetJsonObj.sheetName = sheetNames[i].getName();
        sheetJsonObj.env = env;
        sheetJsonObj.layer = layer;
        sheetJsonObj.region = region;
        sheetJsonObj.order = envOrderRule[layer] + regionOrderRule[region];        
        sheetJson.push(sheetJsonObj);
    }

    let sortedSheetsArray = sheetJson.reduce((acc, curr) => {
        let ind = acc.findIndex((item) => item.order > curr.order);
        if (ind === -1) ind = acc.length;
        acc.splice(ind, 0, curr);
        return acc;
    }, []);

    for(let j=0; j < sortedSheetsArray.length; j++){
      let sheetName = String(sortedSheetsArray[j].sheetName);
      targetSpreadsheet.getSheetByName(sheetName).activate();
      targetSpreadsheet.moveActiveSheet(j+1);
    }
    return sortedSheetsArray;
}

// SpreadSheet へ CSV データ書き込み
function writeSpreadsheetData(awsEnvName, docId, csvData) {

    const spreadsheet = SpreadsheetApp.openById(docId);
    const sheet = spreadsheet.getSheetByName(awsEnvName).activate();
    const startRow = getStartRow(awsEnvName); 
    const sheetData = sheet.getRange(startRow, 1, csvData.length, 4)

    sheetData.setValues(csvData);
}

// SpreadSheet データ更新
function updateSpreadsheetData(awsEnvName, docId, csvData) {

    const targetSpreadsheet = SpreadsheetApp.openById(docId);
    const targetSheet = targetSpreadsheet.getSheetByName(awsEnvName).activate();
    let maxRow = targetSheet.getMaxRows();
    let sheetRange = targetSheet.getRange(11, 1, maxRow, 4)
    let sheetResourcesData = sheetRange.getValues();

    // シートデータと CSV の差分がある場合、更新
    if(JSON.stringify(sheetResourcesData) !== JSON.stringify(csvData)) {

      // 差分データ抽出
      // マージ (CSV -> シート)
      const updateData = sheetResourcesData.map((row, i) => {
        if (csvData[i] != row) return row; // csvData に該当する行がない場合はそのまま
        return row.map((val, j) => csvData[i][j] !== undefined ? csvData[i][j] : val);
      });

      sheetRange.clear();
      sheetRange.setValues(updateData);

      maxRow = targetSheet.getMaxRows();
      lastRow = targetSheet.getRange(maxRow, 1).getNextDataCell(SpreadsheetApp.Direction.UP).getRowIndex();
      let deleteRowCount = maxRow - lastRow;
      targetSheet.deleteRows(lastRow +1, deleteRowCount);
    }
}

// セル書式設定
function setCellFormat(docId, sheetName) { 

  const targetSpreadsheet = SpreadsheetApp.openById(docId);
  const targetSheet = targetSpreadsheet.getSheetByName(sheetName).activate();
  const startRow = getStartRow(sheetName);

  let maxRow = targetSheet.getMaxRows();
  let lastRow = targetSheet.getRange(maxRow, 1).getNextDataCell(SpreadsheetApp.Direction.UP).getRowIndex();
  const lastColumn = targetSheet.getLastColumn();

  targetSheet.getRange(startRow, 1, maxRow, lastColumn).setBorder(true, true, true, true, true, false);
  targetSheet.getRange(startRow, 4, maxRow, lastColumn - 4 + 1).setBorder(null, null, null, null, null, true);

  const resourcesData = targetSheet.getDataRange().getValues();

  for (var row = startRow; row < maxRow; row++) {
    for (var column = 1; column <= 4; column++) {
      if (
        (resourcesData[row][column - 1] !== '') ||
        (column !== 1 && resourcesData[row][column - 1] === '' && resourcesData[row][column - 2] !== '') ||
        (column !== 1 && resourcesData[row - 1][column - 1] === '' && resourcesData[row - 1][column - 2] !== '')
      ) {
        targetSheet.getRange(row, column, 1, 1).setBorder(null, null, true, null, null, null);
      }
    }
  }

  if (sheetName != 'docs') {
    let finder = targetSheet.createTextFinder('AWSリソース自動起動設定情報一覧');
    let scheduleHeaderRowNo = finder.findNext().getRow();
    let scheduleHeader = targetSheet.getRange(scheduleHeaderRowNo, 1, 1, 4);
    let scheduleSubHeader = targetSheet.getRange(scheduleHeaderRowNo +1, 1, 1, 4);
    let blankRow = targetSheet.getRange(scheduleHeaderRowNo -1, 1, 1, 4);
    scheduleHeader.setBorder(false, false, true, false, false, false);
    scheduleHeader.setFontSize(14);
    scheduleHeader.setFontWeight('bold');
    scheduleSubHeader.setFontWeight('bold');
    scheduleSubHeader.setBackground('#f4f5f7');
    blankRow.setBorder(true, false, false, false, false, false);
  } else {
    let resourcesHeaderFinder = targetSheet.createTextFinder('AWS リソース情報一覧');
    let resourcesHeaderRowNo = resourcesHeaderFinder.findNext().getRow();
    let resourcesHeader = targetSheet.getRange(resourcesHeaderRowNo, 1, 1, 4);
    let resourcesHeaderBlankRow = targetSheet.getRange(resourcesHeaderRowNo -1, 1, 1, 4);
    let resourcesSubHeader = targetSheet.getRange(resourcesHeaderRowNo +1, 1, 1, 4);

    let basicAuthHeaderFinder = targetSheet.createTextFinder('Basic認証');
    let basicAuthHeaderRowNo = basicAuthHeaderFinder.findNext().getRow();
    let basicAuthHeader = targetSheet.getRange(basicAuthHeaderRowNo, 1, 1, 4);
    let basicAuthBlankRow = targetSheet.getRange(basicAuthHeaderRowNo -1, 1, 1, 4);
    let basicAuthSubHeader = targetSheet.getRange(basicAuthHeaderRowNo +1, 1, 1, 4);

    resourcesHeader.setBorder(false, false, true, false, false, false);
    resourcesHeader.setFontSize(14);
    resourcesHeader.setFontWeight('bold');
    resourcesHeaderBlankRow.setBorder(true, false, false, false, false, false);
    resourcesSubHeader.setFontWeight('bold');
    resourcesSubHeader.setBackground('#f4f5f7');

    basicAuthHeader.setBorder(false, false, true, false, false, false);
    basicAuthHeader.setFontSize(14);
    basicAuthHeader.setFontWeight('bold');
    basicAuthBlankRow.setBorder(true, false, false, false, false, false);
    basicAuthSubHeader.setFontWeight('bold');
    basicAuthSubHeader.setBackground('#f4f5f7');
  }
  targetSheet.getRange(startRow, 1, maxRow, lastColumn).setWrap(true);
  targetSheet.getRange(startRow, 1, maxRow, lastColumn).setVerticalAlignment('top');

  maxRow = targetSheet.getMaxRows();
  lastRow = targetSheet.getRange(maxRow, 1).getNextDataCell(SpreadsheetApp.Direction.UP).getRowIndex();
  let deleteRowCount = maxRow - lastRow;
  targetSheet.deleteRows(lastRow +1, deleteRowCount);
}

// データ書き込み開始行
function getStartRow(awsEnvName) {
  return awsEnvName != "docs" ? 11: 6;
}
