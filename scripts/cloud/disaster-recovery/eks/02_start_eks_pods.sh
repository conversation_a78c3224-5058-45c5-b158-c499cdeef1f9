#!/bin/bash

source "${0%/*}"/../common_function.sh

account_id=$(aws sts get-caller-identity --query 'Account' --output text)
read -p "Which region? (ap-northeast-1, ap-northeast-3): " region

echo "AWS ACCOUNT: ${account_id}"
echo "REGION: ${region}"

CONFIRMATION

codebuild_project=$(aws codebuild list-projects \
  --region "${region}" \
  --query 'projects[?ends_with(@,`-eks-pod-start`)]' \
  --output text)

aws codebuild start-build \
  --region "${region}" \
  --no-cli-pager \
  --project-name ${codebuild_project}

echo "The codebuild project is starting EKS Pods."
