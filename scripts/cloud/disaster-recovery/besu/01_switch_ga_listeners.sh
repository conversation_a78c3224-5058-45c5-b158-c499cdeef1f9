#!/bin/bash

source "${0%/*}"/../common_function.sh

function check_current_settings() {
  echo -e "\n[current settings]"

  ga_list=$(aws globalaccelerator list-accelerators \
    --region us-west-2 \
    --query 'Accelerators[?contains(Name, `besu-validator-ga`)].AcceleratorArn' \
    --output text)

  for ga_arn in ${ga_list}; do
    listeners=$(aws globalaccelerator list-listeners \
      --region us-west-2 \
      --accelerator-arn "${ga_arn}" \
      --query 'Listeners[].ListenerArn' \
      --output text)
    for listner_arn in ${listeners}; do
      groups=$(aws globalaccelerator list-endpoint-groups \
        --region us-west-2 \
        --listener-arn "${listner_arn}" \
        --output json | jq -r '.EndpointGroups')

      len=$(echo $groups | jq length)
      for i in $(seq 0 $(($len - 1))); do
        row=$(echo $groups | jq .[$i])
        endpoint_group_region=$(echo $row | jq -r ".EndpointGroupRegion")
        traffic_dial_percentage=$(echo $row | jq -r '.TrafficDialPercentage')
        echo "${listner_arn},${endpoint_group_region},${traffic_dial_percentage}"
      done
    done
  done

  echo ""
}

# 設定確認
check_current_settings

account_id=$(aws sts get-caller-identity --query 'Account' --output text)
read -p 'Which region? (ap-northeast-1, ap-northeast-3): ' region

echo "AWS ACCOUNT: ${account_id}"
echo "TO REGION: ${region}"

CONFIRMATION

echo "This operation will stop the block creation of besu. Check the account again."
echo "AWS ACCOUNT: ${account_id}"
echo "TO REGION: ${region}"

CONFIRMATION # サービス停止の恐れがあるので2回確認する

ga_list=$(aws globalaccelerator list-accelerators \
  --region us-west-2 \
  --query 'Accelerators[?contains(Name, `besu-validator-ga`)].AcceleratorArn' \
  --output text)

for ga_arn in ${ga_list}; do
  listeners=$(aws globalaccelerator list-listeners \
    --region us-west-2 \
    --accelerator-arn "${ga_arn}" \
    --query 'Listeners[].ListenerArn' \
    --output text)
  for listner_arn in ${listeners}; do
    groups=$(aws globalaccelerator list-endpoint-groups \
      --region us-west-2 \
      --listener-arn "${listner_arn}" \
      --output json | jq -r '.EndpointGroups')

    len=$(echo $groups | jq length)
    for i in $(seq 0 $(($len - 1))); do
      row=$(echo $groups | jq .[$i])
      endpoint_group_region=$(echo $row | jq -r ".EndpointGroupRegion")
      endpoint_group_arn=$(echo $row | jq -r '.EndpointGroupArn')

      traffic_dial_percentage=0
      if [[ ${endpoint_group_region} == ${region} ]]; then
        # 切り替え先のリージョンのみを100%でトラフィックを転送する
        traffic_dial_percentage=100
      fi

      aws globalaccelerator update-endpoint-group \
        --region us-west-2 \
        --no-cli-pager \
        --endpoint-group-arn "${endpoint_group_arn}" \
        --traffic-dial-percentage "${traffic_dial_percentage}"
    done
  done
done

# 設定確認
check_current_settings

echo "Switching GA listeners finished."
