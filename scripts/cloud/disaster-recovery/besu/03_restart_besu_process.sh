#!/bin/bash

source "${0%/*}"/../common_function.sh

account_id=$(aws sts get-caller-identity --query 'Account' --output text)
read -p 'Which region? (ap-northeast-1, ap-northeast-3): ' region

echo "AWS ACCOUNT: ${account_id}"
echo "REGION: ${region}"

CONFIRMATION

INSTANCES=$(aws ec2 describe-instances \
  --region "${region}" \
  --filters 'Name=tag:Kind,Values=besu-validator' 'Name=instance-state-name,Values=running' \
  --query 'Reservations[].Instances[].InstanceId' \
  --output text)

# BESUの開始
echo '## Starting BESU process....'

for INSTANCE in ${INSTANCES}; do
  INSTANCE_IDS+=",\"${INSTANCE}\""
done

INSTANCE_IDS="[${INSTANCE_IDS#,}]"
TARGETS="[{ \"Key\":\"InstanceIds\",\"Values\":${INSTANCE_IDS} }]"
COMMAND_START='systemctl restart validator'

RES=$(aws ssm send-command --document-name 'AWS-RunShellScript' \
  --region "${region}" \
  --targets "${TARGETS}" \
  --parameters "{\"workingDirectory\":[\"\"],\"executionTimeout\":[\"3600\"],\"commands\":[\"${COMMAND_START}\"]}" \
  --timeout-seconds 600 --max-concurrency '50' --max-errors '0')
COMMAND_ID=$(echo ${RES} | jq -r '.Command.CommandId')
echo 'Requested starting.'
echo ''

# 開始実施時の出力確認
echo 'Checking response....'
RES_INSTANCE_ID_LIST=($(
  aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
    --region "${region}" \
    --output text \
    --query 'CommandInvocations[].{InstanceId:InstanceId}'
))
RES_JSON_OUTPUT=$(
  aws ssm list-command-invocations --command-id ${COMMAND_ID} --details \
    --region "${region}" \
    --query 'CommandInvocations[].{Output:CommandPlugins[0].Output}'
)

FLAG_RES_START=0
for NUM in $(seq 0 $((${#RES_INSTANCE_ID_LIST[*]} - 1))); do
  OUTPUT=$(echo "${RES_JSON_OUTPUT}" | jq .["${NUM}"].Output)
  echo -n "${RES_INSTANCE_ID_LIST[${NUM}]} response: "
  if [[ "${OUTPUT}" == '""' ]]; then
    echo 'OK'
  else
    echo 'NG'
    FLAG_RES_START=1
  fi
done

if [[ "${FLAG_RES_START}" != "0" ]]; then
  echo 'ERROR: Response for starting BESU process is failed. Please check the status. Stop the script.'
  exit 1
fi

echo 'Besu process restarted.'
