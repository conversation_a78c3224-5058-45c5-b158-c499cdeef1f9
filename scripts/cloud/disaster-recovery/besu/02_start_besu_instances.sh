#!/bin/bash

source "${0%/*}"/../common_function.sh

account_id=$(aws sts get-caller-identity --query 'Account' --output text)
read -p "Which region? (ap-northeast-1, ap-northeast-3): " region

echo "AWS ACCOUNT: ${account_id}"
echo "REGION: ${region}"

CONFIRMATION

asg_name=$(aws autoscaling describe-auto-scaling-groups \
  --region "${region}" \
  --query 'AutoScalingGroups[?contains(AutoScalingGroupName, `besu-validator-asg`)].AutoScalingGroupName' \
  --output text)

aws autoscaling resume-processes \
  --region "${region}" \
  --auto-scaling-group-name ${asg_name}

echo "Auto Scaling processes enabled. Creating EC2 instances."
