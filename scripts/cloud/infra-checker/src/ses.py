from .utils.aws_client import AWSClient
from .utils.logger import setup_logger


BPM_EXPECTATION = True

class SES:
    def __init__(self):
        self.logger = setup_logger(name="SES")
        self.clientv2 = AWSClient(service_name="sesv2").client

    def check_ses_sandbox_account_status(self, account_name: str):
        self.logger.info("Starting to check ses sandbox mode...")
        """
        SESのサンドボックスモードを確認する
        
        Memo:
            SESのサンドボックスモード解除はクラウドBPM構築手順の一つ
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/AWS
            
            ProductionAccessEnabled:
                - trueの時: 本番モード
                - falseの時: sandboxモード

            BZ BPM環境の期待値: true
            それ以外の環境の期待値(デフォルト): false
        """

        try:
            # SES Sandbox Account Statusを取得
            response = self.clientv2.get_account()

            # bpmが適切に設定されているか確認
            production_access_status = response.get("ProductionAccessEnabled", None)
            match account_name:
                case _ if "bpmbiz" in account_name:
                    self.logger.info(f"Account is BPM in a business zone: {account_name}")
                    if BPM_EXPECTATION == production_access_status:
                        self.logger.info(f"SES sandbox status is OK: Expectation: {BPM_EXPECTATION}"
                                         f" CurrentStatus: {production_access_status}")
                        return True
                    else:
                        self.logger.error(f"SES sandbox status is NG: Expectation: {BPM_EXPECTATION}"
                                          f" CurrentStatus: {production_access_status}")
                        return False
                case _:
                    self.logger.info(f"ONLY BPM env in a business zone needs to check. Skipped.")
                    return True
        except Exception as e:
            # エラー発生時のロギング
            self.logger.error(f"Failed to check sns resources: {e}")
            return True
