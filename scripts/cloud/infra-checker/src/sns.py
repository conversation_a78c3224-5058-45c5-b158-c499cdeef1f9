from .utils.aws_client import AWSClient
from .utils.logger import setup_logger

BPM_EXPECTED_SANDBOX_MODE = False
FIN_EXPECTED_ENDPOINT = "bctracker_queue_push-notification"
BIZ_EXPECTED_ENDPOINT = "bctracker_queue_email-sender"



BPM_EXPECTATION=False

class SNS:
    def __init__(self):
        self.logger = setup_logger(name="SNS")
        self.client = AWSClient(service_name="sns").client

    def check_sms_sandbox_account_status(self, account_name: str):
        self.logger.info("Starting to check sms sandbox mode...")
        """
        SMSのサンドボックスモードを確認する
        
        Memo:
            SMSのサンドボックスモード解除はクラウドBPM構築手順の一つ
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/AWS+SMS
            
            BPM環境の期待値: false
            それ以外の環境の期待値: true
        """

        try:
            # SMS Sandbox Account Statusを取得
            response = self.client.get_sms_sandbox_account_status()

            # bpmが適切に設定されているか確認
            sandbox_mode_status = response.get("IsInSandbox")
            match account_name:
                case _ if "bpm" in account_name:
                    self.logger.info(f"Account is BPM: {account_name}")
                    if BPM_EXPECTED_SANDBOX_MODE == sandbox_mode_status:
                        self.logger.info(f"SMS sandbox status is OK: Expectation: {BPM_EXPECTED_SANDBOX_MODE}"
                                         f" CurrentStatus: {sandbox_mode_status}")
                        return True
                    else:
                        self.logger.error(f"SMS sandbox status is NG: Expectation: {BPM_EXPECTED_SANDBOX_MODE}"
                                          f" CurrentStatus: {sandbox_mode_status}")
                        return False
                case _:
                    self.logger.info(f"ONLY BPM env needs to check. Skipped.")
                    return True
        except Exception as e:
            # エラー発生時のロギング
            self.logger.error(f"Failed to check sns resources: {e}")
            return False

    def check_subscription_status(self, account_name: str):
        self.logger.info("Starting to check SNS subscription status...")
        """
        SNSのサブスクリプション設定を確認する

        Memo:
            SNSのサブスクリプションの追加はCore構築手順の一つ。
            BPM環境に作成された、SQSのキューのARNをサブスクリプションとして登録する作業がある。
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM#SNS%E3%81%AE%E3%82%B5%E3%83%96%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%97%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%92%E8%BF%BD%E5%8A%A0%E3%81%99%E3%82%8B
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/SNS+SQS

            Fin環境の期待値: arn:aws:sqs:${region}:${account_id}:dcjpy_bctracker_queue_push-notification.fifo
            Biz環境の期待値: arn:aws:sqs:${region}:${account_id}:dcjpy_bctracker_queue_email-sender.fifo
            BPM環境の期待値: なし
            IBC環境の期待値: なし
        """

        try:
            # SNS サブスクリプションのリストを取得
            response = self.client.list_subscriptions()
            subscriptions = response.get('Subscriptions', [])

            # CoreのBiz/Fin以外は処理しない
            if "-fin" not in account_name and "-biz" not in account_name:
                self.logger.info(f"ONLY Core Biz/Fin env needs to check. Skipped")
                return True

            # SNSサブスクリプションが適切に設定されているか確認
            pending_endpoints = []
            has_required_endpoint = False
            expected_endpoint = ""

            match account_name:
                case _ if "-fin" in account_name:
                    expected_endpoint = FIN_EXPECTED_ENDPOINT
                case _ if "-biz" in account_name:
                    expected_endpoint = BIZ_EXPECTED_ENDPOINT

            for sub in subscriptions:
                status = sub.get('SubscriptionArn', '')
                endpoint = sub.get('Endpoint', '')
                self.logger.info(f"Checking subscription: {endpoint}")

                # "PendingConfirmation" チェック
                if status == 'PendingConfirmation':
                    pending_endpoints.append(endpoint)
                    self.logger.info(f"{endpoint} is PendingConfirmation")

                # Endpointが存在するかチェック(取得できている=Confirm済み)
                if expected_endpoint in endpoint:
                    has_required_endpoint = True
                    self.logger.info(f"Account has required endpoint: {endpoint}")

            # エラー条件
            if pending_endpoints:
                self.logger.error("You have an Pending Endpoints:")
                for ep in pending_endpoints:
                    print(f" - {ep}")
                return False

            if not has_required_endpoint:
                self.logger.error(f"You don't have expected endpoint: *-{expected_endpoint}")
                return False

            return True

        except Exception as e:
            # エラー発生時のロギング
            self.logger.error(f"Failed to check sns resources: {e}")
            return False
