import logging

def setup_logger(name="AWSInfraChecker", level=logging.INFO, log_file=None):
    """
    ロガーをセットアップする関数。

    Args:
        name (str): ロガーの名前
        level (int): ログレベル (例: logging.INFO)
        log_file (str): ログを保存するファイルパス (デフォルト: None)
    Returns:
        logging.Logger: セットアップ済みのロガー
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # すでにハンドラーがある場合は追加しない
    if not logger.handlers:
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

        # コンソールハンドラー
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger

