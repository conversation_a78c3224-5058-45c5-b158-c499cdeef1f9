import subprocess
from .logger import setup_logger
from pathlib import Path

def bash_runner(path: str) -> bool:
    """
    指定されたパスの Bash スクリプトを実行します。

    Args:
        path (str): 実行するスクリプトのパス

    Returns:
        bool: 実行が成功した場合は True、それ以外は False
    """
    logger = setup_logger(name="bash_runner")
    script_path = Path(path)
    if not script_path.is_file():
        logger.error(f"Script not found at path: {path}")
        return False

    logger.info(f"Executing script: {path}")
    try:
        result = subprocess.run(
            ["bash", str(script_path)],
            check=True,
            capture_output=True,
            text=True
        )
        logger.info(f"<PERSON><PERSON><PERSON> succeeded with output:\n{result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"<PERSON><PERSON><PERSON> failed with error:\n{e.stderr.strip()}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error occurred: {str(e)}")
        return False