import boto3
import os
import sys
from .logger import setup_logger
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, ClientError
from typing import Optional


class AWSClient:
    def __init__(self, service_name, region_name="ap-northeast-1"):
        """
        指定されたAWSサービスのBoto3クライアントを初期化する

        Args:
            service_name (str): AWSサービス名
            region_name (str): AWSリージョン名
        """
        self.logger = setup_logger(name="AWSClient")
        try:
            self.client = boto3.client(service_name, region_name=region_name)
        except NoCredentialsError:
            self.logger.error("You don't have AWS credentials. Please confirm your settings")
            sys.exit(10)
        except PartialCredentialsError:
            self.logger.error("Your credential is invalid. Please confirm your settings")
            sys.exit(11)
        except Exception as e:
            self.logger.error(f"You got errors while initializing of the Service:{service_name}: {e}")
            sys.exit(100)

    def get_account_id(self) -> Optional[str]:
        """
        現在のAWSアカウントIDを取得。

        Returns:
            Optional[str]: AWSアカウントID（失敗した場合はNone）
        """
        try:
            response = self.client.get_caller_identity()
            account_id = response.get("Account")
            if account_id:
                self.logger.info(f"Account's ID: {account_id}")
                return account_id
            else:
                self.logger.warning(f"There is no such Account ID: {account_id}")
                return None
        except ClientError as e:
            self.logger.error(f"STS Client Error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"An unexpected error has occurred: {e}")
            return None

    def get_account_name(self):
        """
        AWS_PROFILE環境変数を取得する関数

        Returns:
            str: AWSアカウントName（失敗した場合はNone）

        Memo:
            本来は get_account_id() からアカウント名を取得したいが、権限の問題で取得できなかった
            また、下記のコンフィグファイルを使用している前提としている
            https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/AWS
        """
        account_name = os.getenv("AWS_PROFILE","None")
        self.logger.info(f"Account's Name: {account_name}")
        return account_name
