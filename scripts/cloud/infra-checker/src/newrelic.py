import os
import sys

import requests
from string import Template
from .utils.logger import setup_logger
from .utils.helpers import get_cluster_name

GRAPHQL_URL = "https://api.newrelic.com/graphql"


class Newrelic:
    def __init__(self):
        self.logger = setup_logger(name="Newrelic")
        self.api_key = os.getenv("NEWRELIC_API_KEY", "None")
        self.account_id = os.getenv("NEWRELIC_ACCOUNT_ID", "None")

        if self.api_key == "None" or self.account_id == "None":
            self.logger.error("Your NEWRELIC_API_KEY or NEWRELIC_ACCOUNT_ID is not declared."
                              "Please export both envs in your terminal.")

    def query_nrql(self, query_type: str, account_name: str, expect_data_count=1):
        """
        NRQLを投げる

        Memo:
            expect_data_countよりデータの数が多い: True
            expect_data_countよりデータの数が少ない: False
        """
        query = ""

        if query_type == "Log":
            query = (f"SELECT `level`,`message` FROM Log WHERE `Environment` = '{account_name}'")

        if query_type == "Metrics":
            cluster_name = get_cluster_name(account_name)
            query = (f"SELECT average(`aws.eks.apiserver_request_total`)"
                         " FROM Metric FACET `aws.eks.ClusterName`"
                         f" WHERE `aws.eks.ClusterName` = '{cluster_name}'"
                         " SINCE 1 HOUR AGO TIMESERIES")
        if not query:
            self.logger.error(f"You specified wrong query type in query_nrql(): {query_type}")
            sys.exit(3)

        headers = {
            "Api-Key": self.api_key,
            "Content-Type": "application/json",
        }

        # GraphQL形式のリクエストペイロード
        payload = {
            "query": f"""
            {{
                actor {{
                    account(id: {self.account_id}) {{
                        nrql( query: "{query}" ) {{
                            results
                        }}
                    }}
                }}
            }}
            """
        }

        try:
            # 期待値より多くのデータが取れればOK
            response = requests.post(GRAPHQL_URL, headers=headers, json=payload)
            if response.status_code == 200:
                self.logger.info(f"Newrelic EKS Cluster Metrics Check")
                print(response.json())
                result = (response.json()
                          .get("data", {})
                          .get("actor", {})
                          .get("account", {})
                          .get("nrql", {})
                          .get("results", []))
                present_data_count = len(result)
                self.logger.info(f"Present data count: {present_data_count}")
                self.logger.info(f"Expect data count: {expect_data_count} (or more)")
                if present_data_count >= expect_data_count:
                    self.logger.info(f"Newrelic metrics status is OK")
                    return True
                else:
                    self.logger.error(f"Newrelic metrics status is NG")
                    return False
            else:
                self.logger.error(f"Failed to execute NRQL query: {response.status_code}")
        except Exception as e:
            self.logger.error(f"NRQL query failed: {query}: {e}")
            return False

