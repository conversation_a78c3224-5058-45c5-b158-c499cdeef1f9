#!/bin/bash

# ネームスペース判別
aws sts get-caller-identity ||
  {
    echo "[ERROR] Failed to check your AWS_PROFILE. Please check your settings."
    exit 2
  }

echo "AWS Profile: ${AWS_PROFILE}"

REGION=$(aws ec2 describe-availability-zones --output text --query 'AvailabilityZones[0].[RegionName]')
echo "AWS Region: ${REGION}"

case $AWS_PROFILE in
*-biz* | *-fin*) ns=dcbg-core ;;
*-bpmbiz* | *-bpmfin*) ns=dcbg-bpm ;;
*)
  # RDS secretsが不要な環境は終了
  echo "[INFO] Not expects rds secrets in ${AWS_PROFILE}."
  echo "exit 0"
  exit 0
  ;;
esac

cluster_name=$(aws eks list-clusters --query 'clusters[0]' --output text)

echo "EKS Cluster: $cluster_name"

# 認証
echo -e "\n[INFO] update kube config for EKS"
aws eks update-kubeconfig --name ${cluster_name} ||
  {
    echo "[ERROR] Failed to update the kube context. Please check your permission and settings."
    exit $2
  }

# main
kubectl get secrets/secrets-rds -n ${ns}
status=$?

if [[ $status -eq 0 ]]; then
  echo "$0 is successful."
else
  echo "$0 returns invalid output."
  echo "exit ${status}."
fi

exit $status
