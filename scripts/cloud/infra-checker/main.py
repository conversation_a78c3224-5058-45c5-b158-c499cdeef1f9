import fire
import sys
from src.sns import SNS
from src.ses import SES
from src.newrelic import Newrelic
from src.utils.logger import setup_logger
from src.utils.aws_client import AWSClient
from src.utils.bash_runner import bash_runner


class AWSInfraChecker:
    """
    AWSインフラの状態を確認するCLIツール。
    """

    def __init__(self, command_result_handler):
        self.command_result = command_result_handler
        self.results = []
        self.logger = setup_logger()
        sts_client = AWSClient("sts")
        self.account_id = sts_client.get_account_id()
        self.account_name = sts_client.get_account_name()

    def sts(self):
        """認証情報を確認"""
        print(self.account_id)
        print(self.account_name)
        if self.account_id not in [None, "None", "Null"] and self.account_name not in [None, "None", "Null"]:
            self.results.append(("STS account check", True))
        else:
            self.results.append(("STS account check", False))

    def sns(self):
        """
        SNSの状態を確認
        - SNSのサンドボックスモードの状態
        - SQS用のサブスクリプションがあるかを確認
        """
        sns_client = SNS()

        """SNSのサンドボックスモードの状態を確認"""
        status_sms = sns_client.check_sms_sandbox_account_status(self.account_name)
        self.results.append(("SNS-sqs-sandbox-status", status_sms))

        """SQS用のサブスクリプションがあるかを確認"""
        status_sbs = sns_client.check_subscription_status(self.account_name)
        self.results.append(("SNS-sqs-subscription-status", status_sbs))

    def ses(self):
        """SESのサンドボックスモードの状態を確認"""
        sns_client = SES()
        status = sns_client.check_ses_sandbox_account_status(self.account_name)
        self.results.append(("SES", status))

    def cloudformation(self):
        """CloudFormationのStacksetsの状態を確認"""
        status = bash_runner("../cloudformation/check_stacksets.sh")
        self.results.append(("CloudFormation", status))

    def rds(self):
        """RDS Secretsの状態を確認"""
        status = bash_runner("bash/k8s_rds_secret_check.sh")
        self.results.append(("RDS k8s-secrets", status))

    def newrelic(self):
        """
        Newrelicの状態を確認
        - メトリクス転送ができるか確認
            - クエリは https://one.newrelic.com/nerdgraph-graphiql で生成可能
            - 参考: https://zenn.dev/collabostyle/articles/0405b743155f76
        """
        newrelic = Newrelic()
        status_metrics = newrelic.query_nrql("Metrics", self.account_name)
        self.results.append(("Newrelic Metrics", status_metrics))

        status_logs = newrelic.query_nrql("Log", self.account_name)
        self.results.append(("Newrelic Logs", status_logs))

    def all(self):
        """全ての状態を確認"""
        self.sns()
        self.ses()
        self.cloudformation()
        self.rds()
        self.newrelic()

    def __del__(self):
        """全ての結果"""
        if not self.results:
            self.logger.error("[NG] You've skipped all test.")
            self.command_result.is_command_successful = False
            return

        # 一つでもNGがあれば失敗とする
        all_ok = True
        print("\n=== Check Results ===")
        print("{:<30} {:<10}".format("Check Name", "Status"))
        print("=" * 40)
        for name, status in self.results:
            if status:
                print("{:<30} \033[92mOK\033[0m".format(name))  # 緑色
            else:
                print("{:<30} \033[91mNG\033[0m".format(name))  # 赤色
                all_ok = False
        print("=" * 40)

        if all_ok:
            print("\033[92mAll checks passed.\033[0m")
            self.logger.info("[OK] All checks passed.")
            self.command_result.is_command_successful = True
        else:
            print("\033[91mOne or more checks failed.\033[0m")
            self.logger.error("[NG] You have a NG status or more.")
            self.command_result.is_command_successful = False

class CommandResultHandler:
    def __init__(self):
        self.is_command_successful = False


if __name__ == "__main__":
    result = CommandResultHandler()
    fire.Fire(AWSInfraChecker(result))
    if result.is_command_successful:
        print(f"exit: 0")
        sys.exit(0)
    else:
        print(f"exit: 2")
        sys.exit(2)
