# インフラ動作確認スクリプト

## 概要
インフラリソースの設定の定点確認のためのチェックスクリプト。

## ステータス: プロトタイプ
以下のチケットにて開発中。
https://decurret.atlassian.net/browse/DCPF-1248

## 前提条件

python version = 3.12.x


## セットアップ

```
#python&pip setup
brew upgrade pyenv
pyenv install -l | grep 3.12
pyenv install 3.12.8
pyenv local 3.12.8
python -V
pip install -r requirements.txt
#test
python3 main sts
```

### スクリプトの使用方法

基本的な構文

```
python main.py [リソース名]
```

認証情報を確認する

```
$ py main.py sts
```

全てをチェックする時

```
python main.py all
```

ヘルプの出力(引数なし)

```
python main.py
```

出力例

```
NAME
    main.py - AWSインフラの状態を確認するCLIツール。

SYNOPSIS
    main.py GROUP | COMMAND | VALUE

DESCRIPTION
    AWSインフラの状態を確認するCLIツール。

GROUPS
    GROUP is one of the following:

     command_result

     results

COMMANDS
    COMMAND is one of the following:

     all
       全ての状態を確認

     cloudformation
       CloudFormationのStacksetsの状態を確認

     newrelic
       Newrelicの状態を確認 - メトリクス転送ができるか確認 - クエリは https://one.newrelic.com/nerdgraph-graphiql で生成可能 - 参考: https://zenn.dev/collabostyle/articles/0405b743155f76

     rds
       RDS Secretsの状態を確認

     ses
       SESのサンドボックスモードの状態を確認

     sns
       SNSの状態を確認 - SNSのサンドボックスモードの状態 - SQS用のサブスクリプションがあるかを確
認

     sts
       認証情報を確認

VALUES
    VALUE is one of the following:

     account_id
```

#### Newrelicのチェックの場合

実行前に以下の環境変数を宣言してから実行してください

```
export NEWRELIC_API_KEY=NRAK-XXXXXXXXXXXXXXXXXX
export NEWRELIC_ACCOUNT_ID=4002550 #dev-cl
```

実行

```
py main.py newrelic
```

#### Newrelicのチェックの場合

実行前に以下の環境変数を宣言してから実行してください

```
export NEWRELIC_API_KEY=NRAK-XXXXXXXXXXXXXXXXXX
export NEWRELIC_ACCOUNT_ID=4002550 #dev-cl
```

実行

```
py main.py newrelic
```

## ディレクトリ構成

```
tree | grep -v pyc
.
├── README.md
├── bash
│   └── k8s_rds_secret_check.sh
├── main.py
├── requirements.txt
└── src
    ├── newrelic.py
    ├── ses.py
    ├── sns.py
    └── utils
        ├── aws_client.py
        ├── bash_runner.py
        ├── helpers.py
        └── logger.py
```

## 関連資料・設計
[\[運用設計]スクリプトによるAWS正常性確認の運用方針](https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/AWS#%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%97%E3%83%88%E6%A6%82%E8%A6%81)
