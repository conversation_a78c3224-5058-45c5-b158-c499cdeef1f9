apiVersion: apps/v1
kind: Deployment
metadata:
  name: albctest
  namespace: {{ .Values.namespace }}
  labels:
    app: albctest
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: albctest
  template:
    metadata:
      name: "albctest_pod"
      labels:
        app: albctest
    spec:
      shareProcessNamespace: true
      serviceAccountName: albctest-service-account
      containers:
        - name: newrelic-infrastructure
          image: {{ .Values.newrelicAgent.imageName }}:{{ .Values.newrelicAgent.imageTag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: NRIA_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  key: license-key
                  name: newrelic-license-key
            - name: NRIA_LOG_LEVEL
              value: "info"
            - name: NRIA_VERBOSE
              value: "0"
            - name: DISABLE_KUBE_STATE_METRICS
              value: "true"
            - name: CLUSTER_NAME
              value: dcbg-dcf_{{ .Values.newrelicAgent.envTag }}_EKSCluster
            - name: COMPUTE_TYPE
              value: serverless
            - name: NRK8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_DISPLAY_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_CUSTOM_ATTRIBUTES
              value: '{"clusterName":"$(CLUSTER_NAME)", "Environment":"{{ .Values.newrelicAgent.envTag }}"}'
            - name: NRIA_PASSTHROUGH_ENVIRONMENT
              value: KUBERNETES_SERVICE_HOST,KUBERNETES_SERVICE_PORT,CLUSTER_NAME,CADVISOR_PORT,NRK8S_NODE_NAME,KUBE_STATE_METRICS_URL,KUBE_STATE_METRICS_POD_LABEL,TIMEOUT,ETCD_TLS_SECRET_NAME,ETCD_TLS_SECRET_NAMESPACE,API_SERVER_SECURE_PORT,KUBE_STATE_METRICS_SCHEME,KUBE_STATE_METRICS_PORT,SCHEDULER_ENDPOINT_URL,ETCD_ENDPOINT_URL,CONTROLLER_MANAGER_ENDPOINT_URL,API_SERVER_ENDPOINT_URL,DISABLE_KUBE_STATE_METRICS,DISCOVERY_CACHE_TTL
          resources:
            limits:
              cpu: 200m
              memory: 100M
            requests:
              cpu: 100m
              memory: 50M
        - name: albctest
          image: {{ .Values.image.name }}:{{ .Values.image.tag }}
          imagePullPolicy: IfNotPresent
          args:
            - -listen=:{{ .Values.port }}
            - -text=OK
          resources:
            requests:
              cpu: {{ .Values.resources.requests.cpu }}
              memory: {{ .Values.resources.requests.memory }}
            limits:
              cpu: {{ .Values.resources.limits.cpu }}
              memory: {{ .Values.resources.limits.memory }}
