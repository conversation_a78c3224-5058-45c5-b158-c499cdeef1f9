#!/bin/bash -e

# load common resources
DIR_PATH=$(cd $(dirname $0); pwd)
source "${DIR_PATH}/_common_sops_encryption.sh"

# check if the key exists
SOPS_KEY_ARN=$(get_sops_encryption_key_arn)
if [[ -n "${SOPS_KEY_ARN}" ]]; then
  echo "error: sops key already exists (${SOPS_KEY_ARN})" 1>&2
  exit 1
fi

# create a key and an alias
echo "creating a key..."
SOPS_KEY_ARN=$(aws kms create-key \
  --description 'encryption key for sops' \
  --output text \
  --query 'KeyMetadata.Arn')
aws kms create-alias \
  --alias-name "${SOPS_KEY_ALIAS}" \
  --target-key-id "${SOPS_KEY_ARN}"

echo "${SOPS_KEY_ARN}"
