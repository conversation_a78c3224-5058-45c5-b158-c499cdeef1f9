#!/usr/bin/env bash

# common function for start/stop env

check_tools () {
    if [[ $# -eq 0 ]]; then
        printf "Error(check_tools): Some argument(required tools) are required.\n"
        return 1
    fi
    tools=(${1})
    check_flag=1
    printf "\n"
    for tool in "${tools[@]}"
    do 
        if ! LANG=C type ${tool} 2>/dev/null; then
            printf "Required tools '%s' is not installed.\n" ${tool}
            check_flag=0
        fi
        printf "\n"
    done
    if ((${check_flag})); then
        return 0
    else
        printf "Error: Some or all of the required tools are not installed\n"
        return 1
    fi
}

# aws profile check

aws_profile_check () {
    # check an argument
    if [[ $# -eq 0 ]]; then
        printf "Error(aws_profile_check): 1 Argument(AWS profile name) is required.\n"
        return 1
    fi
    # chech sts
    AWS_STS_RESULT=$(aws sts get-caller-identity --profile "${1}" 2>&1)
    AWS_STS_RET=$?
    echo "${AWS_STS_RESULT}"
    
    if [[ ${AWS_STS_RET} != "0" ]]; then
        return ${AWS_STS_RET}
    fi
    printf "\nProfile checks are OK\n"
    return 0
}

# set BESU instance check command

set_besu_instance_chek_cmd () {
    if [[ $# -eq 0 ]]; then
        printf "Error(set_besu_instance_chek_cmd): 1 Argument(AWS profile name) is required.\n"
        return 1
    fi
    int_listener_fin="aws ec2 describe-instances --query \"Reservations[].Instances[?contains(Tags[?Key=='Name'].Value|[0],'listener_fin')][].[InstanceId,State.Name]\" --output text --profile ${1}"
    int_listener_ind="aws ec2 describe-instances --query \"Reservations[].Instances[?contains(Tags[?Key=='Name'].Value|[0],'listener_ind')][].[InstanceId,State.Name]\" --output text --profile ${1}"
    shared_validator="aws ec2 describe-instances --query \"Reservations[].Instances[?contains(Tags[?Key=='Name'].Value|[0],'validator') && contains(Tags[?Key=='Name'].Value|[0],'shared') && !contains(Tags[?Key=='Name'].Value|[0],'snapshot')][].[InstanceId,State.Name]\" --output text --profile ${1}"
    shared_listener="aws ec2 describe-instances --query \"Reservations[].Instances[?contains(Tags[?Key=='Name'].Value|[0],'listener') && contains(Tags[?Key=='Name'].Value|[0],'shared') && !contains(Tags[?Key=='Name'].Value|[0],'snapshot')][].[InstanceId,State.Name]\" --output text --profile ${1}"
    validator="aws ec2 describe-instances --query \"Reservations[].Instances[?contains(Tags[?Key=='Name'].Value|[0],'validator') && !contains(Tags[?Key=='Name'].Value|[0],'shared') && !contains(Tags[?Key=='Name'].Value|[0],'snapshot')][].[InstanceId,State.Name]\" --output text --profile ${1}"
    listener="aws ec2 describe-instances --query \"Reservations[].Instances[?contains(Tags[?Key=='Name'].Value|[0],'listener') && !contains(Tags[?Key=='Name'].Value|[0],'shared') && !contains(Tags[?Key=='Name'].Value|[0],'snapshot')][].[InstanceId,State.Name]\" --output text --profile ${1}"
}

rds_cluster_name (){
    if [[ $# -eq 0 ]]; then
        printf "Error(rds_cluster_name): 1 Argument(AWS profile name) is required.\n"
        return 1
    fi
    RDS_CLUSTER_NAME=$(aws rds describe-db-clusters --query "DBClusters[].DBClusterIdentifier" --profile ${1} --output text)
    echo "${RDS_CLUSTER_NAME}"
}

rds_cluster_status () {
    if [[ $# -eq 0 ]]; then
        printf "Error(rds_cluster_status): 1 Argument(AWS profile name) is required.\n"
        return 1
    fi
    RDS_CLUSTER_NAME=$(rds_cluster_name "${1}")
    local STATUS
    STATUS=$(aws rds describe-db-clusters --db-cluster-identifier ${RDS_CLUSTER_NAME} --query "DBClusters[].Status" --profile ${1} --output text)
    echo "${STATUS}"
}


besu_all_running () {

    local STATUS
    local IS_INT
    local IS_INT_NOT_RUNNING
    local IS_FIN
    local IS_NOT_RUNNING

    STATUS="running"
    IS_INT=0
    IS_INT_NOT_RUNNING=0
    IS_FIN=0
    IS_NOT_RUNNING=0

    unset BESU_PROJECT_NAME_LIST

    if [[ "${1}" =~ "bpm" ]]; then
        printf '\nN/A. The profile is BPM environment.\n'
        return 1
    fi

    set_besu_instance_chek_cmd "${1}"

    # Check if it is an INT environment
    if [[ "${1}" =~ "int" ]]; then
        IS_INT=1
        printf "\nThis profile seems to be an INT environment.\n\n"
        if ! eval ${int_listener_fin} | grep -v "${STATUS}"; then
            printf "\nall INT(listener_fin) instances are running.\n\n"
        else
            printf "\nSome INT(listener_fin) instances do not seem to have been running.\n\n"
            return 1
        fi
        if ! eval ${int_listener_ind} | grep -v "${STATUS}"; then
            printf "\nall INT(listener_ind) instances are running.\n\n"
        else
            printf "\nSome INT(listener_ind) instances do not seem to have been running.\n\n"
            return 1
        fi
        printf "\nALL BESU instances are running in %s\n" "${1}"
        return 0
    fi

    # Check FIN/IND environment
    if [[ ${IS_INT} -eq 0 ]]; then
        if [[ "${1}" =~ "fin" ]]; then
            IS_FIN=1
            printf "\nThis profile seems to be an FIN environment.\n\n"
            if ! eval ${shared_validator} | grep -v "${STATUS}"; then
                printf "\nall %s (shared_validator) instances are running.\n\n" "${1}"
            else
                printf "\nSome %s (shared_validator) instances do not seem to have been running.\n\n" "${1}"
                return 1
            fi
        fi
        if [[ ${IS_FIN} -eq 0 ]]; then
            printf "\nThis profile seems to be an IND environment.\n\n"
        fi
        if ! eval ${shared_listener} | grep -v "${STATUS}"; then
            printf "\nall %s (shared_listener) instances are running.\n\n" "${1}"
        else
            printf "\nSome %s (shared_listener) instances do not seem to have been running.\n\n" "${1}"
            return 1
        fi
        if ! eval ${validator} | grep -v "${STATUS}"; then
            printf "\nall %s (validator) instances are running.\n\n" "${1}"
        else
            printf "\nSome %s (validator) instances do not seem to have been running.\n\n" "${1}"
            return 1
        fi
        if ! eval ${listener} | grep -v "${STATUS}"; then
            printf "\nall %s (listener) instances are running.\n\n" "${1}"
        else
            printf "\nSome %s (listener) instances do not seem to have been running.\n\n" "${1}"
            return 1
        fi
        printf "\nALL BESU instances are running in %s.\n" "${1}"
        return 0
    fi
}

besu_all_stopped () {

    local STATUS
    local IS_INT
    local IS_INT_NOT_STOPPED
    local IS_FIN
    local IS_NOT_STOPPED

    STATUS="stopped"
    IS_INT=0
    IS_INT_NOT_STOPPED=0
    IS_FIN=0
    IS_NOT_STOPPED=0

    unset BESU_PROJECT_NAME_LIST

    if [[ "${1}" =~ "bpm" ]]; then
        printf '\nN/A. The profile is BPM environment.\n'
        return 1
    fi

    set_besu_instance_chek_cmd "${1}"
    
    # Check if it is an INT environment
    if [[ "${1}" =~ "int" ]]; then
        IS_INT=1
        printf "\nThis profile seems to be an INT environment.\n\n"
        if ! eval ${int_listener_fin} | grep -v "${STATUS}"; then
            printf "\nall INT(listener_fin) instances are stopped.\n\n"
        else
            printf "\nSome INT(listener_fin) instances do not seem to have stopped.\n\n"
            return 1
        fi
        if ! eval ${int_listener_ind} | grep -v "${STATUS}"; then
            printf "\nall INT(listener_ind) instances are stopped.\n\n"
        else
            printf "\nSome INT(listener_ind) instances do not seem to have stopped.\n\n"
            return 1
        fi
        printf "\nALL BESU instances are stopped in %s\n" "${1}"
        return 0
    fi

    # Check FIN/IND environment
    if [[ ${IS_INT} -eq 0 ]]; then
        if [[ "${1}" =~ "fin" ]]; then
            IS_FIN=1
            printf "\nThis profile seems to be an FIN environment.\n\n"
            if ! eval ${shared_validator} | grep -v "${STATUS}"; then
                printf "\nall %s (shared_validator) instances are stopped.\n\n" "${1}"
            else
                printf "\nSome %s (shared_validator) instances do not seem to have stopped.\n\n" "${1}"
                return 1
            fi
        fi
        if [[ ${IS_FIN} -eq 0 ]]; then
            printf "\nThis profile seems to be an IND environment.\n\n"
        fi
        if ! eval ${shared_listener} | grep -v "${STATUS}"; then
            printf "\nall %s (shared_listener) instances are stopped.\n\n" "${1}"
        else
            printf "\nSome %s (shared_listener) instances do not seem to have stopped.\n\n" "${1}"
            return 1
        fi
        if ! eval ${validator} | grep -v "${STATUS}"; then
            printf "\nall %s (validator) instances are stopped.\n\n" "${1}"
        else
            printf "\nSome %s (validator) instances do not seem to have stopped.\n\n" "${1}"
            return 1
        fi
        if ! eval ${listener} | grep -v "${STATUS}"; then
            printf "\nall %s (listener) instances are stopped.\n\n" "${1}"
        else
            printf "\nSome %s (listener) instances do not seem to have stopped.\n\n" "${1}"
            return 1
        fi
        printf "\nALL BESU instances are stopped in %s\n" "${1}"
        return 0
    fi
}

eks_cluster_status () {
    if [[ $# -eq 0 ]]; then
        printf "Error(eks_cluster_status): 1 Argument(AWS profile name) is required.\n"
        return 1
    fi
    EKS_CLUSTER_NAME=$(aws eks list-clusters --profile ${1} --query "clusters[]" --output text)
    local STATUS
    STATUS=$(aws eks describe-cluster --name ${EKS_CLUSTER_NAME} --profile ${1} --query "cluster.status" --output text)
    echo "${STATUS}"
}