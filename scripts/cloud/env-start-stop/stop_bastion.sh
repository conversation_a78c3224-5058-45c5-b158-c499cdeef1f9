#!/usr/bin/env bash

usage() {
    cat <<EOF

$(basename "${0}") is a tool for stop bastion host.

Usage:
    $(basename "${0}") [<options>]

Options:
    --profile, -p   Set awsp profile configured in ~/.aws/config.
    --help, -h      Print this.
EOF
}

. common_function.sh

# argument proccessing
while (($# > 0)); do
    case $1 in
    -p | --profile | --profile=*)
        if [[ "$1" =~ ^--profile= ]]; then
            PROFILE="${1/--profile=/}"
        elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
            printf "\nError: 'profile' requires an argument.\n" 1>&2
            exit 1
        else
            PROFILE=$2
            shift
        fi
        ;;
    -h | --help)
        usage
        exit 1
        ;;
    -*)
        printf "\nError: invalid option %s\n" "$1" 1>&2
        usage
        exit 1
        ;;
    *)
        ARGS=("${ARGS[@]}" "$1")
        ;;
    esac
    shift
done

if [[ -z "${PROFILE}" ]] && [[ -z "${AWS_PROFILE}" ]]; then
    printf "\nError: 'profile' or \$AWS_PROFILE is not set.\n"
    exit 1
elif [[ -z "${PROFILE}" ]] && [[ -n ${AWS_PROFILE} ]]; then
    PROFILE=${AWS_PROFILE}
fi

# check "aws sts" and aws profile
printf "\nprofile: %s\n\n" "${PROFILE}"

if ! aws_profile_check "${PROFILE}"; then
    exit 1
fi

# check tools
if ! check_tools "aws jq"; then
    exit 1
fi

# get bastion instance id
INSTANCE_ID=$(aws ec2 describe-instances \
    --filters Name=tag:Name,Values='*-bastion' \
    --query "Reservations[].Instances[].InstanceId" \
    --output text --profile "${PROFILE}")

# stop instance
aws ec2 stop-instances --instance-ids "${INSTANCE_ID}" --profile "${PROFILE}"
printf "\nBastion instance is stopping.\n"

# check status
while :; do
    STATUS=$(aws ec2 describe-instances \
        --instance-ids "${INSTANCE_ID}" \
        --query "Reservations[].Instances[].State.Name" \
        --output text --profile "${PROFILE}")
    if [[ ${STATUS} == 'stopped' ]]; then
        printf "\nBastion instance stopped.\n"
        break
    fi
    sleep 5
    i=${SECONDS}
    ((sec = i % 60, min = (i % 3600) / 60))
    TIMESTAMP=$(printf "%02d:%02d" "${min}" "${sec}")
    printf "Elapsed Time: %s  Status: %s\n" "${TIMESTAMP}" "${STATUS}"
done
