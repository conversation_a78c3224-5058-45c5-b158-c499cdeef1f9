#!/usr/bin/env bash

usage () {
    cat <<EOF

$(basename ${0}) is a tool for stop EKS Pods.

Usage:
    $(basename ${0}) [<options>]

Options:
    --profile, -p   Set awsp profile configured in ~/.aws/config.
    --force, -f     Attempt to stop EKS Pods without prompting for confirmation.
    --help, -h      Print this.
EOF
}

. common_function.sh

# argument proccessing
while (( $# > 0))
do
    case $1 in
        -p | --profile | --profile=*)
            if [[ "$1" =~ ^--profile= ]]; then
                PROFILE="${1/--profile=/}"
            elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
                printf "\nError: 'profile' requires an argument.\n" 1>&2
                exit 1
            else 
                PROFILE=$2
                shift
            fi
            ;;
        -f | --force)
            FORCE=1
            ;;
        -h | --help)
            usage
            exit 1
            ;;
        -*)
            printf "\nError: invalid option %s\n" "$1" 1>&2
            usage
            exit 1
            ;;
        *)
            ARGS=("${ARGS[@]}" "$1")
            ;;
    esac
    shift
done

if [[ -z "${PROFILE}" ]] && [[ -z "${AWS_PROFILE}" ]]; then
    printf "\nError: 'profile' or \$AWS_PROFILE is not set.\n"
    exit 1
    elif [[ -z "${PROFILE}" ]] && [[ -n ${AWS_PROFILE} ]]; then
    PROFILE=${AWS_PROFILE}
fi

# check tools
if ! check_tools "aws jq"; then
    exit 1
fi

# check "aws sts" and aws profile
printf "\nprofile: %s\n\n" "${PROFILE}"

if ! aws_profile_check "${PROFILE}" ; then
    exit 1
fi

##### stop EKS #####
printf "\nYou are about to stop EKS Pods, in profile: %s\n\n" "${PROFILE}"

if [[ ${FORCE} -eq 0 ]]; then
    read -n1 -p "Are you sure? (y/N): " yn
    case "${yn}" in 
        [yY]*) 
            echo
            ;;
        *) 
            printf "\n\nabort.\n"
            exit 1
            ;;
    esac
fi

EKS_PROJECT_NAME_LIST=($(aws codebuild list-projects --profile "${PROFILE}" | jq -r '.projects[]' | grep 'eks-pod-stop'))

# start CodeBuild project
for PROJECT_NAME in "${EKS_PROJECT_NAME_LIST[@]}"
do
    printf "\nStart Build: %s\n" "${PROJECT_NAME}"
    aws codebuild start-build --project-name "${PROJECT_NAME}" --profile "${PROFILE}" --no-cli-pager
done

# get Build ID list
EKS_BUILD_ID_LIST=($(aws codebuild list-builds --profile "${PROFILE}" --sort-order DESCENDING | jq -r '.ids[]' | head -1))
printf "\nBuild list:"
echo "${EKS_BUILD_ID_LIST[@]}"


printf "\n%s builds in progress.\n\n" "${#EKS_BUILD_ID_LIST[@]}"
SECONDS=0

while :
do
    i=0
        for BUILD_ID in "${EKS_BUILD_ID_LIST[@]}"
        do
            BUILD_STATUS=$(aws codebuild batch-get-builds --ids "${BUILD_ID}" --profile "${PROFILE}" | jq -r '.builds[].buildStatus')
            echo "${BUILD_STATUS}"
            if [[ ${BUILD_STATUS} == 'SUCCEEDED' ]]; then
                ((i++))
            fi
        done
    if [[ ${i} -eq ${#EKS_BUILD_ID_LIST[@]} ]]; then
        printf "\nAll EKS build projects are succeeded. EKS Pods stopped.\n"
        break
    fi
    i=${SECONDS}
    ((sec=i%60, min=(i%3600)/60))
    TIMESTAMP=$(printf "%02d:%02d" "${min}" "${sec}")
    printf "Elapsed Time: %s\n\n" "${TIMESTAMP}"
    sleep 10
done

exit 0
