#!/usr/bin/env bash

usage () {
    cat <<EOF

$(basename ${0}) is a tool for stop entire Environment.

Usage:
    $(basename ${0}) [<options>]

Options:
    --profile, -p   Set awsp profile configured in ~/.aws/config.
    --help, -h      Print this.
EOF
}

. common_function.sh

# argument proccessing
while (( $# > 0))
do
    case $1 in
        -p | --profile | --profile=*)
            if [[ "$1" =~ ^--profile= ]]; then
                PROFILE="${1/--profile=/}"
            elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
                printf "\nError: 'profile' requires an argument.\n" 1>&2
                exit 1
            else 
                PROFILE=$2
                shift
            fi
            ;;
        -h | --help)
            usage
            exit 1
            ;;
        -*)
            printf "\nError: invalid option %s\n" "$1" 1>&2
            usage
            exit 1
            ;;
        *)
            ARGS=("${ARGS[@]}" "$1")
            ;;
    esac
    shift
done

if [[ -z "${PROFILE}" ]] && [[ -z "${AWS_PROFILE}" ]]; then
    printf "\nError: 'profile' or \$AWS_PROFILE is not set.\n"
    exit 1
    elif [[ -z "${PROFILE}" ]] && [[ -n ${AWS_PROFILE} ]]; then
    PROFILE=${AWS_PROFILE}
fi

# check "aws sts" and aws profile
printf "\nprofile: %s\n\n" "${PROFILE}"

if ! aws_profile_check "${PROFILE}" ; then
    exit 1
fi

# check tools
if ! check_tools "./stop_rds.sh ./stop_besu.sh ./stop_eks.sh"; then
    exit 1
fi

printf "\nYou are about to stop %s environment.\n\n" "${PROFILE}"

# Are you sure ?
read -n1 -p "Are you sure? (y/N): " yn
    case "${yn}" in 
        [yY]*) 
            echo
            ;;
        *) 
            printf "\n\nabort.\n"
            exit 1
            ;;
    esac

# Discriminate the environment
if   [[ "${PROFILE}" =~ "int" ]]; then
    INT_FLAG=true
    if [[ "${INT_FLAG}" ]]; then
        printf '\nINT environment.\n'
    fi
elif [[ "${PROFILE}" =~ "bpm" ]]; then
    BPM_FLAG=true
    if [[ "${BPM_FLAG}" ]]; then
        printf '\nBPM environment.\n'
    fi
fi

# stop EKS Pods
./stop_eks.sh -f -p "${PROFILE}"
if [[ $? -ne 0 ]]; then
    printf "Abort: EKS Pods stop did not succeed for some reason. (See above)"
    exit 1
fi

# stop BESU instances
if besu_all_stopped "${PROFILE}" ; then
    printf '\nAll BESU instances are already stopped.\n'
elif [[ ! "${BPM_FLAG}" ]]; then
    ./stop_besu.sh -f -p "${PROFILE}"
    if [[ $? -ne 0 ]]; then
        printf "Abort: BESU stop did not succeed for some reason. (See above)"
        exit 1
    fi
fi

# stop RDS cluster
if [[ $(rds_cluster_status "${PROFILE}") = "stopped" ]]; then
    printf '\nRDS cluster is already stopped.\n'
elif [[ $(rds_cluster_status "${PROFILE}") = "available" ]] && [[ ! "${INT_FLAG}" ]]; then
    ./stop_rds.sh -f -p "${PROFILE}"
    if [[ $? -ne 0 ]]; then
        printf "Abort: RDS stop did not succeed for some reason. (See above)"
        exit 1
    fi
fi

exit 0
