#!/usr/bin/env bash

usage () {
    cat <<EOF

$(basename ${0}) is a tool for stop BESU instances.

Usage:
    $(basename ${0}) [<options>]

Options:
    --profile, -p   Set awsp profile configured in ~/.aws/config.
    --force, -f     Attempt to stop BESU instances without prompting for confirmation.
    --help, -h      Print this.
EOF
}

FORCE=0

. common_function.sh

# argument proccessing
while (( $# > 0))
do
    case $1 in
        -p | --profile | --profile=*)
            if [[ "$1" =~ ^--profile= ]]; then
                PROFILE="${1/--profile=/}"
            elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
                printf "\nError: 'profile' requires an argument.\n" 1>&2
                exit 1
            else 
                PROFILE=$2
                shift
            fi
            ;;
        -f | --force)
            FORCE=1
            ;;
        -h | --help)
            usage
            exit 1
            ;;
        -*)
            printf "\nError: invalid option %s\n" "$1" 1>&2
            usage
            exit 1
            ;;
        *)
            ARGS=("${ARGS[@]}" "$1")
            ;;
    esac
    shift
done

if [[ -z "${PROFILE}" ]] && [[ -z "${AWS_PROFILE}" ]]; then
    printf "\nError: 'profile' or \$AWS_PROFILE is not set.\n"
    exit 1
    elif [[ -z "${PROFILE}" ]] && [[ -n ${AWS_PROFILE} ]]; then
    PROFILE=${AWS_PROFILE}
fi

# check tools
if ! check_tools "aws jq"; then
    exit 1
fi

# check "aws sts" and aws profile
printf "\nprofile: %s\n\n" "${PROFILE}"

if ! aws_profile_check "${PROFILE}" ; then
    exit 1
fi

# check BESU instances status
if besu_all_stopped "${PROFILE}" ; then
    exit 1
fi

printf "\nYou are about to stop BESU instances, in profile: %s\n\n" "${PROFILE}"

if [[ ${FORCE} -eq 0 ]]; then
    read -n1 -p "Are you sure? (y/N): " yn
    case "${yn}" in 
        [yY]*) 
            echo
            ;;
        *) 
            printf "\n\nabort.\n"
            exit 1
            ;;
    esac
fi

##### stop BESU #####
printf "\nStop BESU instance (EC2) in %s\n\n" "${PROFILE}"

# get CodeBuild project list 
BESU_PROJECT_NAME_LIST=($(aws codebuild list-projects --profile ${PROFILE} | jq -r '.projects[]' | grep 'besu-stop' | sort -r))

# start CodeBuild project
for PROJECT_NAME in "${BESU_PROJECT_NAME_LIST[@]}"
do
    printf "\nStart Build: %s\n" "${PROJECT_NAME}"
    aws codebuild start-build --project-name "${PROJECT_NAME}" --no-cli-pager --profile "${PROFILE}"
done

# get Build ID list
BESU_BUILD_ID_LIST=($(aws codebuild list-builds --sort-order DESCENDING --profile ${PROFILE} | jq -r '.ids[]' | head -${#BESU_PROJECT_NAME_LIST[@]}))
printf "\nBuild ID list:\n%s" "${BESU_BUILD_ID_LIST[*]}"

printf "\n%s BESU builds in progress.\n\n" "${#BESU_BUILD_ID_LIST[@]}"

SECONDS=0

while :
do
    i=0
        for BUILD_ID in "${BESU_BUILD_ID_LIST[@]}"
        do
            echo "${BUILD_ID}"
            BUILD_STATUS=$(aws codebuild batch-get-builds --ids "${BUILD_ID}" --profile "${PROFILE}" | jq -r '.builds[].buildStatus')
            echo "${BUILD_STATUS}"
            if [[ ${BUILD_STATUS} == 'SUCCEEDED' ]]; then
                ((i++))
            fi
        done
    if [[ ${i} -eq ${#BESU_BUILD_ID_LIST[@]} ]]; then
        printf "\nAll BESU build projects are succeeded. All BESU instnaces stopped.\n"
        break
    fi
    i=${SECONDS}
    ((sec=i%60, min=(i%3600)/60))
    TIMESTAMP=$(printf "%02d:%02d" "${min}" "${sec}")
    printf "\nElapsed Time: %s\n\n" "${TIMESTAMP}"
    sleep 10
done

exit 0
