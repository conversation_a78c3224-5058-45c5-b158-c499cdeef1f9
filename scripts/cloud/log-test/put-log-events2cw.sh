#!/usr/bin/env bash

CMDNAME=$(basename "$0")
USAGE="usage: ${CMDNAME} -t [eks|lamda] -l [basename of log group name] \"message\" "


# capture piped data
if [ -p /dev/stdin ]; then
    INPUT=$(cat)
    # printf "Using message recieved by pipe.\n\n"
fi

# check tools
if ! LANG=C type aws node; then
    printf "\nRequired tools are not installed."
    exit 1
fi

# check aws sts
printf "\nprofile: %s\n\n" "${AWS_PROFILE}"

if ! aws sts get-caller-identity; then
    printf "\nMake sure you've run aws-mfa or check your profile setting (~/.aws/config)\n"
    exit 1
else
    echo
fi

# argument processing

ARG_COUNT=$#

while (( $# > 0))
do
    case $1 in
        -t | --type | --type=*)
            if [[ "$1" =~ ^--type= ]]; then
                TYPE="${1/--type=/}"
            elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
                echo "Error: 'type' requires an argument." 1>&2
                exit 1
            else 
                TYPE=$2
                shift
            fi
            ;;
        -l | --lg-basename | --lg-basename=*)
            if [[ "$1" =~ ^--lg-basename= ]]; then
                LG_BASENAME="${1/--lg-basename=/}"
            elif [[ -z "$2" ]] || [[ "$2" =~ ^-+ ]]; then
                echo "Error: 'lg-basename' requires an argument." 1>&2
                exit 1
            else 
                LG_BASENAME=$2
                shift
            fi
            ;;
        -*)
            echo "invalid option $1"
            exit 1
            ;;
        *)
            ARGS=("${ARGS[@]}" "$1")
            ;;
    esac
    shift
done

if [[ $ARG_COUNT -eq 0 ]]; then
    echo "Error: Missing arguments."
    echo "${USAGE}"
    exit 1
elif [[ ${#ARGS[@]} -eq 0 ]] && [[ -z ${INPUT} ]]; then
    echo "Error: Missing message to create event."
    echo "${USAGE}"
    exit 1
elif [[ -n ${INPUT} ]]; then
    MSG="${INPUT}"
else
    MSG="${ARGS[0]}"
fi

# $TYPE check
if [[ -z $TYPE ]]; then
    echo "Error: 'type' is required. (i.e. -t [eks|lambda] )" 1>&2
    exit 1
fi

# log group name check 
if [[ -z $LG_BASENAME ]]; then
    echo "'lg-basename' is required. (i.e. -l [basename of log group name] )" 1>&2
else 
    LOG_GROUP_NAME=$(aws logs describe-log-groups \
                        --query "logGroups[?(contains(logGroupName,'$TYPE') \
                        && ends_with(logGroupName,'$LG_BASENAME'))].logGroupName" \
                        --output text)
    if [[ -z $LOG_GROUP_NAME ]]; then
        echo "Error: log group name does not exist."
        exit 1
    else
        printf "target log group name: %s\n\n" "${LOG_GROUP_NAME}"
    fi
fi

printf "Message: %s\n\n" "${MSG}"

LOG_STREAM="test-stream-$(date '+%F')"

# If test-stream does not exist, create it.
CHK_STREAM=$(aws logs describe-log-streams \
                   --log-group-name "${LOG_GROUP_NAME}" \
                   --query "logStreams[?logStreamName=='${LOG_STREAM}'].logStreamName" \
                   --output text)

if [ -z "$CHK_STREAM" ]; then
    aws logs create-log-stream \
        --log-group-name "${LOG_GROUP_NAME}" \
        --log-stream-name "${LOG_STREAM}" 
    printf "\nlog stream \"%s\" is created.\n\n" "${LOG_STREAM}"
fi

# If uploadSequenceToken is none, write log-event for the first time, 
# otherwise write log event using uploadSequenceToken
TOKEN=$(aws logs describe-log-streams \
            --log-group-name "${LOG_GROUP_NAME}" \
            --query "logStreams[?logStreamName=='${LOG_STREAM}'].[uploadSequenceToken]" \
            --output=text)

# Escape double-quote is required
ESC_MSG="${MSG//\"/\\\"}"

# put log events
if [ "$TOKEN" != "None" ]; then
    aws logs put-log-events \
        --log-group-name "${LOG_GROUP_NAME}" \
        --log-stream-name "${LOG_STREAM}" \
        --log-events timestamp="$(node -e 'console.log(Date.now())')",message=\""${ESC_MSG}"\" \
        --sequence-token="${TOKEN}"
    RET=$?
else
    aws logs put-log-events \
        --log-group-name "${LOG_GROUP_NAME}" \
        --log-stream-name "${LOG_STREAM}" \
        --log-events timestamp="$(node -e 'console.log(Date.now())')",message=\""${ESC_MSG}"\"
    RET=$?
fi

if [[ $RET -ne 0 ]]; then
    echo "'aws logs put-log-events' command failed."
    exit 1
fi

# Message when log transmission succeeds.
printf "\nThe message \"%s\" was successfully sent!\n" "${MSG}"
printf "  log gourp  : %s\n" "${LOG_GROUP_NAME}"
printf "  log stream : %s\n" "${LOG_STREAM}"

exit 0