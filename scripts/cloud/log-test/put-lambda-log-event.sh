#!/usr/bin/env bash

CMDNAME=$(basename "$0")
USAGE="usage: ${CMDNAME} \"basename of log group name\" \"message\" "
TYPE="lambda"

# capture piped data and arguments
if [[ -p /dev/stdin ]]; then
    INPUT=$(cat)
    printf "Using message recieved by pipe.\n\n"
    if [[ $# -eq 0 ]]; then
        printf "'log base name' is required.\n"
        printf "%s\n" "${USAGE}"
        exit 1
    elif [[ $# -eq 1 ]]; then
        LG_BASENAME=${1}
    fi
else
    if [[ $# -eq 2 ]]; then
        LG_BASENAME=${1}
        INPUT=${2}
    else
        printf "Error: Missing or too many arguments\n"
        printf "%s\n" "${USAGE}"
        exit 1
    fi 
fi

./put-log-events2cw.sh -t "${TYPE}" -l "${LG_BASENAME}" "${INPUT}"