#!/bin/bash -e

BASE=$(cd $(dirname "$0") || exit; pwd)
source "$BASE"/../../bin/common.sh

AWS_REGION=$1

if [[ -z "${AWS_REGION}" ]]; then
  echo "error: please set aws region name (e.g. ap-northeast-1)" 1>&2
  exit 1
fi

export AWS_REGION

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)

echo -e "AWS_PROFILE: $AWS_PROFILE"
echo -e "AWS_ACCOUNT_ID: $AWS_ACCOUNT_ID"
echo -e "AWS_REGION: $AWS_REGION"

choice "Do you continue ?"

# 対象リージョンのバケットを取得
buckets=()
for bucket in $(aws s3api list-buckets --query 'Buckets[?ends_with(Name, `-alb-log`)].Name' --output text); do
  location=$(aws s3api get-bucket-location --bucket $bucket | jq '.LocationConstraint' | sed 's/null/us-east-1/g' | sed 's/"//g')
  if [[ "${location}" != "${AWS_REGION}" ]]; then
    continue
  fi
  buckets+=("${bucket}")
done

echo -e "buckets total: ${#buckets[@]}\n"

# Lambda関数(NewRelic-s3-log-ingestion)のARNを取得
lambda_arn=$(aws lambda get-function \
  --function-name NewRelic-s3-log-ingestion \
  --query 'Configuration.FunctionArn' \
  --output text)
lambda_name=$(aws lambda get-function \
  --function-name NewRelic-s3-log-ingestion \
  --query 'Configuration.FunctionName' \
  --output text)

echo -e "LAMBDA_ARN: ${lambda_arn}\n"

# S3バケットにLambda関数をトリガーする設定のためのJSONを生成
echo '{
  "LambdaFunctionConfigurations": [
    {
      "LambdaFunctionArn": "'${lambda_arn}'",
      "Events": ["s3:ObjectCreated:*"]
    }
  ],
  "EventBridgeConfiguration": {}
}' > notification.json

for bucket in ${buckets[@]}; do
  echo -e "BUCKET:\t${bucket}"
  aws s3api put-bucket-notification-configuration \
    --bucket $bucket \
    --notification-configuration \
    file://notification.json
  aws s3api get-bucket-notification-configuration \
    --bucket $bucket

  # Lambda関数のステートメントポリシーを取得

  policy=$(aws lambda get-policy \
    --function-name ${lambda_name} \
    | jq -r '.Policy')

  # SourceArnをローカル変数に格納
  source_arn="arn:aws:s3:::${bucket}"

  # 既存ステートメントポリシーのSourceArnを抽出
  existing_sourceArn=$(echo ${policy} | \
    jq -r '.Statement[] | select(.Condition.ArnLike["AWS:SourceArn"] == "'${source_arn}'") | .Condition.ArnLike["AWS:SourceArn"]')
  # 既存ステートメントポリシーのSourceArnがある場合は更新をスキップする
  if [[ -n "${existing_sourceArn}" ]]; then
    echo "skip: permission already exists on lambda."
    continue
  fi

  # Lambda関数にS3からのトリガーを許可するための権限を追加
  sid=$(uuidgen | tr 'A-Z' 'a-z')
  aws lambda add-permission \
    --function-name ${lambda_name} \
    --principal s3.amazonaws.com \
    --statement-id lambda-${sid} \
    --action "lambda:InvokeFunction" \
    --source-arn ${source_arn} \
    --source-account ${AWS_ACCOUNT_ID} \
    | jq -r '.Statement' | jq
  echo ""
done

rm notification.json
