#!/bin/bash -e

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)
source "$BASE"/../../bin/common.sh

AWS_REGION=$1

if [[ -z "${AWS_REGION}" ]]; then
  echo "error: please set aws region name (e.g. ap-northeast-1)" 1>&2
  exit 1
fi

export AWS_REGION

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)

echo -e "AWS_PROFILE: $AWS_PROFILE"
echo -e "AWS_ACCOUNT_ID: $AWS_ACCOUNT_ID"
echo -e "AWS_REGION: $AWS_REGION"

choice "Do you continue ?"

# 対象のロググループ名を配列に格納
log_group_patterns=" \
/fargate/core \
/fargate/bcclient \
/fargate/bcclient-stream \
/fargate/bcmonitoring \
/fargate/bctracker-balance \
/fargate/bctracker-transaction \
/fargate/invoke-core-tracker \
batch-file-comparator \
/aws/lambda/bcmonitoring-stream \
/fargate/bpmserver \
/fargate/bctracker-email-sender \
nft-list-bat \
nft-mint-bat \
nft-tfr-bat \
nft-tfr-exe-bat \
nft-tfr-pre-bat \
/fargate/signer \
/fargate/bpmserver \
/fargate/bank-gateway \
/fargate/bctracker-push-notification \
batch-his-aggre \
batch-his-sort \
batch-his-sender \
batch-acct-bal-export \
batch-acct-bal-send \
batch-acct-bal-sort \
nft-autochg-bat \
/fargate/relayer \
"

# Lambda関数(NewRelic-log-ingestion-XXXXX)のARNを取得
lambda_arn=$(aws lambda list-functions \
  --query "Functions[?starts_with(FunctionName, 'newrelic-log-ingestion-')].FunctionArn" \
  --output text)
lambda_name=$(aws lambda list-functions \
  --query "Functions[?starts_with(FunctionName, 'newrelic-log-ingestion-')].FunctionName" \
  --output text)

echo -e "LAMBDA_ARN: ${lambda_arn}\n"

for pattern in $log_group_patterns; do
  # ロググループの存在確認
  log_group_name=$(aws logs describe-log-groups \
    --log-group-name-pattern "${pattern}" |
    jq -r ".logGroups[] | select(.logGroupName | endswith(\"${pattern}\")) | .logGroupName")

  # ロググループが存在しない場合、後続の処理をスキップ
  if [[ -z "${log_group_name}" ]]; then
    echo "Log group ${pattern} does not exist. Skipping..."
    continue
  fi

  echo "LOG_GROUP_NAME: ${log_group_name}"

  # ロググループのArn
  log_group_arn=$(aws logs describe-log-groups \
    --log-group-name-pattern "${pattern}" |
    jq -r ".logGroups[] | select(.logGroupName | endswith(\"${pattern}\")) | .arn")

  # フィルタを上書きする
  aws logs put-subscription-filter \
    --log-group-name ${log_group_name} \
    --filter-name newrelic \
    --filter-pattern "" \
    --destination-arn ${lambda_arn}
  aws logs describe-subscription-filters \
    --log-group-name ${log_group_name} |
    jq '.subscriptionFilters[] | select(.filterName == "newrelic")'

  # Lambda関数のステートメントポリシーを取得
  policy=$(aws lambda get-policy \
    --function-name ${lambda_name} |
    jq -r '.Policy')

  # 既存ステートメントポリシーのSourceArnを抽出
  existing_sourceArn=$(echo ${policy} |
    jq -r '.Statement[] | select(.Condition.ArnLike["AWS:SourceArn"] == "'${log_group_arn}'") | .Condition.ArnLike["AWS:SourceArn"]')
  # 既存ステートメントポリシーのSourceArnがある場合は更新をスキップする
  if [[ -n "${existing_sourceArn}" ]]; then
    echo "skip: permission already exists on lambda."
    continue
  fi

  # Lambda関数にCloudWatch Logsからのトリガーを許可するための権限を追加
  sid=$(uuidgen | tr 'A-Z' 'a-z')
  aws lambda add-permission \
    --function-name ${lambda_name} \
    --principal logs.amazonaws.com \
    --statement-id labmda-${sid} \
    --action "lambda:InvokeFunction" \
    --source-arn ${log_group_arn} \
    --source-account ${AWS_ACCOUNT_ID} |
    jq -r '.Statement' | jq
  echo ""
done
