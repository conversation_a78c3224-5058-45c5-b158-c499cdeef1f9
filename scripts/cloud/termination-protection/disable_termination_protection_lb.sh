#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/common_function.sh

#
# 変数
#
CONTAINS=""
NOT_CONTAINS=""

LOAD_BALANCER_LIST=()

SLEEP_TIME=5
MAX_SLEEP_NUM=5

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to disable termination protection for AWS Elastic Load Balancing.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    [-c <value>]
    [-n <value>]

  OPTIONS
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.

  EXAMPLES
    Example 1: To disable termination protection for load balancers which contain "validator" and "shared".
      $0 -c validator,shared
----------------------------------------------------------------
EOF
}

#
# 対象の確認
#
function CHECK_TARGET () {
  COMMAND_DESCRIBE_LOAD_BALANCERS="aws elbv2 describe-load-balancers --query \"LoadBalancers[].{LoadBalancerArn:LoadBalancerArn}\""

  COMMAND_SELECT_CONTAIN=""
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({LoadBalancerArn: \"${CONTAIN}\"})) | "
    done
  fi

  COMMAND_SELECT_NOT_CONTAIN=""
  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({LoadBalancerArn: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi

  echo "## Target load balancers"
  echo "\$ ${COMMAND_DESCRIBE_LOAD_BALANCERS} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .LoadBalancerArn'"
  echo ""
  echo "${COMMAND_DESCRIBE_LOAD_BALANCERS} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .LoadBalancerArn'" | bash

  # ロードバランサーのリストを控えておく
  LOAD_BALANCER_LIST=($(echo "${COMMAND_DESCRIBE_LOAD_BALANCERS} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .LoadBalancerArn'" | bash))

  # 対象がなければ終了
  if [ ${#LOAD_BALANCER_LIST[@]} -eq 0 ]; then
    echo "Target does not exist."
    exit 0
  fi
}

#
# 終了保護の無効化
#
function DISABLE_TERMINATION_PROTECTION () {
  # 終了保護の無効化
  echo "## Disabling termination protection...."
  for LOAD_BALANCER in "${LOAD_BALANCER_LIST[@]}"
  do
    aws elbv2 modify-load-balancer-attributes --load-balancer-arn ${LOAD_BALANCER} --attributes Key=deletion_protection.enabled,Value=false --output json | jq -r '.Attributes[] | select(.Key == "deletion_protection.enabled")'
  done
  echo "Requested to disable termination protection."
  echo ""

  # 無効化されたことを確認
  echo "## Checking whether it is disabled or not...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    FLAG_OK=0

    for LOAD_BALANCER in "${LOAD_BALANCER_LIST[@]}"
    do
      TERMINATION_VALUE=$(
        aws elbv2 describe-load-balancer-attributes \
          --output json \
          --load-balancer-arn ${LOAD_BALANCER} \
          --query "Attributes[]" | jq -r '.[] | select(.Key == "deletion_protection.enabled") | .Value')
      echo -n "${LOAD_BALANCER}: "
      # Falseであること
      if [[ "${TERMINATION_VALUE}" == "false" ]]; then
        echo "Disabled"
      else
        echo "Enabled"
        FLAG_OK=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_OK}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Checking termination protection value is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# Main
#

# 引数を変数に代入
while getopts hc:n: OPT
do
  case $OPT in
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target load balancers"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
DISABLE_TERMINATION_PROTECTION

echo ""
echo "Completed $0."
