#!/bin/bash -eu

#
# 共通関数の読み込み
#
source "${0%/*}"/common_function.sh

#
# 変数
#
CONTAINS=""
NOT_CONTAINS=""

DB_CLUSTER_LIST=()

SLEEP_TIME=5
MAX_SLEEP_NUM=5

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is to disable termination protection for AWS db cluster.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    [-c <value>]
    [-n <value>]

  OPTIONS
    -c (string)
        Specify contained name separated by commas.
    -n (string)
        Specify not contained name separated by commas.

  EXAMPLES
    Example 1: To disable termination protection for db cluster which contain "validator" and "shared".
      $0 -c validator,shared
----------------------------------------------------------------
EOF
}

#
# 対象の確認
#
function CHECK_TARGET () {
  COMMAND_DESCRIBE_DB_CLUSTERS="aws rds describe-db-clusters --query \"DBClusters[].{DBClusterIdentifier:DBClusterIdentifier}\""

  COMMAND_SELECT_CONTAIN=""
  if [[ "${CONTAINS}" != "" ]]; then
    for CONTAIN in $(echo "${CONTAINS//,/ }")
    do
      COMMAND_SELECT_CONTAIN+="select(contains({DBClusterIdentifier: \"${CONTAIN}\"})) | "
    done
  fi

  COMMAND_SELECT_NOT_CONTAIN=""
  if [[ "${NOT_CONTAINS}" != "" ]]; then
    for NOT_CONTAIN in $(echo "${NOT_CONTAINS//,/ }")
    do
      COMMAND_SELECT_NOT_CONTAIN+="select(contains({DBClusterIdentifier: \"${NOT_CONTAIN}\"})|not) | "
    done
  fi

  echo "## Target db cluster"
  echo "\$ ${COMMAND_DESCRIBE_DB_CLUSTERS} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .DBClusterIdentifier'"
  echo ""
  echo "${COMMAND_DESCRIBE_DB_CLUSTERS} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .DBClusterIdentifier'" | bash

  # DBクラスターリストを控えておく
  DB_CLUSTER_LIST=($(echo "${COMMAND_DESCRIBE_DB_CLUSTERS} | jq -r '.[] | ${COMMAND_SELECT_CONTAIN} ${COMMAND_SELECT_NOT_CONTAIN} .DBClusterIdentifier'" | bash))

  # 対象がなければ終了
  if [ ${#DB_CLUSTER_LIST[@]} -eq 0 ]; then
    echo "Target does not exist."
    exit 0
  fi
}

#
# 終了保護の無効化
#
function DISABLE_TERMINATION_PROTECTION () {
  # 終了保護の無効化
  echo "## Disabling termination protection...."
  for DB_CLUSTER in "${DB_CLUSTER_LIST[@]}"
  do
    aws rds modify-db-cluster --db-cluster-identifier ${DB_CLUSTER} --no-deletion-protection --output json | jq -r '.[] | { DBClusterIdentifier: .DBClusterIdentifier, DeletionProtection: .DeletionProtection}'
  done
  echo "Requested to disable termination protection."
  echo ""

  # 無効化されたことを確認
  echo "## Checking whether it is disabled or not...."
  for SLEEP_NUM in $(seq 1 ${MAX_SLEEP_NUM})
  do
    FLAG_OK=0

    for DB_CLUSTER in "${DB_CLUSTER_LIST[@]}"
    do
      TERMINATION_VALUE=$(
        aws rds describe-db-clusters \
          --output text \
          --db-cluster-identifier ${DB_CLUSTER} \
          --query "DBClusters[].{DeletionProtection:DeletionProtection}")
      echo -n "${DB_CLUSTER}: "
      # Falseであること
      if [[ "${TERMINATION_VALUE}" == "False" ]]; then
        echo "Disabled"
      else
        echo "Enabled"
        FLAG_OK=1
      fi
    done

    # 動作に問題があれば規定回数繰り返す
    if [[ "${FLAG_OK}" == "0" ]]; then
      echo "Finished checking."
      echo "RESULT: OK"
      echo ""
      break
    else
      if [[ "${SLEEP_NUM}" == "${MAX_SLEEP_NUM}" ]]; then
        echo "ERROR: Checking termination protection value is failed. Please check the status. Stop the script."
        echo "RESULT: NG"
        echo ""
        exit 1
      fi
      echo "Waiting for ${SLEEP_TIME} seconds. (${SLEEP_NUM}/${MAX_SLEEP_NUM})"
      sleep ${SLEEP_TIME}
    fi
  done
}

#
# Main
#

# 引数を変数に代入
while getopts hc:n: OPT
do
  case $OPT in
    c) CONTAINS=$OPTARG;;
    n) NOT_CONTAINS=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# IAMユーザ、Roleの確認
echo ""
echo "## Check your IAM user and role"
echo "----------------------------------------------------------------"
aws sts get-caller-identity
echo "----------------------------------------------------------------"

# 対象の確認
echo ""
echo "## Check target db cluster"
echo "----------------------------------------------------------------"
CHECK_TARGET
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION
echo ""

# 実行
DISABLE_TERMINATION_PROTECTION

echo ""
echo "Completed $0."
