#!/bin/bash -eu
#
# CloudFormationのStackSetsの設定状況を確認する
#

# 一つでもエラーがあればスクリプト失敗とする
FAIL_COUNT=0

#
# IAMロールの設定状況を確認する
# 引数
#   $1: (ロール名)
#   $2: NORMAL or PROD (所属組織)
# 返値
#   YES (設定あり) or - (設定なし)
function CHECK_IAM_ROLE () {
  ROLE_JSON="$(aws iam get-role --role-name $1 2> /dev/null)"

  if [[ "${ROLE_JSON}" != "" ]]; then
    # IP許可設定があるか確認する (値がなければjqはnullを返す)
    IP_SETTING="$(echo "${ROLE_JSON}" | jq '.Role.AssumeRolePolicyDocument.Statement[].Condition.IpAddress."aws:SourceIp"' 2> /dev/null)"
    # 所属組織がPRODではない場合 (IP許可設定がないことを確認する)
    if [[ "$2" == "NORMAL" && "${IP_SETTING}" == "null" ]]; then
      echo "YES"
      return 0
    # 所属組織がPRODの場合 (IP許可設定があることを確認する)
    elif [[ "$2" == "PROD" && "${IP_SETTING}" != "null" ]]; then
      echo "YES"
      return 0
    else
      echo "-"
      return 2
    fi
  else
    echo "-"
    return 2
  fi
}

#
# IAMグループの設定状況を確認する
# 引数
#   $1: (グループ名)
# 返値
#   YES (設定あり) or - (設定なし)
function CHECK_IAM_GROUP () {
  GROUP_JSON="$(aws iam get-group --group-name $1 2> /dev/null)"

  if [[ "${GROUP_JSON}" != "" ]]; then
    echo "YES"
    return 0
  else
    echo "-"
    return 2
  fi
}

#
# Lambda関数の設定状況を確認する
# 引数
#   $1: (Lambda関数名)
# 返値
#   YES (設定あり) or - (設定なし)
function CHECK_LAMBDA_FUNCTION () {
  LAMBDA_JSON="$(aws lambda list-functions 2> /dev/null)"

  if [[ "${LAMBDA_JSON}" != "" ]]; then
    # 対象のLambda関数があるか確認する (jqのtest構文を使用する)
    TARGET_LAMBDA_SETTING="$(echo "${LAMBDA_JSON}" | jq ".Functions[] | select( .FunctionName | test(\"$1\") )" 2> /dev/null)"

    if [[ "${TARGET_LAMBDA_SETTING}" != "" ]]; then
      echo "YES"
      return 0
    else
      echo "-"
      return 2
    fi
  else
    echo "-"
    return 2
  fi
}

#
# GuardDutyの設定状況を確認する
# 返値
#   YES (設定あり) or - (設定なし)
function CHECK_GUARD_DUTY () {
  GUARD_DUTY_DETECTOR_JSON="$(aws guardduty list-detectors 2> /dev/null)"

  if [[ "${GUARD_DUTY_DETECTOR_JSON}" != "" ]]; then
    # GuardDutyのディテクター設定があるか確認する
    DETECTOR_SETTING="$(echo "${GUARD_DUTY_DETECTOR_JSON}" | jq ".DetectorIds[]" 2> /dev/null)"

    if [[ "${DETECTOR_SETTING}" != "" ]]; then
      echo "YES"
      return 0
    else
      echo "-"
      return 2
    fi
  else
    echo "-"
    return 2
  fi
}

#
# Main
#
echo "This script is check CloudFormation StackSets setting."
echo "  ('YES': Setting exists. '-': Setting does not exist.)"
echo ""

echo -n "Github-Actions-Role: "
CHECK_IAM_ROLE "GithubActionsOIDCRole" "NORMAL" || ((FAIL_COUNT++))

## AWS-Health-To-Slack Lambda関数、GuardDuty, AWS Configの設定状況の確認
echo -n "AWS-Health-To-Slack: "
CHECK_LAMBDA_FUNCTION "AWS-Health-To-Slack" || ((FAIL_COUNT++))
echo -n "Enable-GuardDuty: "
CHECK_GUARD_DUTY || ((FAIL_COUNT++))

echo ""
echo "FAIL_COUNT: $FAIL_COUNT"
if [[ $FAIL_COUNT -eq 0 ]];then
  exit 0
else
  exit 2
fi
