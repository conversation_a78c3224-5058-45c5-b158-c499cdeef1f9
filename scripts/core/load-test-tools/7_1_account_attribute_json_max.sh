#!/bin/bash
# JSONの最大バイト数でアカウント属性更新、アカウント取得を確認する
# アカウント属性は約1GBのデータを作成する
# 引数1: アカウントID

source script_test_common.env
DUMMY_FILE_NAME=dummy_1mb.txt

ACCOUNT_ID=$1
if [ -z $ACCOUNT_ID ]; then
  echo "ACCOUNT_ID is null"
  exit 1
fi

ACCESS_TOKEN=$(curl -Ss -u $CLIENT_ID:$CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
echo $ACCESS_TOKEN

# JSON作成
# 約1M * 1000行のattribute用データを作成する
DUMMY_DATA=$(<$DUMMY_FILE_NAME)

REQUEST_BODY='{"data":{'
for i in `seq 1 999`
do
  REQUEST_BODY+='"attr'$i'":"'$DUMMY_DATA'",'
done
# 最後はカンマ不要
REQUEST_BODY+='"attr1000":"'$DUMMY_DATA'"'
REQUEST_BODY+='}}'

# アカウント属性更新
# attributeをログに出さないようにする
ACCOUNT_ID=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $ACCESS_TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY" | jq -r ".account_id")
# [ -z $ISSUER_ID ]では判定できない
if [ $ACCOUNT_ID = null ]; then
  echo "アカウント属性更新が失敗しました"
  exit 1
else
  echo "アカウント属性更新が成功しました"
fi

# アカウント属性取得
ACCOUNT_ID=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $ACCESS_TOKEN" -H "Content-Type: application/json" | jq -r ".account_id")
if [ $ACCOUNT_ID = null ]; then
  echo "アカウント属性取得が失敗しました"
  exit 1
else
  echo "アカウント属性取得が成功しました"
fi

exit 0