#!/bin/bash
# 発行者アイデンティティを複数スレッドで実行する
# 引数1: スレッド数

source script_test_common.env

THREAD=$1
if [ -z $THREAD ]; then
  THREAD=1
fi

postIssuerAndPostIdentities () {
  # 発行者作成
  REQUEST_BODY='{"name":"issuer"}'
  RESPONSE=$(curl -Ss -X POST $1/issuers -H "Authorization: $2" -H "Content-Type: application/json" -d "$REQUEST_BODY")
  echo "発行者作成結果:"$RESPONSE
  ISSUER_ID=$(echo $RESPONSE | jq -r ".issuer_id")
  # [ -z $ISSUER_ID ]では判定できない
  if [ $ISSUER_ID = null ]; then
    echo "発行者作成が失敗しました"
    exit 1
  fi

  # 発行者アイデンティティ作成
  RESPONSE=$(curl -Ss -X POST $1/identities/issuers/$ISSUER_ID -H "Authorization: $2" -H "Content-Type: application/json")
  echo "発行者アイデンティティ作成結果:"$RESPONSE
  ISSUER_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
  ISSUER_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
  if [ $ISSUER_CLIENT_ID = null ]; then
    echo "発行者アイデンティティ作成が失敗しました"
  fi
}

echo $CLIENT_ID
ACCESS_TOKEN=$(curl -Ss -u $CLIENT_ID:$CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
echo $ACCESS_TOKEN

for i in `seq 1 $THREAD`
do
  postIssuerAndPostIdentities $BASE_URL $ACCESS_TOKEN &
done

exit 0
