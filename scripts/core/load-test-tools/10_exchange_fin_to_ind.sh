#!/bin/bash
# 共通領域から付加領域へのexchangeを実行する
# script_test_common.envに下記が必要
#   BASE_URL: 接続URL
#   CLIENT_ID: ISSUERのCLIENT_ID
#   CLIENT_SECRET: ISSUERのCLIENT_SECRET
#   TOKEN_URL: アクセストークン作成のURL
#   ACCOUNT_ID_LIST: 付加領域同期済みのアカウントID（,で結合）
#   SIGNATURE_TOOLS: SIGNATURE_TOOLSのパス

source script_test_common.env

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# exchangeの実行と残高の確認
# 引数1 BASE_URL
# 引数2 ACCESS_TOKEN
# 引数3 アカウントID
# 引数4 ループの回数（exchangeの金額にも使用）
checkUpdateIndustryBalance () {
  ACCOUNT_ID=$3
  LOOP=$4
  TO_REGION_ID="3001"

  START_TIME=$(date +%s)
  echo "start: timestamp: $(date +%s) account_id: $ACCOUNT_ID loop: $LOOP"

  # ワンタイムキー取得
  ONETIME=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID/security -H "Authorization: $2" -H "Content-Type: application/json")
  SK_O1=$(echo "$ONETIME" | jq -r ".sk_o")
  INFO1=$(echo "$ONETIME" | jq -r ".info")

  # TODO 応急処置 2.13リリースで不要となる
  SK_O1="0000000000000000000000000000000000000000000000000000000000000000$SK_O1"
  SK_O1=${SK_O1: -64}
  # echo "SK_O1:$SK_O1"

  # 転送用アカウント署名作成
  ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/exchange_signature.sh "$SK_O1" "$ACCOUNT_ID" "$TO_REGION_ID" $LOOP | grep "account signature:" | sed -r 's/^account signature: //')

  # exchange前のbalanceを取得
  BEFORE_BALANCE=$(curl -Ss -X GET $1/accounts/$ACCOUNT_ID/balances -H "Authorization: $2" -H "Content-Type: application/json" | jq  '.items[] | select(.region_id == "'$TO_REGION_ID'").balance')

  # exchange
  REQUEST_BODY='{"account_id":"'$ACCOUNT_ID'","to_region_id":"'$TO_REGION_ID'","exchange_amount":'$LOOP',"account_signature":"'$ACCOUNT_SIGNATURE'","info":"'$INFO1'"}'
  EXCHANGE_RES=$(curl -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $ACCESS_TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY")
  # echo "EXCHANGE_RES:$EXCHANGE_RES"
  TX_HASH=$(echo $EXCHANGE_RES | jq -r ".transaction_hash")

  EXCHANGE_END_TIME=`expr $(date +%s) - $START_TIME`
  echo "end exchange: timestamp: $(date +%s) elapsed: $EXCHANGE_END_TIME account_id: $ACCOUNT_ID loop: $LOOP tx_hash: $TX_HASH exchange_amount: $LOOP"

  # exchange後のbalanceを取得
  AFTER_BALANCE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID/balances -H "Authorization: $ACCESS_TOKEN" -H "Content-Type: application/json" | jq '.items[] | select(.region_id == "'$TO_REGION_ID'").balance')

  # balanceが更新されていない場合、0.5秒スリープしてからbalanceを再取得
  j=0
  while [ $BEFORE_BALANCE -eq $AFTER_BALANCE ]
  do
    sleep 0.5
    AFTER_BALANCE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID/balances -H "Authorization: $ACCESS_TOKEN" -H "Content-Type: application/json" | jq '.items[] | select(.region_id == "'$TO_REGION_ID'").balance')

    j=`expr 1 + $j`
    # 一定時間がすぎて更新されない場合は処理終了
    if [ $j -gt 30 ]; then
      echo "not updated account_id: $ACCOUNT_ID loop: $LOOP"
      exit 1
    fi
  done

  UPDATED_IND_TIME=`expr $(date +%s) - $START_TIME`
  echo "updated industry: timestamp: $(date +%s) elapsed: $UPDATED_IND_TIME account_id: $ACCOUNT_ID loop: $i"
}

# exchange処理の呼び出し
# 引数1 BASE_URL
# 引数2 ACCESS_TOKEN
# 引数3 アカウントID
callCheckUpdateIndustryBalance (){
  LOOP=10
  for i in `seq 1 $LOOP`
  do
    checkUpdateIndustryBalance $1 $2 $3 $i
  done
}

declare -a ARRAY_ACCOUNT_ID=(${ACCOUNT_ID_LIST//,/ })
ACCESS_TOKEN=$(curl -Ss -u $CLIENT_ID:$CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')

for id in ${ARRAY_ACCOUNT_ID[@]}
do
  # アカウントIDリストの数だけ同時実行
  callCheckUpdateIndustryBalance $BASE_URL $ACCESS_TOKEN $id &
done

exit 0
