#!/bin/bash

source ./script_test_common.env

source ./account_info_fin.env

declare -a ARRAY_ACCOUNT_ID=(${ACCOUNT_ID_LIST//,/ })
declare -a ARRAY_SK_O=(${SK_O_LIST//,/ })
declare -a ARRAY_INFO=(${INFO_LIST//,/ })

I_ACCESS_TOKEN=$(curl -Ss -u ${CLIENT_ID}:${CLIENT_SECRET} -d grant_type=client_credentials ${TOKEN_URL} | jq -r '.access_token')

for ((i=0; i<${#ARRAY_ACCOUNT_ID[@]}; i++))
do
    SK_O=${ARRAY_SK_O[i]} &&
    INFO=${ARRAY_INFO[i]} &&
    ACCOUNT_ID=${ARRAY_ACCOUNT_ID[i]} &&
    curl -Ss -X POST ${BASE_URL}/accounts/${ACCOUNT_ID}/industry -H "Authorization: ${I_ACCESS_TOKEN}" -H "Content-Type: application/json" -d "{\"region_id\":$TO_REGION_ID}" &&
    echo ""
    sleep 6
    EXCHANGE_ACCOUNT_SIGNATURE=$($SIGNATURE_TOOLS/tools/exchange_signature.sh $SK_O $ACCOUNT_ID $TO_REGION_ID 5000 | grep "account signature:" | sed -r 's/^account signature: //')
    curl -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID\",\"to_region_id\": $TO_REGION_ID,\"exchange_amount\": 5000, \"account_signature\": \"$EXCHANGE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}"
    echo ""

done
exit 0