#!/bin/bash
# バリデータ作成に継続的に負荷をかけるシェル
# script_test_common.envに下記が必要
#   JMETER_DIRECTORY: 実行するファイルの置き場所（docker側のパス） (/tmp)
#   LOG_FILE: ログファイルの出力パス（docker側のパス） (/tmp/jmeter.log)
#   RESULT_FILE: 結果ファイルの出力パス（docker側のパス） (/tmp/result.csv)
#   BASE_URL:
#   CLIENT_ID:
#   CLIENT_SECRET:
#   TOKEN_URL:
#   CONTINUOUS_TIME: 負荷をかける時間（秒）
# 引数1: 発行者ID

source script_test_common.env

# jmeterの定義ファイル
JMETER_FILE=$JMETER_DIRECTORY"/9_post-validators-variable.jmx"
# 処理を実行する際のスレッド数
THREAD=20

ISSUER_ID=$1
if [ -z $ISSUER_ID ]; then
  echo "ISSUER_ID is null"
  exit 1
fi

ACCESS_TOKEN=$(curl -Ss -u $CLIENT_ID:$CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')

START_TIME=`date +%s`
echo "Start: "`date -r $START_TIME`

RUNNING_TIME=0
i=1

while [ $RUNNING_TIME -lt $CONTINUOUS_TIME ]
do
  echo $i"回目"
  docker exec -ti jmeter_master /bin/bash /jmeter/apache-jmeter-3.3/bin/jmeter -n -t $JMETER_FILE -JACCESS_TOKEN=$ACCESS_TOKEN -JBASE_URL=$BASE_URL -j $LOG_FILE -l $RESULT_FILE -JTHREAD=$THREAD -JISSUER_ID=$ISSUER_ID
  wait
  RUNNING_TIME=`expr $(date +%s) - $START_TIME`
  echo "Running: "$RUNNING_TIME" seconds"
  i=`expr 1 + $i`
done

exit 0