#!/bin/bash
# バリデータアイデンティティを複数スレッドで実行する
# 引数1: スレッド数

source script_test_common.env

THREAD=$1
if [ -z $THREAD ]; then
  THREAD=1
fi

postValidatorAndPostIdentities () {
  echo $3 "start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
  # バリデータ作成
  REQUEST_BODY='{"name":"validator"}'
  RESPONSE=$(curl -Ss -X POST $1/validators -H "Authorization: $2" -H "Content-Type: application/json" -d "$REQUEST_BODY")
  echo $3 "バリデータ作成結果:"$RESPONSE
  VALIDATOR_ID=$(echo $RESPONSE | jq -r ".validator_id")
  if [ $VALIDATOR_ID = null ]; then
    echo "バリデータ作成が失敗しました"
    exit 1
  fi

  # バリデータアイデンティティ作成
  RESPONSE=$(curl -Ss -X POST $1/identities/validators/$VALIDATOR_ID -H "Authorization: $2" -H "Content-Type: application/json" -d '{}')
  echo $3 "バリデータアイデンティティ作成結果:"$RESPONSE
  VALIDATOR_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
  VALIDATOR_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
  if [ $VALIDATOR_CLIENT_ID = null ]; then
    echo "バリデータアイデンティティ作成が失敗しました"
  fi
  echo $3 "バリデータアイデンティティ作成が成功しました validator_id: $VALIDATOR_ID"
  echo $3 "end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
}

ACCESS_TOKEN=$(curl -Ss -u $CLIENT_ID:$CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')

for i in `seq 1 $THREAD`
do
  postValidatorAndPostIdentities $BASE_URL $ACCESS_TOKEN $i &
  sleep 0.1
done

exit 0