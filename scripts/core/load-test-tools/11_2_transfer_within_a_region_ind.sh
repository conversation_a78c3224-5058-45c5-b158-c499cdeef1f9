#!/bin/bash
# 領域内でのtransferを実行する
# script_test_common.envに下記が必要
#   BASE_URL: 接続URL
#   CLIENT_ID: ISSUERのCLIENT_ID
#   CLIENT_SECRET: ISSUERのCLIENT_SECRET
#   TOKEN_URL: アクセストークン作成のURL
#   FROM_ACCOUNT_ID_LIST: 送金元アカウント
#   TO_ACCOUNT_ID_LIST: 送金先アカウント(送金元アカウントと同数）
#   SK_O: 共通領域で作成したSK_O
#   INFO: 共通領域で作成したINFO
#   SIGNATURE_TOOLS: SIGNATURE_TOOLSのパス

source script_test_common_ind.env

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# transferの実行
# 引数1 BASE_URL
# 引数2 ACCESS_TOKEN
# 引数3 送金元アカウントID
# 引数4 送金先アカウントID
# 引数5 共通領域で作成したSK_O
# 引数6 共通領域で作成したINFO
# 引数7 ループの回数（transferの金額にも使用）
execTransfer () {
  FROM_ACCOUNT_ID=$3
  SEND_ACCOUNT_ID=$FROM_ACCOUNT_ID
  TO_ACCOUNT_ID=$4
  SK_O=$5
  INFO=$6
  LOOP=$7

  # transfer用アカウント署名作成
  ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/transfer_signature.sh "$SK_O" "$SEND_ACCOUNT_ID" "$FROM_ACCOUNT_ID" "$TO_ACCOUNT_ID" $LOOP | grep "account signature:" | sed -r 's/^account signature: //')

  START_TIME=$(date +%s.%3N)
  echo "start: timestamp: $START_TIME from_account_id: $FROM_ACCOUNT_ID loop: $LOOP"

  # transfer
  REQUEST_BODY='{"send_account_id":"'$SEND_ACCOUNT_ID'","from_account_id":"'$FROM_ACCOUNT_ID'","to_account_id":"'$TO_ACCOUNT_ID'","transfer_amount":'$LOOP',"account_signature":"'$ACCOUNT_SIGNATURE'","info":"'$INFO'"}'
  TRANSFER_RES=$(curl -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization: $ACCESS_TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY")
  TX_HASH=$(echo $TRANSFER_RES | jq -r ".transaction_hash")

  END_TIME=$(date +%s.%3N)
  TRANSFER_END_TIME=$(echo "$END_TIME - $START_TIME" | bc)
  echo "end transfer: timestamp: $END_TIME elapsed: $TRANSFER_END_TIME from_account_id: $FROM_ACCOUNT_ID loop: $LOOP tx_hash: $TX_HASH transfer_amount: $LOOP"
}

# transfer処理の呼び出し
# 引数1 BASE_URL
# 引数2 ACCESS_TOKEN
# 引数3 送金元アカウントID
# 引数4 送金先アカウントID
# 引数5 共通領域で作成したSK_O
# 引数6 共通領域で作成したINFO
callTransfer (){
  LOOP=10
  for i in $(seq 1 $LOOP)
  do
    execTransfer $1 $2 $3 $4 $5 $6 $i
  done
}

declare -a ARRAY_FROM_ACCOUNT_ID=(${FROM_ACCOUNT_ID_LIST//,/ })
declare -a ARRAY_TO_ACCOUNT_ID=(${TO_ACCOUNT_ID_LIST//,/ })
declare -a ARRAY_SK_O=(${SK_O_LIST//,/ })
declare -a ARRAY_INFO=(${INFO_LIST//,/ })

if [ ${#ARRAY_FROM_ACCOUNT_ID[*]} -ne ${#ARRAY_TO_ACCOUNT_ID[*]} ]; then
  echo "ERROR:The number of accounts in FROM_ACCOUNT_ID_LIST and TO_ACCOUNT_ID_LIST should be equal."
  exit 1
fi

ACCESS_TOKEN=$(curl -Ss -u $CLIENT_ID:$CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')

# 送金元アカウント数取得
NUM_OF_FROM_ACCOUNTS=${#ARRAY_FROM_ACCOUNT_ID[*]}

for ((i=0; i<$NUM_OF_FROM_ACCOUNTS; i++))
do
  # 送金元アカウントの数だけ同時実行
  callTransfer $BASE_URL $ACCESS_TOKEN ${ARRAY_FROM_ACCOUNT_ID[$i]} ${ARRAY_TO_ACCOUNT_ID[$i]} ${ARRAY_SK_O[$i]} ${ARRAY_INFO[$i]} &
done

exit 0