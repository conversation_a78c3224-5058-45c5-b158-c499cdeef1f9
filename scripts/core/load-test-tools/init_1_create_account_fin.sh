#!/bin/bash

source ./script_test_common.env

cp /dev/null account_info_fin.env

ACCOUNT_ID_LIST=()
INFO_LIST=()
SK_O_LIST=()

for i in $(seq 1 $1)
do
    I_ACCESS_TOKEN=$(curl -Ss -u ${CLIENT_ID}:${CLIENT_SECRET} -d grant_type=client_credentials ${TOKEN_URL} | jq -r '.access_token') &&
    ACCOUNT_ID=$(curl -Ss -X POST ${BASE_URL}/accounts -H "Authorization: ${I_ACCESS_TOKEN}" -H "Content-Type: application/json" | jq -r ".account_id") &&
    ACCOUNT_ID_LIST+=(${ACCOUNT_ID}) &&
    curl -Ss -X POST ${BASE_URL}/identities/accounts/${ACCOUNT_ID} -H "Authorization: ${I_ACCESS_TOKEN}" -H "Content-Type: application/json" &&
    sleep 1.0
    SECURITY=$(curl -Ss -X GET ${BASE_URL}/accounts/${ACCOUNT_ID}/security -H "Authorization: ${I_ACCESS_TOKEN}" -H "Content-Type: application/json") &&
    INFO=$(echo ${SECURITY} | jq -r ".info") &&
    SK_O=$(echo ${SECURITY} | jq -r ".sk_o") &&
    INFO_LIST+=(${INFO}) &&
    SK_O_LIST+=(${SK_O}) &&
    curl -Ss -X POST ${BASE_URL}/tokenSpecs/mint -H "Authorization: ${I_ACCESS_TOKEN}" -H "Content-Type: application/json" -d "{\"account_id\": \"${ACCOUNT_ID}\",\"mint_amount\": 10000}"
    echo ""
done
echo "$(IFS=,; echo "ACCOUNT_ID_LIST=${ACCOUNT_ID_LIST[*]}")" >> account_info_fin.env
echo "$(IFS=,; echo "INFO_LIST=${INFO_LIST[*]}")" >> account_info_fin.env
echo "$(IFS=,; echo "SK_O_LIST=${SK_O_LIST[*]}")" >> account_info_fin.env
exit 0