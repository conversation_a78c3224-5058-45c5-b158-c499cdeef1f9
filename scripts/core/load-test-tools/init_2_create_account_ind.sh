#!/bin/bash

source ./script_test_common_ind.env
source ./account_info_fin.env

cp /dev/null account_info_ind.env

declare -a ARRAY_ACCOUNT_ID=(${ACCOUNT_ID_LIST//,/ })
declare -a ARRAY_SK_O=(${SK_O_LIST//,/ })
declare -a ARRAY_INFO=(${INFO_LIST//,/ })

ACCESS_TOKEN=$(curl -Ss -u ${CLIENT_ID}:${CLIENT_SECRET} -d grant_type=client_credentials ${TOKEN_URL} | jq -r ".access_token")

for ((i=0; i<${#ARRAY_ACCOUNT_ID[@]}; i++))
do
  SK_O=${ARRAY_SK_O[i]} &&
  INFO=${ARRAY_INFO[i]} &&
  ACCOUNT_ID=${ARRAY_ACCOUNT_ID[i]} &&
  SYNCHRONOUS_ACCOUNT_SIGNATURE=$(${SIGNATURE_TOOLS}/tools/synchronous_signature.sh ${SK_O} ${ACCOUNT_ID} | grep "account signature:" | sed -r 's/^account signature: //') &&
  curl -Ss -X POST ${BASE_URL}/accounts/${ACCOUNT_ID}/synchronous -H "Authorization: ${ACCESS_TOKEN}" -H "Content-Type: application/json" -d "{\"account_signature\":\"${SYNCHRONOUS_ACCOUNT_SIGNATURE}\",\"info\":\"${INFO}\"}"
  echo ""
done
echo "$(IFS=,; echo "ACCOUNT_ID_LIST=${ACCOUNT_ID_LIST[*]}")" >> account_info_ind.env
exit 0