# signature-tools
## はじめに
アカウント署名を作成するためのスクリプトについて詳細な使用方法について記載を行う。

## 利用前提

- ツールの利用者がDockerの動作環境を構築していること。
    - Windowsの場合はDocker Desktopの利用を想定
    - Macの場合はDockerがインストールされた環境を想定

## 利用方法
ツール実行前にdockerを起動した状態にすること。
以下のいずれかのコマンドを実行する。

- 引数を指定せず、対話モードで入力する場合の起動方法
  ```bash
  docker compose run --rm signature-tool
  ```
  コマンド実行後、以下のように出力が行われる。各スクリプトの説明は後述。

  ```bash
  Select a Python file to execute:
  1. approve_signature.py
  2. synchronous_signature.py
  3. transfer_signature.py
     Enter the number of the file you want to execute:
  ```

  実行したいスクリプトの番号を入力し、Enterを押下すると以下が出力される。下記は1を選択した例。
  ```bash
  Enter the number of the file you want to execute: 1
  You selected: approve_signature.py
  approve_signature.py requires arguments: [sk_o] [owner_account_id] [spender_account_id] [approve_amount]
  Enter 4 argument(s) for the script (space separated):
  ```

  スクリプト実行に必要な引数が出力されるため、半角スペース区切りで引数を入力しEnterを押下すると、アカウント署名が出力される。
  ```bash
  approve_signature.py requires arguments: [sk_o] [owner_account_id] [spender_account_id] [approve_amount]
  Enter 4 argument(s) for the script (space separated): xxx〜 601xxxxxxxxxxxxxxxxxxxx 600xxxxxxxxxxxxxxxxxxxx 10000
  account signature: 0x〜
  ```

- 引数を指定し、対話モードなしで直接起動する場合の起動方法
  ```bash
  docker compose run --rm signature-tool
  ```
  上記コマンドの後に半角スペース区切りで、スクリプト名とスクリプトに必要な引数を入力する。  
  引数を指定し、対話モードなしで起動するコマンドの例
  ```bash
  docker compose run --rm signature-tool approve_signature.py fce80e57878d682e3fcb34860098431f48d7a1814d93d7a3a35339192530ee9b 601xxxxxxxxxxxxxxxxxxxx 600yi48ZLSmqE9WGSqzsCPJRy4SZ04qL 10000
  ```
  引数を入力しEnterを押下すると、アカウント署名が出力される。
  ```bash
  account signature: 0x〜
  ```

## スクリプト説明
| 実行できるスクリプト               | 対象のCoreAPI                               | パラメータ                                                                                                                      |
|--------------------------|------------------------------------------|----------------------------------------------------------------------------------------------------------------------------|
| approve_signature.py     | 送金許可設定前確認、送金許可設定                         | string sk_o <br> string owner_account_id <br> string spender_account_i <br> uint255 approve_amount                         |
| synchronous_signature.py | BizZoneアカウント申込受付前確認、BizZoneアカウント申込受付     | string sk_o <br> string account_id                                                                                         |
| transfer_signature.py    | 利用者アカウントから他利用者への送金前確認、利用者アカウントから他利用者への送金 | string sk_o <br> string send_account_id <br> string from_account_id <br> string to_account_id <br> uint256 transfer_amount |