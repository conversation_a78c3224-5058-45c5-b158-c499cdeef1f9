{"approve_signature.py": {"description": "approve_signature.py requires arguments: [sk_o] [owner_account_id] [spender_account_id] [approve_amount]", "count": 4}, "synchronous_signature.py": {"description": "synchronous_signature.py requires arguments: [sk_o] [account_id]", "count": 2}, "transfer_signature.py": {"description": "transfer_signature.py requires arguments: [sk_o] [send_account_id] [from_account_id] [to_account_id] [transfer_amount]", "count": 5}}