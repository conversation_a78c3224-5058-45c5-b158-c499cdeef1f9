import os
import sys
import subprocess

if len(sys.argv) != 6:
    print(f"usage: python {os.path.basename(__file__)} [sk_o] [send_account_id] [from_account_id] [to_account_id] [transfer_amount]")
    sys.exit()

try:
    # 現在のディレクトリを保存して、スクリプトのディレクトリに移動
    original_dir = os.getcwd()
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    # nodeコマンドを実行
    subprocess.run(["node", "./account_signature.js", sys.argv[1], "transfer", "string", sys.argv[2], "string", sys.argv[3], "string", sys.argv[4], "uint256", sys.argv[5]], check=True)

finally:
    # 元のディレクトリに戻る
    os.chdir(original_dir)
