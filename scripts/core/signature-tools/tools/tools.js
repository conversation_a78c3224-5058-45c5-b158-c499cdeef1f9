const ethSigUtil = require('eth-sig-util');

function extSign(web3, privateKey, typesArray, parameters) {
    const abi = web3.eth.abi.encodeParameters(typesArray, parameters);
    const hash = web3.utils.keccak256(abi);
    if (privateKey.startsWith('0x')) {
        return [ethSigUtil.personalSign(Buffer.from(privateKey.slice(2), 'hex'), { data: hash }), hash];
    } else {
        return [ethSigUtil.personalSign(Buffer.from(privateKey, 'hex'), { data: hash }), hash];
    }
}

module.exports = {
    extSign: extSign
};
