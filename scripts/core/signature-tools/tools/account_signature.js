const Tools = require('./tools.js');
const Web3 = require('web3');
const elliptic = require('elliptic');
const secp256k1 = new elliptic.ec('secp256k1'); // eslint-disable-line
const bytes32 = require('bytes32');

// 'node [実行ファイル] [引数]'の順で入れると引数がargv[2]になる
// node tools/account_signature.js privateKey methodName UINT256 1 UINT256 2
const privateKey = process.argv[2];
const methodName = process.argv[3];
let types = [];
let values = [];

let web3 = new Web3();
for (let i = 4; i < process.argv.length; i++) {
    // string型の場合はbytes32型に変換する
    if (process.argv[i] == "string") {
        types.push("bytes32");
    } else {
        types.push(process.argv[i]);
    }
    switch (process.argv[i]) {
        case 'bytes':
        case 'bytes32':
            values.push(Buffer.from(process.argv[++i], 'hex'));
            break;
        case 'string':
            values.push(Buffer.from(web3.utils.asciiToHex(process.argv[++i]).padEnd(66, "0").substring(2), 'hex'));
            break;
        default:
            values.push(process.argv[++i]);
    }
}

types.push('bytes32');
values.push(bytes32({input: methodName}));

let abi = web3.eth.abi.encodeParameters(types, values);
let sign = Tools.extSign(web3, privateKey, types, values);
// public key 取得
let publicKeySet = publicKeyFromPrivateKey(privateKey);

console.log('account signature: 0x' + sign[0].slice(2));

function publicKeyFromPrivateKey(privateKey) {
    let buffer;
    if (privateKey.startsWith('0x')) {
        buffer = new Buffer.from(privateKey.slice(2), 'hex');
    } else {
        buffer = new Buffer.from(privateKey, 'hex');
    }
    let ecKey = secp256k1.keyFromPrivate(buffer);
    return [ecKey.getPublic(false, 'hex').slice(2), ecKey.getPublic(true, 'hex')];
}
