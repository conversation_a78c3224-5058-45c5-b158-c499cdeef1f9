const Tools = require('./tools.js');
const ethSigUtil = require('eth-sig-util');
const ethUtil = require("ethereumjs-util");



// "node [実行ファイル] [引数]"の順で入れると引数がargv[2]になる
// node tools/recover_signature.js hash sign
const hash = process.argv[2];
const sign = process.argv[3];
let b = ethUtil.toBuffer(addHexPrefix(sign));
let publicKey = ethSigUtil.extractPublicKey({data:addHexPrefix(hash), sig:addHexPrefix(sign)});
console.log("public key: " + publicKey.slice(2));

function addHexPrefix(str) {
    if (str.startsWith('0x')) {
        return str;
    } else {
        return '0x' + str;
    }
}
