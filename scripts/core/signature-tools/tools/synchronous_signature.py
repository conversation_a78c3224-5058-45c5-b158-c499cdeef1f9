import os
import sys
import subprocess

if len(sys.argv) != 3:
    print(f"usage: python {os.path.basename(__file__)} [sk_o] [account_id]")
    sys.exit()

try:
    # 現在のディレクトリを保存して、スクリプトのディレクトリに移動
    original_dir = os.getcwd()
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    # nodeコマンドを実行
    subprocess.run(["node", "./account_signature.js", sys.argv[1], "synchronous", "string", sys.argv[2]], check=True)

finally:
    # 元のディレクトリに戻る
    os.chdir(original_dir)
