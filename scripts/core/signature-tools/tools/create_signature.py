import json
import os
import subprocess
import sys


def list_python_files(arguments_config: any)-> list:
    """同ディレクトリ内のPythonファイルをリストアップし、名前の昇順にソート"""
    # arguments_config に定義されているスクリプト名をセットで取得
    valid_files = set(arguments_config.keys())
    # 現在のディレクトリ内のPythonファイルをフィルタリング
    files = [f for f in os.listdir('.') if f.endswith('.py') and f in valid_files]
    return sorted(files)

def display_files(files: list)-> None:
    """ファイルリストを表示"""
    print("Select a Python file to execute:")
    for i, file in enumerate(files, start=1):
        print("{0}. {1}".format(i, file))

def get_user_selection(files: list)-> str:
    """ユーザーにファイル選択を促す"""
    while True:
        try:
            selection = int(input("Enter the number of the file you want to execute: "))
        except Exception as e:
            raise ValueError("Please enter a valid number.")
        if 1 <= selection <= len(files):
            return files[selection - 1]
        else:
            raise ValueError("Invalid selection.")

def load_arguments_config(filename="arguments.json")-> any:
    """外部ファイルから引数の設定を読み込む"""
    with open(filename, 'r') as f:
        return json.load(f)

def get_arguments(script_name: str, argument_config: any, argument: any, mode: str)-> list:
    """対話モードならユーザーに引数の入力を促し、数が一致しない場合はエラーを返す"""
    if script_name in argument_config:
        config = argument_config[script_name]

        # 対話モードなら引数の入力を行う
        if mode == 'Interactive':
            print(config["description"])

            # 引数の入力を促す
            user_input = input(f"Enter {config['count']} argument(s) for the script (space separated): ")
            # 入力された引数をスペースで分割してリストにする
            arguments_list = user_input.split()
        else:
            arguments_list = argument

        # 引数の数がcountと一致するかチェック
        if len(arguments_list) == config['count']:
            return arguments_list  # 一致する場合は引数を返す
        else:
            raise ValueError("You must enter exactly {} argument(s).".format(config['count']))
    else:
        raise ValueError("No argument configuration found for this script.")

def run_script(script: str, arguments_list: list)-> None:
    """選択したスクリプトを指定された引数で実行"""
    try:
        cmd = ["python", script] + arguments_list  # arguments_listはリストとしてそのまま使用
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print("Error running script: {}".format(e))

if __name__ == "__main__":

    # 1. 引数の設定を外部ファイルから読み込む
    arguments_config: list = load_arguments_config()

    # 2. 同フォルダ内のPythonファイルを収集
    python_files: list = list_python_files(arguments_config)

    # ファイルが見つからない場合
    if not python_files:
        print("No Python files found in the current directory.")
        exit(1)

    try:
        # 起動時に引数が入力された場合
        if len(sys.argv) > 1:

            # スクリプト名を格納
            selected_file: str = sys.argv[1]

            # 5. 引数の入力を受け付ける
            arguments: list = get_arguments(selected_file, arguments_config, sys.argv[2:],'Argument')

            # 6. 選択したファイルを指定された引数で実行する
            run_script(selected_file, arguments)

        else:
            # 4. ファイルリストを表示してユーザーに選択させる
            display_files(python_files)

            selected_file: str = get_user_selection(python_files)

            # 4. 選択したファイルの出力
            print("You selected: {}".format(selected_file))

            # 5. 引数の入力を受け付ける
            arguments: list = get_arguments(selected_file, arguments_config,None,'Interactive')

            # 6. 選択したファイルを指定された引数で実行する
            run_script(selected_file, arguments)

    except Exception as e:
        print("Error: {}".format(e))
        exit(1)