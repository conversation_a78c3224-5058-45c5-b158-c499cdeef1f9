# Python ベースイメージを使用
FROM python:3.12.7-slim

# Node.js, npm, make, g++  を公式リポジトリからインストール
RUN apt-get update && apt-get install -y \
    curl \
    make \
    g++ && \
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 作業ディレクトリを作成
WORKDIR /usr/src/app

# Node.js 用の package.json をコンテナにコピー
COPY ./package.json ./

# 依存するNode.jsモジュールのインストール
RUN npm install

# 依存ライブラリのインストール
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Python スクリプトをコンテナ内にコピー
COPY tools/ .

# スクリプト実行コマンドを設定
ENTRYPOINT ["python", "create_signature.py"]
