#!/bin/bash

pkill -f '55432:'
echo "Connecting to RDS at localhost:55432."
echo -e "\xE2\x9C\x85 \033[1;32mStopping at CTRL+Z.\033[0;39m"
BASTION=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=dcbg-dcf_*_ec2_bastion" --query "Reservations[].Instances[].InstanceId" --output text)
ssh -i ~/.ssh//bastion_ntt-mc-fin.pem ec2-user@"${BASTION}" -NL 55432:dcbg-dcf-ntt-mc-fin.cluster-ct9ntcir5bgx.ap-northeast-1.rds.amazonaws.com:5432
