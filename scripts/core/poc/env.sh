#!/bin/bash

export TOKEN_URL=https://auth.dcjpy-fin.dc-labo.com/oauth2/token
echo "TOKEN_URL:$TOKEN_URL"
export BASE_URL=https://dcjpy-fin.dc-labo.com
echo "BASE_URL=$BASE_URL"
export DB=dcbg-dcf-dcjpy-fin.cluster-ccjlblybrled.ap-northeast-1.rds.amazonaws.com
echo "DB:$DB"
export DB_PORT=5432
echo "DB_PORT:$DB_PORT"
export DB_USER=Administrator
echo "DB_USER:$DB_USER"
export DB_NAME=postgres
echo "DB_NAME:$DB_NAME"
export PGPASSWORD=password
echo "PGPASSWORD:$PGPASSWORD"
export KEY_ADMIN=
echo "KEY_ADMIN:$KEY_ADMIN"
export TRUFFLE_NETWORK=main
echo "TRUFFLE_NETWORK:$TRUFFLE_NETWORK"
export CONTRACT_PATH=
echo "CONTRACT_PATH:$CONTRACT_PATH"
export A_CLIENT_ID=76q0dbieoidva8178smguu2bgg
echo "A_CLIENT_ID:$A_CLIENT_ID"
export A_CLIENT_SECRET=do7a1gobkei3g0t65jrrj6jp7d69lvtgo7gi79iaptd1mlo99j8
echo "A_CLIENT_SECRET:$A_CLIENT_SECRET"
A_ACCESS_TOKEN=$(curl -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
export A_ACCESS_TOKEN
echo "A_ACCESS_TOKEN:$A_ACCESS_TOKEN"
export SIGNATURE_TOOLS=../signature-tools
echo "SIGNATURE_TOOLS:$SIGNATURE_TOOLS"
