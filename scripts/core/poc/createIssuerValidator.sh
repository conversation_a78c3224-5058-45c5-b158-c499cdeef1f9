#!/bin/bash

source env.sh

if [ $# -ne 2 ]; then
echo "usage:"
echo "    bash $0 [issuer name] [validator name]"
exit 1
fi

ISSUER_ID=$(curl -Ss -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"$1\"}" | jq -r ".issuer_id"); echo $ISSUER_ID

ISSUER_RES=$(curl -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
I_CLIENT_ID=$(echo $ISSUER_RES | jq -r ".client_id")
I_CLIENT_SECRET=$(echo $ISSUER_RES | jq -r ".client_secret")

VALIDATOR_ID=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"$2\",\"issuer_id\":\"$ISSUER_ID\"}" | jq -r ".validator_id"); echo $VALIDATOR_ID

VALIDATOR_RES=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}")
V_CLIENT_ID=$(echo $VALIDATOR_RES | jq -r ".client_id")
V_CLIENT_SECRET=$(echo $VALIDATOR_RES | jq -r ".client_secret")

echo "****************************************"
echo "ISSUER_ID=$ISSUER_ID"
echo "I_CLIENT_ID=$I_CLIENT_ID"
echo "I_CLIENT_SECRET=$I_CLIENT_SECRET"
echo "****************************************"
echo "VALIDATOR_ID=$VALIDATOR_ID"
echo "V_CLIENT_ID=$V_CLIENT_ID"
echo "V_CLIENT_SECRET=$V_CLIENT_SECRET"
echo "****************************************"
echo "DB"
psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select * from external_signers where signer_id='$ISSUER_ID'"
echo "****************************************"

sleep 1

echo "確認"
I_ACCESS_TOKEN=$(curl -Ss -u $I_CLIENT_ID:$I_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json"
echo ""
V_ACCESS_TOKEN=$(curl -Ss -u $V_CLIENT_ID:$V_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json"
echo ""
