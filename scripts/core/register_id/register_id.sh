#!/bin/bash
# 発行者、バリデータを登録
# 前提: register_id.envに環境変数が記載されていること

# 設定ファイルの存在チェック
if [  ! -e "register_id.env" ]; then
  echo "register_id.env が存在しません。設定ファイルを用意してください。"
  exit 1
fi

echo ""
echo "---- register_id.env の設定内容 ----"
cat ./register_id.env
echo ""
echo "-----------------------------------"
echo ""

echo "上記設定内容に不備がない場合は y を入力してください。"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

source register_id.env

ADMIN_ACCESS_TOKEN=$(curl -Ss -u $ADMIN_CLIENT_ID:$ADMIN_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
echo "Admin アクセストークン :"
echo $ADMIN_ACCESS_TOKEN

if $IS_COMMON; then
  # 共通領域の場合は発行者を登録する
  # 発行者作成
  REQUEST_BODY='{"name":"'$ISSUER_NAME'"}'
  RESPONSE=$(curl -Ss -X POST $BASE_URL/issuers -H "Authorization: $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY")
  echo "発行者作成結果 :"
  echo $RESPONSE
  ISSUER_ID=$(echo $RESPONSE | jq -r ".issuer_id")
  # [ -z $ISSUER_ID ]では判定できない
  if [ $ISSUER_ID = null ]; then
    echo "発行者作成が失敗しました"
    exit 1
  fi
  echo ""

  # 発行者アイデンティティ作成
  RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID -H "Authorization: $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json")
  echo "発行者アイデンティティ作成結果 :"
  echo $RESPONSE
  ISSUER_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
  ISSUER_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
  if [ $ISSUER_CLIENT_ID = null ]; then
    echo "発行者アイデンティティ作成が失敗しました"
    exit 1
  fi
  echo ""
fi

# バリデータ作成
if $IS_COMMON; then
  REQUEST_BODY='{"name":"'$VALIDATOR_NAME'","issuer_id":"'$ISSUER_ID'"}'
else
  REQUEST_BODY='{"name":"'$VALIDATOR_NAME'"}'
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY")
echo "バリデータ作成結果 :"
echo $RESPONSE
VALIDATOR_ID=$(echo $RESPONSE | jq -r ".validator_id")
if [ $VALIDATOR_ID = null ]; then
  echo "バリデータ作成が失敗しました"
  exit 1
fi
echo ""

# バリデータアイデンティティ作成
if $IS_COMMON; then
  REQUEST_BODY='{"issuer_id":"'$ISSUER_ID'"}'
else
  REQUEST_BODY='{"issuer_id":""}'
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json" -d "$REQUEST_BODY")
echo "バリデータアイデンティティ作成結果 :"
echo $RESPONSE
VALIDATOR_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
VALIDATOR_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
if [ $VALIDATOR_CLIENT_ID = null ]; then
  echo "バリデータアイデンティティ作成が失敗しました"
  exit 1
fi
echo ""

# プロバイダのアクセストークン取得
PROVIDER_ACCESS_TOKEN=$(curl -Ss -u $PROVIDER_CLIENT_ID:$PROVIDER_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
echo "Provider アクセストークン :"
echo $PROVIDER_ACCESS_TOKEN

# トークンのID取得
RESPONSE=$(curl -Ss -X GET $BASE_URL/tokenSpecs -H "Authorization: $PROVIDER_ACCESS_TOKEN" -H "Content-Type: application/json")
echo "トークンのID取得 :"
echo $RESPONSE
TOKEN_ID=$(echo $RESPONSE | jq -r ".token_id")
if [ $TOKEN_ID = null ]; then
  echo "トークンのID取得が失敗しました"
  exit 1
fi
echo ""

# トークンの更新
echo "トークンの更新 :"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $PROVIDER_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"'$TOKEN_NAME'","symbol":"'$TOKEN_SYMBOL'","peg_kind":""}')
TOKEN_SPEC=$(curl -Ss -X GET $BASE_URL/tokenSpecs -H "Authorization: $PROVIDER_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $TOKEN_SPEC

echo "---- 登録確認 ----"
if $IS_COMMON; then
  echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -c "select case when count(*) = 2 then 'external_signers ok' else 'external_signers ng' end as signers_check from external_signers where signer_id in ('$ISSUER_ID','$VALIDATOR_ID')")
  echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -c "select case when count(*) = 1 then 'identity_authorities ok' else 'identity_authorities ng' end as identity_authorities_check from identity_authorities where issuer_id = '$ISSUER_ID' and validator_id = '$VALIDATOR_ID'")
  ISSUER_PK=$(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -A -t -c "select private_key from external_signers where signer_id='$ISSUER_ID'")
  ISSUER_EOA=$(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -A -t -c "select eoa from external_signers where signer_id='$ISSUER_ID'")
  echo "発行者取得結果 :"
  RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json")
  echo $RESPONSE
else
  echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -c "select case when count(*) = 1 then 'external_signers ok' else 'external_signers ng' end as signers_check from external_signers where signer_id in ('$VALIDATOR_ID')")
  echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -c "select case when count(*) = 1 then 'identity_authorities ok' else 'identity_authorities ng' end as identity_authorities_check from identity_authorities where validator_id = '$VALIDATOR_ID'")
fi

echo "バリデータ取得結果 :"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $ADMIN_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
VALIDATOR_PK=$(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -A -t -c "select private_key from external_signers where signer_id='$VALIDATOR_ID'")
VALIDATOR_EOA=$(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -A -t -c "select eoa from external_signers where signer_id='$VALIDATOR_ID'")
echo ""

echo "---- 登録結果 ----"
echo "token : "$TOKEN_SPEC
if $IS_COMMON; then
  echo "issuer_id : "$ISSUER_ID
  echo "issuer_client_id : "$ISSUER_CLIENT_ID
  echo "issuer_client_secret : "$ISSUER_CLIENT_SECRET
  echo "issuer_private_key : "$ISSUER_PK
  echo "issuer_eoa : "$ISSUER_EOA
fi
echo "validator_id : "$VALIDATOR_ID
echo "validator_client_id : "$VALIDATOR_CLIENT_ID
echo "validator_client_secret : "$VALIDATOR_CLIENT_SECRET
echo "validator_private_key : "$VALIDATOR_PK
echo "validator_eoa : "$VALIDATOR_EOA
echo "-----------------"

exit 0