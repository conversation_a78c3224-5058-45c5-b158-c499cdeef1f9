# id-resolver
## はじめに
dc_bank_numberからaccount_idを、またはその逆のaccount_idからdc_bank_numberを取得するためのツールについての使用方法の記載を行う。

## 利用前提

- ツールの利用者がDockerの動作環境を構築していること。
  - Windowsの場合はDocker Desktopの利用を想定
  - Macの場合はDockerがインストールされた環境を想定

## 利用方法
/dcbg-dcf-devops-tools/scripts/core/id-resolverに移動し、
dockerを起動した状態で以下のいずれかのコマンドを実行する。  
なお、引数にはDC口座番号または、AccountIDのどちらかが必須。  
同一のDC口座番号またはAccount IDを指定して複数回実行した場合、結果はすべて同じ値となる。

- DC口座番号をAccountIDに変換する場合
  ```bash
  docker build -t id_resolver .
  docker run --rm id_resolver DC口座番号
  ```
  DC口座番号はフォーマットチェックを実施しているため、正しい形式で入力すること。  
  実行後、戻り値としてAccount IDが返却される。


- AccountIDをDC口座番号に変換する場合
  ```bash
  docker build -t id_resolver .
  docker run --rm id_resolver AccountID
  ```
  AccountIDは60から始まる値を入力すること。  
  実行後、戻り値としてDC口座番号が返却される。

以下にツールの実行例を示す。  

DC口座番号をAccountIDに変換した場合
```bash
docker run --rm id_resolver DC310-1001-0007-1
Account ID: 605IVQUG9uZDjYb9IXJrZiXoIQuG5QD9
```
AccountIDをDC口座番号に変換した場合
```bash
docker run --rm id_resolver 605IVQUG9uZDjYb9IXJrZiXoIQuG5QD9
DC bank number: DC310-1001-0007-1
```
