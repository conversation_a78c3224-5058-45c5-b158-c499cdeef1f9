import base64
import uuid
import struct
import argparse
import re
import sys
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 定数
SECRET_KEY = b"DECURRET-DCP__DCJPY-NETWORK_____"
AES_INIT_VECTOR = b"com.decurret_dcp"
ACCOUNT_ID_PREFIX = "60"
# Base62エンコーディング
BASE62_ENCODING = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

# 暗号化用と復号化用の関数
def get_cipher(aes_mode: str) -> any:
    return AES.new(SECRET_KEY, AES.MODE_CBC, AES_INIT_VECTOR) if aes_mode == 'encrypt' else AES.new(SECRET_KEY, AES.MODE_CBC, AES_INIT_VECTOR)

# DC口座番号からAccountIdを生成
def to_account_id(generate_account_id: str) -> str:

    # フォーマットチェック
    if not re.match(r'^DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]$', generate_account_id):
        raise ValueError("dc_bank_number format is invalid.")

    # AES-256のキーとIVで暗号化
    base: bytes = generate_account_id[2:].encode()  # 先頭2文字を除外
    
    cipher = get_cipher('encrypt')
    encrypted = cipher.encrypt(pad(base, AES.block_size))
    
    if len(encrypted) != 16:
        raise ValueError("Invalid encrypted length.")
    
    hashed: str = to_hash(encrypted)

    generate_account_id = ''.join([
        ACCOUNT_ID_PREFIX, hashed,
            hashed[1], hashed[3], hashed[7], hashed[5],
            hashed[0], hashed[3], hashed[9], hashed[6]
        ])
    
    return generate_account_id

# AccountIdからDC口座番号を生成
def to_dc_bank_number(generate_dc_bank_number: str) -> str:

    # PREFIXを除いた値
    hashed: str = generate_dc_bank_number[2:24]

    # デコードを行う
    decoded_uuid: bytes = base62_decode(hashed)
    cipher = get_cipher('decrypt')
    decrypted_data = cipher.decrypt(decoded_uuid)
    decrypted_data = unpad(decrypted_data, AES.block_size)

    # DC口座番号のDCを付与
    generate_dc_bank_number = "DC" + decrypted_data.decode()

    return generate_dc_bank_number

def to_hash(encrypted: any) -> str:
    # most_sig_bitsとleast_sig_bitsを適切に取得する
    most_sig_bits, least_sig_bits = struct.unpack(">QQ", encrypted)

    # UUIDを生成し、それをBase62にエンコードする（ただしUUIDは128ビット）
    uuid_value = uuid.UUID(int=((most_sig_bits << 64) | least_sig_bits))  # 64ビットの値を連結して128ビットにする

    base62_hashed: str = base62_encode(uuid_value.int)  # UUIDをBase62に変換

    # エンコード後の桁数が元のバイト配列に一致するように0を補完
    required_length = 22  # Base62のハッシュ結果は通常22文字
    if len(base62_hashed) < required_length:
        base62_hashed = '0' * (required_length - len(base62_hashed)) + base62_hashed

    return base62_hashed

# Base62エンコードに0を維持する処理を追加
def base62_encode(num: int) -> str:
    if num == 0:
        return BASE62_ENCODING[0]
    arr = []
    while num:
        num, rem = divmod(num, 62)
        arr.append(BASE62_ENCODING[rem])
    arr.reverse()

    return ''.join(arr)

def base62_decode(hashed: str) -> bytes:
    # 末尾のゼロパディングを削除
    hashed = hashed.rstrip('0')

    num = 0
    for char in hashed:
        num = num * 62 + BASE62_ENCODING.index(char)

    # バイト列の長さを16バイトに調整
    decoded_bytes = num.to_bytes((num.bit_length() + 7) // 8, 'big')

    # バイト列が16バイトに満たない場合、16バイトになるように補完
    if len(decoded_bytes) < 16:
        # 16バイトに満たない場合、先頭に0を追加して16バイトにする
        decoded_bytes = decoded_bytes.rjust(16, b'\0')

    return decoded_bytes

# メイン関数: コマンドライン引数を処理
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert DC bank number to Account ID or Convert Account ID to DC bank number.")
    parser.add_argument("convert_number", help="DC bank number to convert into Account ID or Account ID to convert into DC bank number")
    args = parser.parse_args()

    convert_number: str = args.convert_number

    try:
        if convert_number.startswith("DC"):
            account_id: str = to_account_id(convert_number)
            print("Account ID: {}".format(account_id))
        elif convert_number.startswith("60"):
            dc_bank_number: str = to_dc_bank_number(convert_number)
            print("DC bank number: {}".format(dc_bank_number))
        else:
            raise ValueError("convert_number format is invalid.")

    except Exception as e:
        print("Error: {}".format(e))
        exit(1)