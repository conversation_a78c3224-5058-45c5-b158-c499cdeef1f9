#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

directory=$SCRIPTDIR/"targets/ssm"
TEXT_PREFIX=delete_ssm_"$TARGET_ZONE"_tokyo

echo "### ssmの初期化を行います。 ###"

init_folder $directory

# ssm からデータを取得してテキストファイルに出力
aws $AWS_END_POINT ssm describe-parameters --region "$AWS_REGION" --query "Parameters[?contains(Name, 'dcjpy/core/private_key/"$ZONE_ID"')].Name" --output text  | tr '\t' '\n' > "$directory/${TEXT_PREFIX}.txt"

# テキストファイルを分割
split_text_file "$directory/${TEXT_PREFIX}.txt" "$directory"

# テキストファイルを処理
for file in "$directory"/*tokyo*.txt; do
    if [ ! -e "$file" ]; then
        echo "削除対象がないためスキップします。"
        continue
    fi

    echo "${file}のデータを削除します"
    absolute_path="$(realpath "$file")"

    # 実施コマンド
    if [ -z $AWS_PROFILE ]; then
      AWS_PROFILE="default"
    fi
    command="export AWS_PROFILE="$AWS_PROFILE"; \
    while read target; do \
    aws "$AWS_END_POINT" ssm delete-parameter --region "$AWS_REGION" --name \$target > /dev/null; \
    done < "$absolute_path"; \
    exit"
    execute_command_in_terminal "$command"
done

# osakaリージョンがある環境の場合はそちらからも削除する
if [ $TARGET_ZONE = 'fin' ] && [ "$RELEASE_ENV" != 'dev' ]; then
    TEXT_PREFIX=delete_ssm_"$TARGET_ZONE"_osaka
    # ssm からデータを取得してテキストファイルに出力
    aws $AWS_END_POINT ssm describe-parameters --region "$REPLICA_TARGET_REGION" --query "Parameters[?contains(Name, 'dcjpy/core/private_key/"$ZONE_ID"')].Name" --output text  | tr '\t' '\n' > "$directory/${TEXT_PREFIX}.txt"

    # テキストファイルを分割
    split_text_file "$directory/${TEXT_PREFIX}.txt" "$directory"

    # テキストファイルを処理
    for file in "$directory"/*osaka*.txt; do
        if [ ! -e "$file" ]; then
            echo "削除対象がないためスキップします。"
            continue
        fi

        echo "${file}のデータを削除します"
        absolute_path="$(realpath "$file")"

        # 実施コマンド
        if [ -z $AWS_PROFILE ]; then
          AWS_PROFILE="default"
        fi
        command="export AWS_PROFILE="$AWS_PROFILE"; \
        while read target; do \
        aws "$AWS_END_POINT" ssm delete-parameter --region "$REPLICA_TARGET_REGION" --name \$target > /dev/null; \
        done < "$absolute_path"; \
        exit"
        execute_command_in_terminal "$command"
    done
fi

echo "### ssmの初期化が完了しました。 ###"
