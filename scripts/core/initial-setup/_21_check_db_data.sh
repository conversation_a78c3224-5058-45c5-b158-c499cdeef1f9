#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

$SCRIPTDIR/_port_forward.sh

echo "◾TBLの作成件数の確認"
output=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "\dt" 2>&1)
# テーブル行のみに絞り込み、行数をカウント
table_count=$(echo "$output" | awk '/^ public \|/{count++} END {print count}')
if [[ $table_count == 8 ]]; then
    echo "作成したテーブルの件数について想定通りです。("$table_count"件)"
else
    echo "テーブルの作成件数が想定外になっています。("$table_count"件) 右記の実行結果を確認してください。 psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -c "\dt""
fi

$SCRIPTDIR/_disconnect_port_forward.sh
