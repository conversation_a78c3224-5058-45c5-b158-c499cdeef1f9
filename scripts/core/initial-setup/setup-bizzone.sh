#!/bin/bash
function error_close() {
  $SCRIPTDIR/_disconnect_port_forward.sh
  echo "登録処理を中断します。"
  exit 1
}

# BizZoneへの金融機関登録にあたり、Issuer作成、Validator作成、サービスオーナー作成を行うシェル。
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
echo "BizZoneのEscrowアカウント用のデータ作成を行います。"

# 環境変数の設定
ENV_FILE=./env/biz.env
if [ ! -f "$ENV_FILE" ]; then
  echo $ENV_FILE"が未作成です。手順に則り作成をしてください。"
  exit 1
fi
source $ENV_FILE
if [ -z "$TOKEN_URL" ] || [ -z "$BASE_URL" ] || [ -z "$A_CLIENT_ID" ] || [ -z "$A_CLIENT_SECRET" ]; then
  echo bpmbiz.env"が未設定のため設定をお願いします。"
  exit 1
fi
if [ -z "$DB_PORT" ] || [ -z "$USER" ] || [ -z "$DB_NAME" ] || [ -z "$PGPASSWORD" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi
if [ -z "$ZONE_NAME" ] || [ -z "$TOKEN_NAME" ] || [ -z "$TOKEN_SYMBOL" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi

# （Admin）ACCESS_TOKENを取得する
if [[ "$TOKEN_URL" == *"localhost"* ]]; then
    A_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -X GET $TOKEN_URL'/admin_id');
else
    A_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token');
fi

$SCRIPTDIR/_port_forward.sh true

# プロバイダー作成(再実行不可)
echo "1-1 プロバイダー作成"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/providers -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'","zone_name":"'"$ZONE_NAME"'"}');
echo ${RESULT}| jq .
PROVIDER_ID=$(echo ${RESULT} | jq -r ".provider_id")
if [ "$PROVIDER_ID" == null ]; then
  # 登録済みかを確認
  echo "プロバイダー作成に失敗しましたが、プロバイダー取得を行い登録済みであるかを確認します。"
fi
echo ""

# プロバイダー取得
echo "1-2 プロバイダー取得 1件取得できることを確認する"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
PROVIDER_ID=$(echo ${RESULT} | jq -r ".provider_id")
if [ "$PROVIDER_ID" == null ]; then
  # 登録済みかを確認
  echo "プロバイダーの取得ができませんでした。"
  error_close
fi
echo ""

# プロバイダーのアイデンティティを作成する
echo "1-3 プロバイダーのアイデンティティを作成する"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/identities/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'"}')
echo ${RESULT}| jq .
P_CLIENT_ID=$(echo $RESULT | jq -r ".client_id")
P_CLIENT_SECRET=$(echo $RESULT | jq -r ".client_secret")
if [ "$P_CLIENT_ID" == null ]; then
  # 登録済みかを確認
  echo "プロバイダーのアイデンティティ作成に失敗しました。"
  error_close
fi
echo ""

echo "1-4 ACCESS_TOKENを取得する"
if [[ "$TOKEN_URL" == *"localhost"* ]]; then
    P_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -X GET $TOKEN_URL'/'$P_CLIENT_ID);
else
    P_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -u $P_CLIENT_ID:$P_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
fi
if [ "$P_ACCESS_TOKEN" == null ]; then
  echo "ACCESS_TOKENの取得に失敗しました。"
  exit 1
fi
echo ""

# プロバイダー取得
echo "2-1 プロバイダー取得"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/providers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
echo ""

# トークン作成(再実行不可)
echo "3-1 トークン作成"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'","name":"'"$TOKEN_NAME"'","symbol":"'"$TOKEN_SYMBOL"'"}');
echo ${RESULT}| jq .
TOKEN_ID=$(echo $RESULT | jq -r ".token_id")
if [ "$TOKEN_ID" == null ]; then
  echo "トークン作成失敗しましたが、トークン照会を行い登録済みであるかを確認します。"
fi
echo ""

# トークン照会
echo "3-2 トークン照会"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
TOKEN_ID=$(echo $RESULT | jq -r ".token_id")
if [ "$TOKEN_ID" == null ]; then
  # 登録済みかを確認
  echo "トークンの作成に失敗しました。"
  error_close
fi
echo ""
echo "登録処理が正常終了しました。"

# 登録内容の出力
echo "登録内容の出力を行います。"
echo "▼DB登録内容の確認▼"

echo "▼プロバイダー▼"
echo "client_entity:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from client_entity where entity_id='$PROVIDER_ID'"
echo "entity_signer:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from entity_signer where entity_id='$PROVIDER_ID'"
echo ""

$SCRIPTDIR/_disconnect_port_forward.sh

echo "全ての処理が正常終了しました。"
