#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo "### DynamoDBの初期化を行います。 ###"
table=sending_counter
echo "### "$table"を初期化します。 ###"
aws $AWS_END_POINT dynamodb update-item --table-name $table --key '{"counter_name": {"S": "sending"}}' --update-expression "SET #c = :val" --expression-attribute-names '{"#c": "counter"}' --expression-attribute-values '{":val": {"N": "0"}}'

# 初期化後の値確認
RESULT=$(aws $AWS_END_POINT dynamodb scan --table-name $table)
counter=$(echo $RESULT | jq -r .Items[0].counter.N)
if [ $counter != 0 ]; then
    echo $table"の設定値が想定外のため処理を中断します。"
    echo $RESULT | jq .
    exit 1
fi

# 削除対象のテーブルの情報を取得
table=balance_cache
echo "### "$table"を初期化します。 ###"
key_schema=$(aws $AWS_END_POINT dynamodb describe-table --table-name $table | jq -r '.Table.KeySchema')
partition_key=$(echo $key_schema | jq -r '.[] | select(.KeyType == "HASH") | .AttributeName')
sort_key=$(echo $key_schema | jq -r '.[] | select(.KeyType == "RANGE") | .AttributeName')
items=$(aws $AWS_END_POINT dynamodb scan --table-name $table --output json)

# データを1件ずつ削除
for item in $(echo $items | jq -r '.Items[] | @base64')
do
    # パーティションキーとソートキーを取得しデータを削除
    json_item=$(echo $item | base64 --decode)
    json_key=$(echo $json_item | jq -c "{ \"$partition_key\": .\"$partition_key\", \"$sort_key\": .\"$sort_key\" }")
    aws $AWS_END_POINT dynamodb delete-item --table-name $table --key "$json_key"
done

# 削除後の件数確認
RESULT=$(aws $AWS_END_POINT dynamodb scan --table-name $table)
count=$(echo $RESULT | jq -r .Count)
if [ $count != 0 ]; then
    echo $table"の初期化結果が想定外のため処理を中断します。"
    echo $RESULT | jq .
    exit 1
fi

echo "### DynamoDBの初期化が完了しました。 ###"
