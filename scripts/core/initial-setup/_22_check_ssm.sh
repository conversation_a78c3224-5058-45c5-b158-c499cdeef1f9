#!/bin/bash
function check_ssm() {
    command=$1
    region=$2
    ssm_name=$3
    key_admin=$4
    SSM_VALUE=$($command 2>&1)
    get_status=$?
    if [[ $get_status -ne 0 ]]; then
        check_error "$SSM_VALUE" "$ssm_name"
        return
    fi
    # 取得結果の出力
    arn=$(echo $SSM_VALUE | jq -r ".Parameter.ARN")
    name=$(echo $SSM_VALUE | jq -r ".Parameter.Name")
    value=$(echo $SSM_VALUE | jq -r ".Parameter.Value")
    echo "ARN:"$arn
    echo "Name:"$name
    echo "KEY_ADMIN:"$value
    # ARNにリージョンが含まれているか
    is_contained "$arn" "$region"
    arn_status=$?
    # SSMに登録したNameは一致しているか(取得できている時点で一致となる想定)
    is_equal "$name" "$ssm_name"
    name_status=$?
    # SSMに登録したKEY_ADMINは一致しているか(復号した状態で取得しているためそのまま検証する)
    is_equal "$value" "$key_admin"
    key_admin_status=$?
    if [ "$arn_status" -eq 0 ] && [ "$name_status" -eq 0 ] && [ "$key_admin_status" -eq 0 ]; then
        echo "${name}の取得結果は想定通りです。"
    else
        echo "${name}の登録結果が想定外です。 ARN:"$(replace_status "$arn_status")" NAME:"$(replace_status "$name_status")" KEY_ADMIN:"$(replace_status "$key_admin_status")" 取得コマンド:"$command""
    fi
}

function is_contained() {
    actual_value="$1"
    expected_value="$2"
    if [[ "$actual_value" == *"$expected_value"* ]]; then
        return 0
    else
        return 1
    fi
}
function is_equal() {
    actual_value="$1"
    expected_value="$2"
    if [ "$actual_value" = "$expected_value" ]; then
        return 0
    else
        return 1
    fi
}
function replace_status() {
    case $1 in
        0)
            echo "想定通り"
            ;;
        1)
            echo "想定外"
            ;;
        *)
            echo "想定外"
            ;;
    esac
}
function check_error() {
    ERROR_MESSAGE=$1
    TARGET_NAME=$2
    if [ -z "$1" ]; then
        #正常終了しているので何もしない
        :
    elif [[ $ERROR_MESSAGE == *"ParameterNotFound"* ]]; then
        echo "対象のkeyはSSMに存在しません。対象:"$TARGET_NAME" リージョン:"$region
    else
        # 想定外のエラーが発生した場合"
        echo $ERROR_MESSAGE
        exit 1
    fi
}
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo "◾◾◾ssm登録内容◾◾◾"
ssm_name="/dcjpy/core/private_key/$ZONE_ID/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
key_admin=$(get_key_admin)
command="aws $AWS_END_POINT ssm get-parameter --region $AWS_REGION --with-decryption --name $ssm_name"
check_ssm "$command" "$AWS_REGION" "$ssm_name" "$key_admin"

if [ $TARGET_ZONE = 'fin' ] && [ "$RELEASE_ENV" != 'dev' ]; then
    echo "◾レプリケート先の登録内容確認"
    command="aws $AWS_END_POINT ssm get-parameter --region $REPLICA_TARGET_REGION --with-decryption --name $ssm_name"
    check_ssm "$command" "$REPLICA_TARGET_REGION" "$ssm_name" "$key_admin"
fi