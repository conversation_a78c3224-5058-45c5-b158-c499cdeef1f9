#!/bin/bash
function error_close() {
  $SCRIPTDIR/_disconnect_port_forward.sh
  echo "登録処理を中断します。"
  exit 1
}

# FizZoneへの金融機関登録にあたり、Issuer作成、Validator作成、サービスオーナー作成を行うシェル。
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
echo "FinZoneのEscrowアカウント用のデータ作成を行います。"

# 環境変数の設定
ENV_FILE=./env/fin.env
if [ ! -f "$ENV_FILE" ]; then
  echo $ENV_FILE"が未作成です。手順に則り作成をしてください。"
  exit 1
fi
source $ENV_FILE
if [ -z "$TOKEN_URL" ] || [ -z "$BASE_URL" ] || [ -z "$A_CLIENT_ID" ] || [ -z "$A_CLIENT_SECRET" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi
if [ -z "$DB_PORT" ] || [ -z "$USER" ] || [ -z "$DB_NAME" ] || [ -z "$PGPASSWORD" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi
if [ -z "$ZONE_NAME" ] || [ -z "$TOKEN_NAME" ] || [ -z "$TOKEN_SYMBOL" ] || [ -z "$ISSUER_NAME" ] || [ -z "$VALIDATOR_NAME" ]; then
  echo $ENV_FILE"が未設定のため設定をお願いします。"
  exit 1
fi

# （Admin）ACCESS_TOKENを取得する
if [[ "$TOKEN_URL" == *"localhost"* ]]; then
    A_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -X GET $TOKEN_URL'/admin_id');
else
    A_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token');
fi

$SCRIPTDIR/_port_forward.sh true

# プロバイダー作成(再実行不可)
echo "1-1 プロバイダー作成"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/providers -H "Authorization: Bearer $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'","zone_name":"'"$ZONE_NAME"'"}');
echo ${RESULT}| jq .
PROVIDER_ID=$(echo ${RESULT} | jq -r ".provider_id")
if [ "$PROVIDER_ID" == null ]; then
  # 登録済みかを確認
  echo "プロバイダー作成に失敗しましたが、プロバイダー取得を行い登録済みであるかを確認します。"
fi
echo ""

# プロバイダー取得
echo "1-2 プロバイダー取得 1件取得できることを確認する"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
PROVIDER_ID=$(echo ${RESULT} | jq -r ".provider_id")
if [ "$PROVIDER_ID" == null ]; then
  # 登録済みかを確認
  echo "プロバイダーの取得ができませんでした。"
  error_close
fi
echo ""

# プロバイダーのアイデンティティを作成する
echo "1-3 プロバイダーのアイデンティティを作成する"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/identities/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'"}')
echo ${RESULT}| jq .
P_CLIENT_ID=$(echo $RESULT | jq -r ".client_id")
P_CLIENT_SECRET=$(echo $RESULT | jq -r ".client_secret")
if [ "$P_CLIENT_ID" == null ]; then
  # 登録済みかを確認
  echo "プロバイダーのアイデンティティ作成に失敗しました。"
  error_close
fi
echo ""

echo "1-4 ACCESS_TOKENを取得する"
if [[ "$TOKEN_URL" == *"localhost"* ]]; then
    P_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -X GET $TOKEN_URL'/'$P_CLIENT_ID);
else
    P_ACCESS_TOKEN=$(curl $CURL_INSECURE -Ss -u $P_CLIENT_ID:$P_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
fi
if [ "$P_ACCESS_TOKEN" == null ]; then
  echo "ACCESS_TOKENの取得に失敗しました。"
  exit 1
fi
echo ""

# プロバイダー取得
echo "2-1 プロバイダー取得"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/providers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
echo ""

# トークン作成(再実行不可)
echo "3-1 トークン作成"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'","name":"'"$TOKEN_NAME"'","symbol":"'"$TOKEN_SYMBOL"'"}');
echo ${RESULT}| jq .
TOKEN_ID=$(echo $RESULT | jq -r ".token_id")
if [ "$TOKEN_ID" == null ]; then
  echo "トークン作成失敗しましたが、トークン照会を行い登録済みであるかを確認します。"
fi
echo ""

# トークン照会
echo "3-2 トークン照会"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
TOKEN_ID=$(echo $RESULT | jq -r ".token_id")
if [ "$TOKEN_ID" == null ]; then
  # 登録済みかを確認
  echo "トークンの作成に失敗しました。"
  error_close
fi
echo ""

# Issuer作成
echo "4-1 イシュア作成"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'", "bank_code":"'${BANK_CODE}'", "name":"'"$ISSUER_NAME"'"}');
echo ${RESULT}| jq .
ISSUER_ID=$(echo $RESULT | jq -r ".issuer_id");
if [ "$ISSUER_ID" == null ]; then
  # 登録済みであるかをDBから値取得して確認
  ISSUER_ID=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select entity_id from client_entity where entity_type = 'issuer'" 2>&1 | xargs)
  if [ "$ISSUER_ID" == "" ]; then
    echo "イシュア作成に失敗しました。"
    error_close
  fi
  echo "イシュア作成に失敗しましたが、DBに登録済みのため後続処理を実施します。"
fi
echo ""

echo "4-2 イシュア取得 1件取得できることを確認する"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
ISSUER_ID=$(echo $RESULT | jq -r ".issuer_id");
if [ "$ISSUER_ID" == null ]; then
  echo "イシュア作成に失敗しました。"
  error_close
fi
echo ""

echo "4-3 イシュアのアイデンティティを作成する"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"request_id":"'$REQUEST_ID'"}')
echo ${RESULT}| jq .
ISSUER_ID=$(echo $RESULT | jq -r ".issuer_id")
ISSUER_CLIENT_ID=$(echo $RESULT | jq -r ".client_id")
ISSUER_CLIENT_SECRET=$(echo $RESULT | jq -r ".client_secret")
if [ "$ISSUER_ID" == null ]; then
  echo "イシュアのアイデンティティを作成に失敗しました。"
  error_close
fi
echo ""

# バリデータ作成
echo "5-1 バリデータ作成"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" $BASE_URL/validators -d '{"request_id":"'$REQUEST_ID'","name": "'"$VALIDATOR_NAME"'","issuer_id": "'"$ISSUER_ID"'"}');
echo ${RESULT}| jq .
VALIDATOR_ID=$(echo $RESULT | jq -r ".validator_id")
if [ "$VALIDATOR_ID" == null ]; then
  # 登録済みであるかをDBから値取得して確認
  VALIDATOR_ID=$(psql -P pager=off -U $USER -d $DB_NAME -p $DB_PORT -h localhost -t -c "select entity_id from client_entity where entity_type = 'validator'" 2>&1 | xargs)
  if [ "$VALIDATOR_ID" == "" ]; then
    echo "バリデータ作成に失敗しました。"
    error_close
  fi
  echo "バリデータ作成に失敗しましたが、DBに登録済みのため後続処理を実施します。"
fi
echo ""

# バリデータ取得
echo "5-2 バリデータ取得 1件取得できることを確認する"
RESULT=$(curl $CURL_INSECURE -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo ${RESULT}| jq .
VALIDATOR_ID=$(echo $RESULT | jq -r ".validator_id")
if [ "$VALIDATOR_ID" == null ]; then
  echo "バリデータ作成に失敗しました。"
  error_close
fi
echo ""

# バリデータのアイデンティティを作成する
echo "5-3 バリデータのアイデンティティを作成する"
REQUEST_ID="request-$(date +'%Y%m%d-%H%M%S')"
RESULT=$(curl $CURL_INSECURE -Ss -X POST -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" $BASE_URL/identities/validators/${VALIDATOR_ID} -d '{"request_id":"'$REQUEST_ID'","issuer_id": "'"$ISSUER_ID"'"}')
echo ${RESULT}| jq .
VALIDATOR_ID=$(echo $RESULT | jq -r ".validator_id")
VALIDATOR_CLIENT_ID=$(echo $RESULT | jq -r ".client_id")
VALIDATOR_CLIENT_SECRET=$(echo $RESULT | jq -r ".client_secret")
if [ "$VALIDATOR_ID" == null ]; then
  echo "バリデータのアイデンティティを作成に失敗しました。"
  error_close
fi
echo ""
echo "登録処理が正常終了しました。"

# 登録内容の出力
echo "登録内容の出力を行います。"
echo "▼issuer関連情報▼"
echo "    Issuer ID                     : ${ISSUER_ID}"
echo "    Issuer OAuth Client ID        : ${ISSUER_CLIENT_ID}"
echo "    Issuer OAuth Client Secret    : ${ISSUER_CLIENT_SECRET}"
echo ""

echo "▼validator関連情報▼"
echo "    Validator ID                  : ${VALIDATOR_ID}"
echo "    Validator OAuth Client ID     : ${VALIDATOR_CLIENT_ID}"
echo "    Validator OAuth Client Secret : ${VALIDATOR_CLIENT_SECRET}"
echo ""

echo "▼DB登録内容の確認▼"

echo "▼プロバイダー▼"
echo "client_entity:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from client_entity where entity_id='$PROVIDER_ID'"
echo "entity_signer:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from entity_signer where entity_id='$PROVIDER_ID'"
echo ""

echo "▼イシュア▼"
echo "client_entity:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from client_entity where entity_id='$ISSUER_ID'"
echo "entity_signer:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from entity_signer where entity_id='$ISSUER_ID'"
echo ""

echo "▼バリデータ▼"
echo "client_entity:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from client_entity where entity_id='$VALIDATOR_ID'"
echo "entity_signer:"
psql -h localhost -p $DB_PORT -U $USER -d $DB_NAME -P pager=off -c "select * from entity_signer where entity_id='$VALIDATOR_ID'"
echo ""
$SCRIPTDIR/_disconnect_port_forward.sh

echo "全ての処理が正常終了しました。"
