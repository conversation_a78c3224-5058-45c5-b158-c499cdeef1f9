#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

echo "### ssmの登録を行います。 ###"
key_admin=$(get_key_admin)

aws $AWS_END_POINT ssm --region "$AWS_REGION" put-parameter --name "/dcjpy/core/private_key/$ZONE_ID/zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz" --value "$key_admin" --type "SecureString" --key-id "alias/app-resource-encryption-key" --overwrite > /dev/null;

echo "### ssmの登録が完了しました。 ###"
