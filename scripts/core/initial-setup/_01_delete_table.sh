#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/utils.sh
check_env $1 $2
source $SCRIPTDIR/env/$1.env
export RELEASE_ENV=$2

$SCRIPTDIR/_port_forward.sh

echo "### flywayでDBの初期構築を行います。 ###"
flyway_folder=../../../../dcbg-dcjpy-core-db-migration/
RESULT=$(${flyway_folder}/scripts/cleanMigrate.sh 2>&1)
count=$(echo $RESULT | grep -o "BUILD SUCCESSFUL" | wc -l)
if [ $count != 3 ]; then
    echo "flyway処理の設定値が想定外のため処理を中断します。"
    echo -e "$RESULT"
    exit 1
fi
echo "### DBの初期構築が完了しました。 ###"

$SCRIPTDIR/_disconnect_port_forward.sh
