#!/bin/bash

DIR=$(dirname "$0")
cd $DIR

# ディレクトリを作成して、必要なファイルをコピー
mkdir ./id-resolver
# コピーするファイルのリストを変数に格納
files="../id-resolver/Dockerfile \
        ../id-resolver/id_resolver.py \
        ../id-resolver/README.md \
        ../id-resolver/requirements.txt"

# コピー先のディレクトリ
destination="./id-resolver"

# ファイルをコピー
cp $files $destination

# signature-toolsをzip化
zip -r id-resolver.zip id-resolver/

# 後始末
rm -fR ./id-resolver
