#!/bin/bash

# Core 初期構築スクリプト
# 1. プロバイダの登録
# 2. トークンの登録
# 3. イシュアの登録 (FinZone のみ)
# 4. バリデータの登録

ZONE_TYPE=$1
ADMIN_ID=$2
BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

source ${BASE}/env/${ZONE_TYPE}.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi

echo "CORE 初期構築を開始します"

# Core Idp Mock より access_token を取得する
function fetch_access_token() {
  local client_id=$1
  local access_token=$(curl -Ss -X GET http://localhost:${CORE_COGNITO_PORT}/token/${client_id})

  echo "${access_token}"
}

A_ACCESS_TOKEN=$(fetch_access_token "${ADMIN_ID}")

# プロバイダの取得
RESULT=$(curl -Ss -H "Authorization: ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
  -X GET ${CORE_BASE_URL}/providers)
PROVIDER_ID=$(echo ${RESULT} | jq -r ".provider_id")

# プロバイダが未登録の場合、登録する
if [ ${PROVIDER_ID} = null ]; then
  REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
  RESULT=$(
    curl -Ss -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST ${CORE_BASE_URL}/providers -d @- <<EOF
{
  "request_id" : "${REQUEST_ID}",
  "zone_name" : "${ZONE_NAME}"
}
EOF
  )
  PROVIDER_ID=$(echo ${RESULT} | jq -r ".provider_id")

  if [ ${PROVIDER_ID} = null ]; then
    echo "プロバイダ登録 : 失敗しました"
    exit 1
  fi
  echo "プロバイダを登録しました。"
fi

# プロバイダ アイデンティティ作成
REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
RESULT=$(curl -Ss -H "Authorization: ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
  -X POST ${CORE_BASE_URL}/identities/providers/${PROVIDER_ID} -d @- <<EOF
{
  "request_id":"${REQUEST_ID}"
}
EOF
)

PROVIDER_CLIENT_ID=$(echo ${RESULT} | jq -r ".client_id")
PROVIDER_CLIENT_SECRET=$(echo ${RESULT} | jq -r ".client_secret")

if [ ${PROVIDER_CLIENT_ID} = null ]; then
  echo "プロバイダ アイデンティティ作成 : 失敗しました"
  exit 1
fi
echo "プロバイダ アイデンティティを作成しました。"

P_ACCESS_TOKEN=$(fetch_access_token ${PROVIDER_CLIENT_ID})

# トークンの取得
RESULT=$(curl -Ss -H "Authorization: ${P_ACCESS_TOKEN}" -H "Content-Type: application/json" \
  -X GET $CORE_BASE_URL/tokenSpecs)
TOKEN_ID=$(echo ${RESULT} | jq -r ".token_id")

if [ ${TOKEN_ID} = null ]; then
  # トークンの作成
  REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
  RESULT=$(curl -Ss -H "Authorization: ${P_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST $CORE_BASE_URL/tokenSpecs -d @- <<EOF
{
  "request_id":"${REQUEST_ID}",
  "name":"DCJPY",
  "symbol":"DCJPY"
}
EOF
)
fi

if [ "${ZONE_TYPE}" = "fin" ]; then
  # イシュア作成
  REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
  RESULT=$(curl -Ss -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST $CORE_BASE_URL/issuers -d @- <<EOF
{
  "request_id" : "${REQUEST_ID}",
  "bank_code" : "${BANK_CODE}",
  "name" : "${ISSUER_NAME}"
}
EOF
  )

  ISSUER_ID=$(echo ${RESULT} | jq -r ".issuer_id")
  if [ ${ISSUER_ID} = null ]; then
    echo "イシュア作成 : 失敗しました"
    exit 1
  fi
  echo "イシュアを作成しました"

  # イシュアアイディンティティ作成
  REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
  RESULT=$(curl -sS -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST ${CORE_BASE_URL}/identities/issuers/${ISSUER_ID} -d @- <<EOF
{
  "request_id" : "${REQUEST_ID}"
}
EOF
  )
  ISSUER_CLIENT_ID=$(echo ${RESULT} | jq -r ".client_id")
  ISSUER_CLIENT_SECRET=$(echo ${RESULT} | jq -r ".client_secret")

  if [ ${ISSUER_CLIENT_ID} = null ]; then
    echo "イシュア アイデンティティ作成 : 失敗しました"
    exit 1
  fi
  echo "イシュア アイデンティティを作成しました"
fi

# バリデータ作成
REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")

if [ "${ZONE_TYPE}" = "fin" ]; then
  RESULT=$(curl -Ss -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST ${CORE_BASE_URL}/validators -d @- <<EOF
    {"request_id":"${REQUEST_ID}", "name":"${VALIDATOR_NAME}", "issuer_id": "${ISSUER_ID}"}
EOF
  )
else
  RESULT=$(curl -Ss -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST ${CORE_BASE_URL}/validators -d @- <<EOF
    {"request_id":"${REQUEST_ID}", "name":"${VALIDATOR_NAME}"}
EOF
  )
fi

VALIDATOR_ID=$(echo ${RESULT} | jq -r ".validator_id")
if [ ${VALIDATOR_ID} = null ]; then
  echo "バリデータ作成 : 失敗しました"
  exit 1
fi
echo "バリデータを作成しました"

# バリデータ アイデンティティ作成
REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
if [ "${ZONE_TYPE}" = "fin" ]; then
  RESULT=$(curl -Ss -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST ${CORE_BASE_URL}/identities/validators/${VALIDATOR_ID} -d @- <<EOF
{
  "request_id":"${REQUEST_ID}",
  "issuer_id": "${ISSUER_ID}"
}
EOF
  )
else
  RESULT=$(curl -Ss -H "Authorization: Bearer ${A_ACCESS_TOKEN}" -H "Content-Type: application/json" \
    -X POST ${CORE_BASE_URL}/identities/validators/${VALIDATOR_ID} -d @- <<EOF
{
  "request_id":"${REQUEST_ID}"
}
EOF
  )
fi

VALIDATOR_CLIENT_ID=$(echo ${RESULT} | jq -r ".client_id")
VALIDATOR_CLIENT_SECRET=$(echo ${RESULT} | jq -r ".client_secret")
if [ ${VALIDATOR_CLIENT_ID} = null ]; then
  echo "バリデータ アイデンティティ作成 : 失敗しました"
  exit 1
fi
echo "バリデータ アイデンティティを作成しました"

echo ""
echo "CORE 初期構築が完了しました"
echo ""
echo "========================================================================"
echo " プロバイダ"
echo "    PROVIDER_ID             : ${PROVIDER_ID}"
echo "    PROVIDER_CLIENT_ID      : ${PROVIDER_CLIENT_ID}"
echo "    PROVIDER_CLIENT_SECRET  : ${PROVIDER_CLIENT_SECRET}"
echo ""
if [ "${ZONE_TYPE}" = "fin" ]; then
  echo " イシュア"
  echo "    ISSUER_ID               : ${ISSUER_ID}"
  echo "    ISSUER_CLIENT_ID        : ${ISSUER_CLIENT_ID}"
  echo "    ISSUER_CLIENT_SECRET    : ${ISSUER_CLIENT_SECRET}"
  echo ""
fi
echo " バリデータ"
echo "    VALIDATOR_ID            : ${VALIDATOR_ID}"
echo "    VALIDATOR_CLIENT_ID     : ${VALIDATOR_CLIENT_ID}"
echo "    VALIDATOR_CLIENT_SECRET : ${VALIDATOR_CLIENT_SECRET}"
echo "========================================================================"
echo ""
