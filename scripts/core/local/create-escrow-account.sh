#!/bin/bash
# FinZoneでEscrowアカウントの登録とValidatorに紐づくアカウントを作成します
set -e

# 引数チェック
if [ $# -ne 1 ]; then
    echo '[引数なし] ./create-escrow-account.sh $VALIDATOR_CLIENT_ID'
    exit 9
fi

V_CLIENT_ID=$1

BASE=$(
  cd $(dirname "$0") || exit
  pwd
)

# contract-ibcがクローンされているかの確認
IBC_PATH="${BASE}/../../../../dcbg-dcjpy-contract"
# ディレクトリが存在するかをチェック
if [ ! -d "$IBC_PATH" ]; then
  echo "dcbg-dcf-devopt-toolsと同じ階層にdcbg-dcjpy-contractをcloneしてから実行してください"
  exit 1
fi

source ${BASE}/env/fin.env

# .env ファイルが存在する場合のみ、上書き更新する
if [ -f ${BASE}/env/.env ]; then
  source ${BASE}/env/.env
fi

#バリデータのACCESS_TOKENを取得
V_ACCESS_TOKEN=$(curl -Ss -X GET http://localhost:${CORE_COGNITO_PORT}/token/${V_CLIENT_ID})

#CoreAPIでEscrowアカウントの作成を行う
echo "EscrowAccount作成用のAPI実行1"
REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
ESCROW_ACCOUNT_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer $V_ACCESS_TOKEN" \
  $CORE_BASE_URL/accounts -d @- <<EOF
{"request_id":"${REQUEST_ID}", "account_id": "${ESCROW_ACCOUNT_ID1}", "account_name": "EscrowAccount"}
EOF
)
echo ${ESCROW_ACCOUNT_RESULT} | jq .

echo "EscrowAccount作成用のAPI実行2"
REQUEST_ID="request-"$(date +"%Y%m%d-%H%M%S")
ESCROW_ACCOUNT_RESULT=$(curl -sS -X POST -H "Content-Type: application/json" -H "Authorization: Bearer $V_ACCESS_TOKEN" \
  $CORE_BASE_URL/accounts -d @- <<EOF
{"request_id":"${REQUEST_ID}", "account_id": "${ESCROW_ACCOUNT_ID2}", "account_name": "EscrowAccount"}
EOF
)
echo ${ESCROW_ACCOUNT_RESULT} | jq .

#IBCディレクトリへ移動
echo "IBCContractへEscrowAccountの登録"
cd $IBC_PATH
npm install --silent

#IBCコントラクトにEscrowアカウントを登録する
npx hardhat registerEscrowAcc --network localFin \
  --key-admin ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80 \
  --src-zone-id 3000 \
  --dst-zone-id 3001 \
  --escrow-account ${ESCROW_ACCOUNT_ID1}

npx hardhat registerEscrowAcc --network localFin \
  --key-admin ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80 \
  --src-zone-id 3000 \
  --dst-zone-id 3002 \
  --escrow-account ${ESCROW_ACCOUNT_ID2}
