#!/bin/bash

# 引数チェック
if [ $# -ne 1 ]; then
    echo "[引数なし] ./initialize-core.sh < fin | biz >"
    exit 9
fi
ZONE_TYPE=$1

./initialize-core-entity-create.sh $ZONE_TYPE "admin_id1"

if [ "${ZONE_TYPE}" = "biz" ]; then
  echo "このまま3002のzoneを作成しますか、続行する場合はyを入力してください"
  echo -n " -> "
  read INPUT_STR
  if [ "$INPUT_STR" = "y" ]; then
      ./initialize-core-entity-create.sh $ZONE_TYPE "admin_id2"
  fi
fi
