#!/bin/bash
# 正常系テストのうち、発行者無効化とバリデータ無効化を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 1 ]; then
    echo "[引数なし] ./11_normal5-fin.sh [issuer_client_id]"
    exit 9
fi

# ACCESS_TOKENの取得
I_CLIENT_ID=$1

# issuer_idとvalidator_idをidentity_authoritiesから取得
ISSUER_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select issuer_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $ISSUER_ID = null ]; then
    echo "ERROR:ISSUER_IDの取得に失敗しました"
    exit 9
fi
VALIDATOR_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:VALIDATOR_IDの取得に失敗しました"
    exit 9
fi

echo "----- 11_normal5-fin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "23-1 発行者の無効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/issuers/$ISSUER_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false}")
echo $RESPONSE
if [ "$ISSUER_ID" != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "22-4 発行者取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
# 2021-12-15時点では、issuerのenabledがfalseの場合、コントラクトがエラーを返す使用なので、判定をコメントアウト
#if [ "$ISSUER_ID" != $(echo $RESPONSE | jq -r ".issuer_id") ] || [ "false" != $(echo $RESPONSE | jq -r ".enabled") ]; then
#    echo "ERROR:失敗しました"
#    exit 1
#fi
echo ""

echo "23-2 発行者の有効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/issuers/$ISSUER_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}")
echo $RESPONSE
if [ "$ISSUER_ID" != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "43-1 バリデータの無効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false}")
echo $RESPONSE
if [ "$VALIDATOR_ID" != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

sleep 10

echo "41-4 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "$VALIDATOR_ID" != $(echo $RESPONSE | jq -r ".validator_id") ] || [ "false" != $(echo $RESPONSE | jq -r ".enabled") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "43-2 バリデータの有効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}")
echo $RESPONSE
if [ "$VALIDATOR_ID" != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 11_normal5-fin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
