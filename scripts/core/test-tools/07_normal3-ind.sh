#!/bin/bash
# 正常系テストのうち、送金〜転送を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 8 ]; then
    echo "[引数なし] ./07_normal3-ind.sh [validator_client_id] [validator_client_secret] [account_id1] [account_id1のsk_o] [account_id1のinfo] [account_id2] [account_id2のsk_o] [account_id2のinfo]"
    exit 9
fi

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# ACCESS_TOKENの取得
V_CLIENT_ID=$1
V_CLIENT_SECRET=$2
V_ACCESS_TOKEN=$(curl -Ss -u $V_CLIENT_ID:$V_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $V_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi

ACCOUNT_ID1=$3
SK_O1=$4
INFO1=$5
ACCOUNT_ID2=$6
SK_O2=$7
INFO2=$8
echo ACCOUNT_ID1:$ACCOUNT_ID1
echo SK_O1:$SK_O1
echo INFO1:$INFO1
echo ACCOUNT_ID2:$ACCOUNT_ID2
echo SK_O2:$SK_O2
echo INFO2:$INFO2

TO_REGION_ID=3000

# validator_idをidentity_authoritiesから取得
VALIDATOR_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$V_CLIENT_ID'"))
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:VALIDATOR_IDの取得に失敗しました"
    exit 9
fi

echo "----- 07_normal3-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "42-3 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "送金許可用アカウント署名作成"
APPROVE_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/approve_signature.sh "$SK_O1" "$ACCOUNT_ID1" "$ACCOUNT_ID2" 100000 | grep "account signature:" | sed -r 's/^account signature: //'); echo "$APPROVE_ACCOUNT_SIGNATURE"
if [ -z "$APPROVE_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "59 送金許可設定前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID1/approve/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"spender_account_id": "'$ACCOUNT_ID2'","approve_amount": 100000,"account_signature":"'$APPROVE_ACCOUNT_SIGNATURE'","info":"'$INFO1'"}')
echo $RESPONSE
echo ""

echo "10 送金許可設定"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID1/approve -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"spender_account_id": "'$ACCOUNT_ID2'","approve_amount": 100000,"account_signature":"'$APPROVE_ACCOUNT_SIGNATURE'","info":"'$INFO1'"}')
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".owner_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "11 送金許可取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".owner_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "8-1 取引一覧照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "送金用アカウント署名作成"
TRANSFER_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/transfer_signature.sh "$SK_O1" "$ACCOUNT_ID1" "$ACCOUNT_ID1" "$ACCOUNT_ID2" 10000 | grep "account signature:" | sed -r 's/^account signature: //'); echo "$TRANSFER_ACCOUNT_SIGNATURE"
if [ -z "$TRANSFER_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "56 送金前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\": \"$ACCOUNT_ID1\",\"from_account_id\": \"$ACCOUNT_ID1\",\"to_account_id\": \"$ACCOUNT_ID2\",\"transfer_amount\": 10000, \"account_signature\": \"$TRANSFER_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".send_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "37 送金(送金用1→送金用2)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\": \"$ACCOUNT_ID1\",\"from_account_id\": \"$ACCOUNT_ID1\",\"to_account_id\": \"$ACCOUNT_ID2\",\"transfer_amount\": 10000, \"account_signature\": \"$TRANSFER_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
TX1=$(echo $RESPONSE | jq -r ".transaction_hash")
if [ $TX1 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "2-4 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "8-2 取引一覧照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1?offset=0\&limit=100\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3001\&region_id_sort=desc\&date_sort=desc -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "9-1 取引照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1/txhash/$TX1 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $TX1 != $(echo $RESPONSE | jq -r ".transaction_hash") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "9-2 取引照会(送金用2)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2/txhash/$TX1 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $TX1 != $(echo $RESPONSE | jq -r ".transaction_hash") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "転送用アカウント署名作成"
EXCHANGE_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/exchange_signature.sh "$SK_O2" "$ACCOUNT_ID2" "$TO_REGION_ID" 10000 | grep "account signature:" | sed -r 's/^account signature: //'); echo "$EXCHANGE_ACCOUNT_SIGNATURE"
if [ -z "$EXCHANGE_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "57 転送前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID2\",\"to_region_id\": \"$TO_REGION_ID\",\"exchange_amount\": 10000, \"account_signature\": \"$EXCHANGE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO2\"}")
echo $RESPONSE
TX5=$(echo $RESPONSE | jq -r ".account_id")
if [ $TX5 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "36 転送"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID2\",\"to_region_id\": \"$TO_REGION_ID\",\"exchange_amount\": 10000, \"account_signature\": \"$EXCHANGE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO2\"}")
echo $RESPONSE
TX5=$(echo $RESPONSE | jq -r ".transaction_hash")
if [ $TX5 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "3-2 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID2 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "8-3 取引一覧照会"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2?limit=2  -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "9-3 取引照会"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2/txhash/$TX5 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $TX5 != $(echo $RESPONSE | jq -r ".transaction_hash") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 07_normal3-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
