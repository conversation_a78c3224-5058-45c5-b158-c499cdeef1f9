#!/bin/bash
# このファイルをコピーしてファイル名を env.sh に変更の上、各パラメータを設定してください

export TOKEN_URL=https://dcbg-dcf-dev.auth.ap-northeast-1.amazoncognito.com/oauth2/token
echo "TOKEN_URL:$TOKEN_URL"
export BASE_URL=https://dev.dc-labo.com
echo "BASE_URL=$BASE_URL"
export DB=dcbg-dcf-dev.cluster-c3swqgm1n2fm.ap-northeast-1.rds.amazonaws.com
echo "DB:$DB"
export DB_PORT=5432
echo "DB_PORT:$DB_PORT"
export DB_USER=Administrator
echo "DB_USER:$DB_USER"
export DB_NAME=postgres
echo "DB_NAME:$DB_NAME"
export PGPASSWORD=password
echo "PGPASSWORD:$PGPASSWORD"
export KEY_ADMIN=21a803d6e0b661ce61ef97e975de8737390ac03ddd0dbd0d6ac2b17b2c78dd05
echo "KEY_ADMIN:$KEY_ADMIN"
export TRUFFLE_NETWORK=main
echo "TRUFFLE_NETWORK:$TRUFFLE_NETWORK"
export CONTRACT_PATH=
echo "CONTRACT_PATH:$CONTRACT_PATH"
export A_CLIENT_ID=3f7gkk8h5gebkhfg9u6p264oud
echo "A_CLIENT_ID:$A_CLIENT_ID"
export A_CLIENT_SECRET=1cdkfqv2tj68ldqbfg5guqoufj5hoapa7qaqro9h7q0bjmskmi92
echo "A_CLIENT_SECRET:$A_CLIENT_SECRET"
A_ACCESS_TOKEN=$(curl -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
export A_ACCESS_TOKEN
echo "A_ACCESS_TOKEN:$A_ACCESS_TOKEN"
export SIGNATURE_TOOLS=../signature-tools
echo "SIGNATURE_TOOLS:$SIGNATURE_TOOLS"
