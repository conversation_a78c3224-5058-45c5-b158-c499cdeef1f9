#!/bin/bash

echo "----- semi-normal-preparation start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
echo ""
DIR=$(dirname "$0")
cd $DIR

TOKEN_URL=https://dcbg-dcf-dev.auth.ap-northeast-1.amazoncognito.com/oauth2/token
BASE_URL=https://dev.dc-labo.com
A_CLIENT_ID=3f7gkk8h5gebkhfg9u6p264oud
A_CLIENT_SECRET=1cdkfqv2tj68ldqbfg5guqoufj5hoapa7qaqro9h7q0bjmskmi92
A_ACCESS_TOKEN=$(curl -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
SIGNATURE_TOOLS=../signature-tools
SLEEP_SEC=8

# issuer1,validator1の作成
RESPONSE=$(curl -Ss -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"issuer-name1"}')
ISSUER_ID1=$(echo $RESPONSE | jq -r ".issuer_id")
if [ -z "$ISSUER_ID1" ]; then
    echo "ERROR:発行者1の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID1 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
I_CLIENT_ID1=$(echo $RESPONSE | jq -r ".client_id")
I_CLIENT_SECRET1=$(echo $RESPONSE | jq -r ".client_secret")
if [ -z "$I_CLIENT_ID1" ]; then
    echo "ERROR:発行者1のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator-name1\",\"issuer_id\":\"$ISSUER_ID1\"}")
VALIDATOR_ID1=$(echo $RESPONSE | jq -r ".validator_id")
if [ -z "$VALIDATOR_ID1" ]; then
    echo "ERROR:バリデータ1の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID1 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID1\"}")
V_CLIENT_ID1=$(echo $RESPONSE | jq -r ".client_id")
V_CLIENT_SECRET1=$(echo $RESPONSE | jq -r ".client_secret")
if [ -z "$V_CLIENT_ID1" ]; then
    echo "ERROR:バリデータ1のアイデンティティ作成に失敗しました"
    exit 1
fi
I_ACCESS_TOKEN1=$(curl -Ss -u $I_CLIENT_ID1:$I_CLIENT_SECRET1 -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ -z "$I_ACCESS_TOKEN1" ]; then
    echo "ERROR:バリデータ1のアクセストークン取得に失敗しました"
    exit 1
fi
echo "ISSUER_ID=$ISSUER_ID1"
echo "I_CLIENT_ID=$I_CLIENT_ID1"
echo "I_CLIENT_SECRET=$I_CLIENT_SECRET1"
echo "VALIDATOR_ID=$VALIDATOR_ID1"
echo "V_CLIENT_ID=$V_CLIENT_ID1"
echo "V_CLIENT_SECRET=$V_CLIENT_SECRET1"
echo ""

# issuer2,validator2の作成
RESPONSE=$(curl -Ss -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"issuer-name2"}')
ISSUER_ID2=$(echo $RESPONSE | jq -r ".issuer_id")
if [ -z "$ISSUER_ID2" ]; then
    echo "ERROR:発行者2の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID2 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
I_CLIENT_ID2=$(echo $RESPONSE | jq -r ".client_id")
I_CLIENT_SECRET2=$(echo $RESPONSE | jq -r ".client_secret")
if [ -z "$I_CLIENT_ID2" ]; then
    echo "ERROR:発行者2のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator-name2\",\"issuer_id\":\"$ISSUER_ID2\"}")
VALIDATOR_ID2=$(echo $RESPONSE | jq -r ".validator_id")
if [ -z "$VALIDATOR_ID2" ]; then
    echo "ERROR:バリデータ2の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID2 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID2\"}")
V_CLIENT_ID2=$(echo $RESPONSE | jq -r ".client_id")
V_CLIENT_SECRET2=$(echo $RESPONSE | jq -r ".client_secret")
if [ -z "$V_CLIENT_ID2" ]; then
    echo "ERROR:バリデータ2のアイデンティティ作成に失敗しました"
    exit 1
fi
I_ACCESS_TOKEN2=$(curl -Ss -u $I_CLIENT_ID2:$I_CLIENT_SECRET2 -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ -z "$I_ACCESS_TOKEN2" ]; then
    echo "ERROR:バリデータ2のアクセストークン取得に失敗しました"
    exit 1
fi
echo "ISSUER_ID2=$ISSUER_ID2"
echo "I_CLIENT_ID2=$I_CLIENT_ID2"
echo "I_CLIENT_SECRET2=$I_CLIENT_SECRET2"
echo "VALIDATOR_ID2=$VALIDATOR_ID2"
echo "V_CLIENT_ID2=$V_CLIENT_ID2"
echo "V_CLIENT_SECRET2=$V_CLIENT_SECRET2"
echo ""

# issuer3の作成
RESPONSE=$(curl -Ss -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"issuer-name3"}')
ISSUER_ID3=$(echo $RESPONSE | jq -r ".issuer_id")
if [ -z "$ISSUER_ID3" ]; then
    echo "ERROR:発行者3の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID3 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
I_CLIENT_ID3=$(echo $RESPONSE | jq -r ".client_id")
I_CLIENT_SECRET3=$(echo $RESPONSE | jq -r ".client_secret")
if [ -z "$I_CLIENT_ID3" ]; then
    echo "ERROR:発行者3のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X PUT $BASE_URL/issuers/$ISSUER_ID3/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":false}")
if [ $ISSUER_ID3 != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:発行者3の無効化に失敗しました"
    exit 1
fi
echo "# 無効な発行者"
echo "ISSUER_ID3=$ISSUER_ID3"
echo "I_CLIENT_ID3=$I_CLIENT_ID3"
echo "I_CLIENT_SECRET3=$I_CLIENT_SECRET3"
echo ""

# アカウントをまとめて作成
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID1=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID1" ]; then
    echo "ERROR:アカウント1の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN2" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID2=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID2" ]; then
    echo "ERROR:アカウント2の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID3=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID3" ]; then
    echo "ERROR:アカウント3の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID4=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID4" ]; then
    echo "ERROR:アカウント4の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID5=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID5" ]; then
    echo "ERROR:アカウント5の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID6=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID6" ]; then
    echo "ERROR:アカウント6の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID7=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID7" ]; then
    echo "ERROR:アカウント7の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID8=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID8" ]; then
    echo "ERROR:アカウント8の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID9=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID9" ]; then
    echo "ERROR:アカウント9の作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{}')
ACCOUNT_ID10=$(echo $RESPONSE | jq -r ".account_id")
if [ -z "$ACCOUNT_ID10" ]; then
    echo "ERROR:アカウント10の作成に失敗しました"
    exit 1
fi

# アカウントのアイデンティティをまとめて作成
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID1 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント1のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID2 -H "Authorization: $I_ACCESS_TOKEN2" -H "Content-Type: application/json")
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント2のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID3 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID3 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント3のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID4 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント4のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID5 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID5 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント5のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID6 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID6 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント6のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID7 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID7 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント7のアイデンティティ作成に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID8 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID8 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント8のアイデンティティ作成に失敗しました"
    exit 1
fi
## アカウント9はアイデンティティ作成しない
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID10 -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
if [ $ACCOUNT_ID10 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント10のアイデンティティ作成に失敗しました"
    exit 1
fi

# アカウントのワンタイムキーをまとめて取得
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O1=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO1=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O1" ]; then
    echo "ERROR:アカウント1のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID2/security -H "Authorization: $I_ACCESS_TOKEN2" -H "Content-Type: application/json")
SK_O2=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO2=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O2" ]; then
    echo "ERROR:アカウント2のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID3/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O3=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO3=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O3" ]; then
    echo "ERROR:アカウント3のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID4/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O4=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO4=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O4" ]; then
    echo "ERROR:アカウント4のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID5/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O5=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO5=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O5" ]; then
    echo "ERROR:アカウント5のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID6/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O6=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO6=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O6" ]; then
    echo "ERROR:アカウント6のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID7/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O7=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO7=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O7" ]; then
    echo "ERROR:アカウント7のワンタイムキー取得に失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID8/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O8=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO8=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O8" ]; then
    echo "ERROR:アカウント8のワンタイムキー取得に失敗しました"
    exit 1
fi
## アカウント9はワンタイムキーを取得しない
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID10/security -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json")
SK_O10=$(echo "$RESPONSE" | jq -r ".sk_o")
INFO10=$(echo "$RESPONSE" | jq -r ".info")
if [ -z "$SK_O10" ]; then
    echo "ERROR:アカウント10のワンタイムキー取得に失敗しました"
    exit 1
fi

# validator1のアカウントで、残高も送金履歴もあり
# validator2のアカウントで、残高も送金履歴もあり
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID1\",\"mint_amount\": 100000}")
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント1のトークン発行に失敗しました"
    exit 1
fi
sleep $SLEEP_SEC
TRANSFER_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/transfer_signature.sh "$SK_O1" "$ACCOUNT_ID1" "$ACCOUNT_ID1" "$ACCOUNT_ID2" 1000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$TRANSFER_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:アカウント1のtransferのアカウント署名作成で失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"send_account_id\": \"$ACCOUNT_ID1\",\"from_account_id\": \"$ACCOUNT_ID1\",\"to_account_id\": \"$ACCOUNT_ID2\",\"transfer_amount\": 1000, \"account_signature\": \"$TRANSFER_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
TXHASH1=$(echo $RESPONSE | jq -r ".transaction_hash")
TXHASH2=$TXHASH1
if [ -z "$TXHASH1" ]; then
    echo "ERROR:アカウント1からアカウント2の送金で失敗しました"
    exit 1
fi

# 取引のキャンセルテスト用アカウント
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID10\",\"mint_amount\": 100000}")
if [ $ACCOUNT_ID10 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント10のトークン発行に失敗しました"
    exit 1
fi
MINT_CANCEL_HASH=$(echo $RESPONSE | jq -r ".transaction_hash")
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID10\",\"burn_amount\": 100000}")
if [ $ACCOUNT_ID10 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント10のトークン償却に失敗しました"
    exit 1
fi
BURN_CANCEL_HASH=$(echo $RESPONSE | jq -r ".transaction_hash")
INVALID_CANCEL_HASH="0x0000000000000000000000000000000000000000000000000000000000000000"

echo "# validator1のアカウントで、残高も送金履歴もあり"
echo "ACCOUNT_ID=$ACCOUNT_ID1"
echo "SK_O=$SK_O1"
echo "INFO=$INFO1"
echo "TXHASH=$TXHASH1"
echo ""
echo "# validator2のアカウントで、残高も送金履歴もあり"
echo "ACCOUNT_ID2=$ACCOUNT_ID2"
echo "SK_O2=$SK_O2"
echo "INFO2=$INFO2"
echo "TXHASH2=$TXHASH2"
echo ""
echo "# 取引のキャンセルテスト用アカウント"
echo "ACCOUNT_ID10=$ACCOUNT_ID10"
echo "SK_O10=$SK_O10"
echo "INFO10=$INFO10"
echo "MINT_CANCEL_HASH=$MINT_CANCEL_HASH"
echo "BURN_CANCEL_HASH=$BURN_CANCEL_HASH"
echo "INVALID_CANCEL_HASH=$INVALID_CANCEL_HASH"
echo ""

# validator1の無効のアカウント
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID3/enabled -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"enabled\":false,\"reason_code\":10000}")
if [ $ACCOUNT_ID3 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント3の無効化に失敗しました"
    exit 1
fi

echo "# validator1の無効のアカウント"
echo "ACCOUNT_ID3=$ACCOUNT_ID3"
echo "SK_O3=$SK_O3"
echo "INFO3=$INFO3"
echo ""

# validator1の本人未確認のアカウント
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID4/identified -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"identified\":false}")
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント4の本人確認で失敗しました"
    exit 1
fi

echo "# validator1の本人未確認のアカウント"
echo "ACCOUNT_ID4=$ACCOUNT_ID4"
echo "SK_O4=$SK_O4"
echo "INFO4=$INFO4"
echo ""

# validator1のアカウントで、残高も送金履歴もあり
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID5\",\"mint_amount\": 10000}")
if [ $ACCOUNT_ID5 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント5のトークン発行に失敗しました"
    exit 1
fi
sleep $SLEEP_SEC
TRANSFER_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/transfer_signature.sh "$SK_O5" "$ACCOUNT_ID5" "$ACCOUNT_ID5" "$ACCOUNT_ID1" 2000 | grep "account signature:" | sed -r 's/^account signature: //')
if [ -z "$TRANSFER_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:アカウント5のtransferのアカウント署名作成で失敗しました"
    exit 1
fi
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"send_account_id\": \"$ACCOUNT_ID5\",\"from_account_id\": \"$ACCOUNT_ID5\",\"to_account_id\": \"$ACCOUNT_ID1\",\"transfer_amount\": 2000, \"account_signature\": \"$TRANSFER_ACCOUNT_SIGNATURE\",\"info\":\"$INFO5\"}")
TXHASH5=$(echo $RESPONSE | jq -r ".transaction_hash")
if [ -z "$TXHASH5" ]; then
    echo "ERROR:アカウント5からアカウント1の送金で失敗しました"
    exit 1
fi

echo "# validator1のアカウントで、残高も送金履歴もあり"
echo "ACCOUNT_ID5=$ACCOUNT_ID5"
echo "SK_O5=$SK_O5"
echo "INFO5=$INFO5"
echo "TXHASH5=$TXHASH5"
echo ""

# validator1のアカウントで、共通領域の残高なし、付加領域の残高あり
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID6\",\"mint_amount\": 6000}")
if [ $ACCOUNT_ID6 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント6のトークン発行に失敗しました"
    exit 1
fi

echo "# validator1のアカウントで、共通領域の残高なし、付加領域の残高あり"
echo "ACCOUNT_ID6=$ACCOUNT_ID6"
echo "SK_O6=$SK_O6"
echo "INFO6=$INFO6"
echo "# この後、付加領域でアカウント受付申込、付加領域のアカウント作成、共通領域で6000のexchange実施が必要"
echo ""

# validator1のアカウントで、共通領域の残高あり、付加領域の残高なし
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID7\",\"mint_amount\": 7000}")
if [ $ACCOUNT_ID7 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント7のトークン発行に失敗しました"
    exit 1
fi

echo "# validator1のアカウントで、共通領域の残高あり、付加領域の残高なし"
echo "ACCOUNT_ID7=$ACCOUNT_ID7"
echo "SK_O7=$SK_O7"
echo "INFO7=$INFO7"
echo "# この後、付加領域でアカウント受付申込、付加領域のアカウント作成が必要"
echo ""

# validator1の解約済みのアカウント
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID8/terminated -H "Authorization: $I_ACCESS_TOKEN1" -H "Content-Type: application/json" -d '{"terminated": true}')
if [ $ACCOUNT_ID8 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:アカウント8の解約に失敗しました"
    exit 1
fi

echo "# validator1の解約済アカウント"
echo "ACCOUNT_ID8=$ACCOUNT_ID8"
echo "SK_O8=$SK_O8"
echo "INFO8=$INFO8"
echo ""

# validator1のアカウントアイデンティティ未作成のアカウント
echo "# validator1のアカウントアイデンティティ未作成アカウント"
echo "ACCOUNT_ID9=$ACCOUNT_ID9"
echo ""

# ACCOUNT_SIGNATUREとINFOのバリデーションテスト用環境変数
echo "# ACCOUNT_SIGNATUREとINFOのバリデーションテスト用環境変数"
echo "SHORT_ACCOUNT_SIGNATURE=0x8644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51"
echo "LONG_ACCOUNT_SIGNATURE=0x8644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51ba"
echo "NO_PREFIX_ACCOUNT_SIGNATURE=008644e4b426f3f43784ef8ef3fbff486b9c1e4dd45c493f7b5727dc6a057e1cac6c697de741b63af6549cc071e98a88c182d8d100f7b9c6154aadd93b339c7cb51b"
echo "SHORT_INFO=0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b0000000000000000000000000000000000000000000000000000000000000"
echo "LONG_INFO=0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b0000000000000000000000000000000000000000000000000000000000000000"
echo "NO_PREFIX_INFO=00000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103d3567ab5db5350169d43b8cc49783cd584341d078bf950bfbb2b06f7b7c428d200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103b37291376263cd3d1e5b14d84ac9d3950b52f17b9756320f406a2d2ff80c3e9b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041c56a6381fed45fb83747ea118816a4930bf1d9ab541d437045fa4089541e8337416d21be9019b1a95fdadce8713f965e433759d12bef36571769861da9dc26371b000000000000000000000000000000000000000000000000000000000000000"
echo ""

echo "----- semi-normal-preparation end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
