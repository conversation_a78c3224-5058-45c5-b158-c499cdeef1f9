#!/bin/bash

CASE_CNT=1
case_echo () {
  echo "$CASE_CNT $1"
  ((CASE_CNT++))
}

echo "----- semi-normal start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
echo ""
DIR=$(dirname "$0")
cd $DIR

LOG_FILE=semi-normal-result.txt
cp /dev/null $LOG_FILE
exec 1>>$LOG_FILE

# 準正常系テストに使用する環境変数

TOKEN_URL=https://dcbg-dcf-dev.auth.ap-northeast-1.amazoncognito.com/oauth2/token
BASE_URL=https://dev.dc-labo.com

A_CLIENT_ID=3f7gkk8h5gebkhfg9u6p264oud
A_CLIENT_SECRET=1cdkfqv2tj68ldqbfg5guqoufj5hoapa7qaqro9h7q0bjmskmi92
A_ACCESS_TOKEN=$(curl -Ss -u $A_CLIENT_ID:$A_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')

PROVIDER_ID=100084d2260a5d224b9faba68167d9127b42
P_CLIENT_ID=5426dmhqaq6hcm32t13srka1pe
P_CLIENT_SECRET=ih7s1tgg5vqnqhk3blv8uiee32ne7ao4od1d437pi5p2qqbcmtn
P_ACCESS_TOKEN=$(curl -Ss -u $P_CLIENT_ID:$P_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')

TOKEN_ID=3001
TO_REGION_ID=3001

###--- ここから貼り付け ---###

ISSUER_ID=20002da186cc73474638a79a00f091cce9f0
I_CLIENT_ID=46usalpglru1u3a2979c6ujg29
I_CLIENT_SECRET=1m2gls9b8i1u6qgphnl1v1ol2oe421tlgb0q1o817i6cr3gvu6il
VALIDATOR_ID=8000ce71de4cf58347c59d489b793fcbedd2
V_CLIENT_ID=46usalpglru1u3a2979c6ujg29
V_CLIENT_SECRET=1m2gls9b8i1u6qgphnl1v1ol2oe421tlgb0q1o817i6cr3gvu6il

ISSUER_ID2=2000a10770214e9647e88775483a38d91f11
I_CLIENT_ID2=n67eulfqvp2iifn1hl95pe4h6
I_CLIENT_SECRET2=a6lkfu9d2jetvnm17mrvrejhhekbodpq66pmjqqtp88jit0g3kg
VALIDATOR_ID2=8000c8cc5c2321c14d97b644b6272bd66971
V_CLIENT_ID2=n67eulfqvp2iifn1hl95pe4h6
V_CLIENT_SECRET2=a6lkfu9d2jetvnm17mrvrejhhekbodpq66pmjqqtp88jit0g3kg

# 無効な発行者
ISSUER_ID3=2000bf6a152e974341a994ea765a1f6b2d4c
I_CLIENT_ID3=70bkp08afoho53314sju03dha9
I_CLIENT_SECRET3=1bo4v4p30al3besopubm8po389t705cv6pqn560fconsa249gcp7

# validator1のアカウントで、残高も送金履歴もあり
ACCOUNT_ID=6000c2503d4434ee41688f9aca4634b8890e
SK_O=5d69396454b8fbfca23c733c4f07d0b7733ebe274ee8ba89fab7eabdce4bfc5f
INFO=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000000000000000210381169cf3811527421eefdb3b7737f1c425388fc8ab7372ec9c091eeff428576a0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000210249051b9f263f60fa719f155c0ec6f9ba30550d4f985491dc6dd5e2c31b35eb40000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041a4f1fd8b946b49f62187c73dd78a6cbd510756453c878b30b0faf38689159a93471729e03c88e4023a06ff6f7397ba8b6f6b328137402a9f5c94309fb9be5ed11b00000000000000000000000000000000000000000000000000000000000000
TXHASH=0x8f9be37c492fac61e101f704572a7a26fc8606002c2806e07a7117d605b1639b

# validator2のアカウントで、残高も送金履歴もあり
ACCOUNT_ID2=600000ecbfa033324fc0a5d3221869dd0376
SK_O2=8b571726596e4807128c91a107f82d896fc48a05a28e877138a70560564b3a17
INFO2=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff00000000000000000000000000000000000000000000000000000000000001400000000000000000000000000000000000000000000000000000000000000021020ed54ab250758f7c612d4e8da95da6d211341082a5cabd74bfb3209cfb58b8d600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103355951e690c5b126db8865066c3c5563c684c317fa96fb1d5d502414bf028ddd00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004197bbcea84903552b9af08e61d65d14f22447775f7fe763bce8e2b008389fb3bf7ebd58e5b206a72839667ec3c44f6ea2d64460e65aabc295590a08fc7b08e1b01c00000000000000000000000000000000000000000000000000000000000000
TXHASH2=0x8f9be37c492fac61e101f704572a7a26fc8606002c2806e07a7117d605b1639b

# validator1の無効のアカウント
ACCOUNT_ID3=6000a0d94d825c304fd693744cfc2fd11844
SK_O3=f2f01e6ac91f2e9431d2aeeec673c1d50e200e9201ebbf7817688bc7550c8013
INFO3=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002102ce24b7ff5cb54c70e274bad21f18f0fc61946cea9fc38a7b5860fc2878b32467000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021032cb6dbf7f3aa18722492529bfc3c1c3211f5634b00784427411c9e962e885c2b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041181d665660ca1f256e62ede5c8237143a1e234ca3d83094e1d2e8c2ee3c6acb63639c2d9ae6371ae130ef704d8e8c40c369d3549f3183cecfdbe1b0cad41f5081b00000000000000000000000000000000000000000000000000000000000000

# validator1の本人未確認のアカウント
ACCOUNT_ID4=60003bc485ff8f714c6c80b849430e0c185a
SK_O4=089beac608cd236ffe17a1d2fe2e08d3831210718c8a4e51b7caa4465142467b
INFO4=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002102672811751f75549719f73458e6d5a6ca066b26e89facc71952b4abb9506aa6f500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103ea0eea32a3c903dfe6b58ddecdf1220ec1cdd4959d69e0c443a683d6f24bdc54000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041fc814d95e7983485049f4c2cdb720d8b071b3b9684acc2cd48add78d5db289611f3c907a8618e8d937e53ea52382bb93d1b4b8503b3ead831aad9e4a6048c1161b00000000000000000000000000000000000000000000000000000000000000

# validator1のアカウントで、残高も送金履歴もあり
ACCOUNT_ID5=6000ae7575c40a044482aeb21a78c6c2d783
SK_O5=46aab8655f295e94f7cc17470f3d53c73e82939b06df883b3142b71039facca9
INFO5=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002102ab952ed6a75946f7dfdc3db39c46b1f7ea0e9ac24fd23eb9537809cb6673948400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103e1160ef375622998f6e029d59d42fcc0ca96c0f6ea14d27fca7422976aa4c1eb000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041666319188af49d3678b1384f27a210e2a7e87203332d49e91670a4a3b26411bf1e4370c61703f2d21527ca931701ade4c2d395a3bcc201bd16633e80477db1771c00000000000000000000000000000000000000000000000000000000000000
TXHASH5=0xe41f9a5b17a8e7265f1c0a28d57d3053731266498ed1e5d364fba3a5935ac990

# validator1のアカウントで、共通領域の残高なし、付加領域の残高あり
ACCOUNT_ID6=6000403f3db4af0f4b15b9e65ac9028135e3
SK_O6=f808b669b544f9ff2daa89438332a9bcc0bc9b2a55410a9be1cd9d13c3d37b90
INFO6=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103343f4f682ef2b74abc4fafef09fcd6f590a1b89fdf41a9fe64cd9fb551d59498000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021036dad8741246731f8d96ed285f0f39ef1774b84acf9682653773229817ad55cb8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041fec0813906d8fa44666231e3a0a957d5ca766de745b2d69162b8876f5acbb3ff765c158170fa2ac3116628217ed9ee6a795b6a2c910a14c3292e0bcdd94da96c1c00000000000000000000000000000000000000000000000000000000000000
# この後、付加領域でアカウント受付申込、付加領域のアカウント作成、共通領域で6000のexchange実施が必要

# validator1のアカウントで、共通領域の残高あり、付加領域の残高なし
ACCOUNT_ID7=60003f556798a7fc44e89cac03740aa9f37b
SK_O7=86154c2aa7775e417b2f674f4e0781ea6b8074be7b60aa4e704bc0deb2cd4520
INFO7=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff00000000000000000000000000000000000000000000000000000000000001400000000000000000000000000000000000000000000000000000000000000021025efc8a8bcadfca7953b48431c27e9be6541250c4d62b9b1515746aaf7b7e1ab800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103d7ca970e453502a1901550de8c543ab8b1f94b0329b1e57aa134dd3ae60916f2000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041a722f09f188b46e233c22aa0117c925f429e3caecff1b00e86ae0f5d22fd740873a601aaab109f99b60af8040cccc82f53c28ce56668a8ecaa2140b5615e7a6b1c00000000000000000000000000000000000000000000000000000000000000
# この後、付加領域でアカウント受付申込、付加領域のアカウント作成が必要

# validator1の解約済アカウント
ACCOUNT_ID8=600062a15872953842049f9d9984e065734e
SK_O8=3efa64eb6af06e7aab113af3c239d337916eb21b66df89e6db9e3527818c6c0c
INFO8=000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002103db256e4efed028e51474876b500c6c02d9591364dd4e822ff847ea80ec8ef4b20000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000210358ad46603e67a753dbe4683b67be34443f3a3c9f0e1eae03beeab7ee166b08b3000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041053f8fc64f298f2b0e4e3664646cd359584941df508efc2fbeb7f3ba0cd8b6082e6108f3e7c8eae8b5fbbcf18171a924ceb0a7076629261935abdff1e4288a6e1c00000000000000000000000000000000000000000000000000000000000000

# validator1のアカウントアイデンティティ未作成アカウント
ACCOUNT_ID9=600094290f0c43ff4db5a316feb2be8afe1f

# 取引のキャンセルテスト用
ACCOUNT_ID10=
SK_O10=
INFO10=
MINT_CANCEL_HASH=
BURN_CANCEL_HASH=
INVALID_CANCEL_HASH=

###--- ここまで貼り付け ---###

# 以下、固定の変数
I_ACCESS_TOKEN=$(curl -Ss -u $I_CLIENT_ID:$I_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
V_ACCESS_TOKEN=$I_ACCESS_TOKEN

# アカウント署名
ACCOUNT_SIGNATURE=0x45d07d96d25b80201d6ce376d86dd9b862ea4620f1eca46867d775ffcae82ff03e779b67d89f32c439f222c879c81325dae9e4aa00218adc73580d533a55aa701c
# 誤ったアカウント署名
INVALID_ACCOUNT_SIGNATURE=0x00d07d96d25b80201d6ce376d86dd9b862ea4620f1eca46867d775ffcae82ff03e779b67d89f32c439f222c879c81325dae9e4aa00218adc73580d533a55aa701c
# 誤ったINFO
INVALID_INFO=0x000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e00000000000000000000000000000000000000000000000000de0b6b3a763ffff0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000002102089e536261767047d4038592d87f254ff0f4b79538c6b2e2a42d5c22c6d8d88300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002103f247dcbf6c0c457a9a9bce79df446cabf29db1aa78b2babeb17d72b58669dfa600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008430786338313835636434653438636261316532636534353933323933336262363535316262353935336163623865616363386463353465333964663366306636383334313232306632353166393963646361313665323035616134363231333363313362396635376338356433326535373938313330393266623136653730383237316300000000000000000000000000000000000000000000000000000000
case_echo "/account/{account_id} アカウント取得 account_idがコントラクトに存在しない"
curl $BASE_URL/accounts/12345 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/account/{account_id} アカウント取得 account_idが半角英数以外"
curl $BASE_URL/accounts/\!\:@ -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/account/{account_id} アカウント取得 URLにセミコロンが含まれる"
curl $BASE_URL/accounts/\; -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/account/{account_id} アカウント取得 validatorと紐付いていないaccount_idを指定"
curl $BASE_URL/accounts/$ACCOUNT_ID2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/account/{account_id} アカウント取得 権限違い"
curl $BASE_URL/accounts/$ACCOUNT_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント一覧取得 limit<1"
curl $BASE_URL/accounts?limit=-1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント一覧取得 limit>100"
curl $BASE_URL/accounts?limit=101 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント一覧取得 offsetが-1"
curl $BASE_URL/accounts?offset=-1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント一覧取得 offsetが1,000,000,000,000"
curl $BASE_URL/accounts?offset=************* -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント一覧取得 権限違い"
curl $BASE_URL/accounts?limit=10 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 burn_limit< 0"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":-1,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 burn_limit>999,999,999,999"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":*************,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 daily_limit< 0"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":-1}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 daily_limit>999,999,999,999,999"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":*************000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 exchange_limit< 0"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":-1,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 exchange_limit>999,999,999,999"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":*************,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/accounts -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 mint_limit< 0"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":-1,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 mint_limit>999,999,999,999"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":*************,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 transfer_limit< 0"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":-1,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 transfer_limit>999,999,999,999"
curl -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":*************,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 権限違い"
curl -X POST $BASE_URL/accounts -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts アカウント作成 付加領域"
curl -X POST $BASE_URL/accounts -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts?offset={offset}&limit={limit} アカウント一覧取得 identity_authoritiesからValidatorIdが取得できない"
curl $BASE_URL/accounts?offset=0\&limit=20 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id} アカウント取得 identity_authoritiesからValidatorIdが取得できない"
curl $BASE_URL/accounts/$ACCOUNT_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 account_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/accounts/12345/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 account_idが解約済みの状態(解約済みにしてから行う)"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID8/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 account_idが半角英数以外"
curl -X PUT $BASE_URL/accounts/\!\:@/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 data項目無し"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 identity_authoritiesからValidatorIdが取得できない"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 validatorと紐付いていないaccount_idを指定"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 リクエストボディなし"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性更新 権限違い"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性取得 account_idがコントラクトに存在しない"
curl -X GET $BASE_URL/accounts/12345/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性取得 account_idが半角英数以外"
curl -X GET $BASE_URL/accounts/\!\:@/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性取得 identity_authoritiesからValidatorIdが取得できない"
curl -X GET $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性取得 validatorと紐付いていないaccount_idを指定"
curl -X GET $BASE_URL/accounts/$ACCOUNT_ID2/attribute -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/attribute アカウント属性取得 権限違い"
curl -X GET $BASE_URL/accounts/$ACCOUNT_ID/attribute -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"attribute1\":\"aaa\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/balances 残高取得 account_idがコントラクトに存在しない"
curl -X GET $BASE_URL/accounts/12345/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/balances 残高取得 account_idが半角英数以外"
curl -X GET $BASE_URL/accounts/\!\:@/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/balances 残高取得 identity_authoritiesからIssuerIdが取得できない"
curl -X GET $BASE_URL/accounts/$ACCOUNT_ID/balances -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/balances 残高取得 issuerと紐付いていないaccount_idを指定"
curl -X GET $BASE_URL/accounts/$ACCOUNT_ID2/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/cumulativereset 累積限度額初期化 account_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/accounts/12345/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/cumulativereset 累積限度額初期化 account_idが解約済みの状態(解約済みにしてから行う)"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID8/cumulativereset -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/cumulativereset 累積限度額初期化 account_idが半角英数以外"
curl -X PUT $BASE_URL/accounts/\!\:@/cumulativereset -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/cumulativereset 累積限度額初期化 issuerと紐付いていないaccount_idを指定"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/cumulativereset -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/cumulativereset 累積限度額初期化 権限違い"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/cumulativereset -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 account_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/accounts/12345/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 account_idが半角英数以外"
curl -X PUT $BASE_URL/accounts/\!\:@/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 enabled項目なし"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":true, \"reason_code\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 identity_authoritiesからIssuerIdが取得できない"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 issuerと紐付いていないaccount_idを指定"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 reason_codeが100000以上"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":100000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 reason_codeが10000未満"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":9999}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 reason_code項目がない"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 リクエストボディなし"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/enabled -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/enabled アカウントを有効化／無効化 権限違い"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/enabled -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true, \"reason_code\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 account_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/accounts/12345/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 account_idが解約済みの状態(解約済みにしてから行う)"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID8/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 account_idが半角英数以外"
curl -X PUT $BASE_URL/accounts/\!\:@/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 identified項目なし"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 identity_authoritiesからIssuerIdが取得できない"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/identified -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 issuerと紐付いていないaccount_idを指定"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 リクエストボディなし"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/identified アカウントの本人確認ステータスの更新 権限違い"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/identified -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/security ワンタイムキー取得 account_idがコントラクトに存在しない"
curl $BASE_URL/accounts/12345/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/security ワンタイムキー取得 account_idが半角英数以外"
curl $BASE_URL/accounts/\!\:@/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/security ワンタイムキー取得 issuerと紐付いていないaccount_idを指定"
curl $BASE_URL/accounts/$ACCOUNT_ID2/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/security ワンタイムキー取得 アカウントアイデンティティ未作成"
curl $BASE_URL/accounts/$ACCOUNT_ID9/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/security ワンタイムキー取得 権限違い"
curl $BASE_URL/accounts/$ACCOUNT_ID/security -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/accounts/12345/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_idが半角英数以外"
curl -X POST $BASE_URL/accounts/\!\:@/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_signatureが16進数以外"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"1\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_signature項目なし"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_signature項目空"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 INFOが誤り"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 info項目なし"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 info項目空"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 アカウント署名が誤り"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 リクエストボディなし"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 共通領域"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 権限違い"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/accounts/12345/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_idが半角英数以外"
curl -X POST $BASE_URL/accounts/\!\:@/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_signatureが16進数以外"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"1\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_signature項目なし"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_signature項目空"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 INFOが誤り"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 info項目なし"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 info項目空"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 アカウント署名が誤り"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 リクエストボディなし"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 共通領域"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 権限違い"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 (共通領域) 共通領域の残高=0 and 付加領域の残高>0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID6/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 (共通領域)共通領域の残高>0 and 付加領域の残高=0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID7/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 (付加領域)付加領域の残高>0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID6/terminated -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 account_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/accounts/12345/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 account_idが半角英数以外"
curl -X PUT $BASE_URL/accounts/\!\:@/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 identity_authoritiesからValidatorIdが取得できない"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID6/terminated -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 issuerと紐付いていないaccount_idを指定"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"terminated\": true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 terminated項目がない"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID6/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/terminated アカウント解約 リクエストボディなし"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID6/terminated -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 account_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/accounts/12345/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 account_idが半角英数以外"
curl -X PUT $BASE_URL/accounts/\!\:@/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 burn_limit< 0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":-1,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 burn_limit>999,999,999,999"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":*************,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 daily_limit< 0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":-1}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 daily_limit>999,999,999,999,999"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":*************000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 exchange_limit< 0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":-1,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 exchange_limit>999,999,999,999"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":*************,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 mint_limit< 0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":-1,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 mint_limit>999,999,999,999"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":*************,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 transfer_limit< 0"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":-1,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 transfer_limit>999,999,999,999"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":*************,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 validatorと紐付いていないaccount_idを指定"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 リクエストが空"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{}' -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/tokenlimit 限度額更新 権限違い"
curl -X PUT $BASE_URL/accounts/$ACCOUNT_ID/tokenlimit -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":1000,"exchange_limit":2000,"mint_limit":3000,"burn_limit":4000,"daily_limit":5000}' -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) argsが空"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Provider\",\"method\": \"getToken\",\"args\": {}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) args項目がない"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Provider\",\"method\": \"getToken\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) contract_nameがコントラクトに存在しない"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"12345\",\"method\": \"getToken\",\"args\": {\"providerId\":\"$PROVIDER_ID\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) contract_nameが空"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"\",\"method\": \"getToken\",\"args\": {\"providerId\":\"$PROVIDER_ID\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) contract_name項目がない"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"method\": \"getToken\",\"args\": {\"providerId\":\"$PROVIDER_ID\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) methodがコントラクトに存在しない"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Provider\",\"method\": \"12345\",\"args\": {\"providerId\":\"$PROVIDER_ID\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) methodが空"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Provider\",\"method\": \"\",\"args\": {\"providerId\":\"$PROVIDER_ID\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) method項目がない"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Provider\",\"args\": {\"providerId\":\"$PROVIDER_ID\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/call デジタル通貨のコントラクト実行(取得) リクエストボディが設定されていない"
curl -X POST $BASE_URL/contracts/call -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) argsが空"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Issuer\",\"method\": \"addIssuer\",\"args\": {}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) args項目がない"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Issuer\",\"method\": \"addIssuer\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) contract_nameがコントラクトに存在しない"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"12345\",\"method\": \"addIssuer\",\"args\": {\"\name\":\"$NAME_HEX\",\"deadline\":\"$DEADLINE\",\"signature\":\"$SIGNATURE\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) contract_nameが空"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"\",\"method\": \"addIssuer\",\"args\": {\"\name\":\"$NAME_HEX\",\"deadline\":\"$DEADLINE\",\"signature\":\"$SIGNATURE\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) contract_name項目がない"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"method\": \"addIssuer\",\"args\": {\"\name\":\"$NAME_HEX\",\"deadline\":\"$DEADLINE\",\"signature\":\"$SIGNATURE\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) methodがコントラクトに存在しない"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Issuer\",\"method\": \"12345\",\"args\": {\"\name\":\"$NAME_HEX\",\"deadline\":\"$DEADLINE\",\"signature\":\"$SIGNATURE\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) methodが空"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Issuer\",\"method\": \"\",\"args\": {\"\name\":\"$NAME_HEX\",\"deadline\":\"$DEADLINE\",\"signature\":\"$SIGNATURE\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) method項目がない"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"contract_name\": \"Issuer\",\"args\": {\"\name\":\"$NAME_HEX\",\"deadline\":\"$DEADLINE\",\"signature\":\"$SIGNATURE\"}}" -w '\n%{http_code}\n' 
echo ""
case_echo "/contracts/send デジタル通貨のコントラクト実行(更新) リクエストボディが設定されていない"
curl -X POST $BASE_URL/contracts/send -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/accounts/{account_id} アカウントアイデンティティ作成 account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/identities/accounts/12345 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/accounts/{account_id} アカウントアイデンティティ作成 account_idが半角英数以外"
curl -X POST $BASE_URL/identities/accounts/\!\:@ -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/accounts/{account_id} アカウントアイデンティティ作成 identity_authoritiesからIssuerIdが取得できない"
curl -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/accounts/{account_id} アカウントアイデンティティ作成 issuerと紐付いていないaccount_idを指定"
curl -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/issuers/{issuer_id} 発行者アイデンティティ作成 identity_authoritiesからAdminIdが取得できない"
curl -X POST $BASE_URL/identities/issuers/$ISSUER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"validator_id\":\"$VALIDATOR_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/issuers/{issuer_id} 発行者アイデンティティ作成 issuer_idがコントラクトに存在しない"
curl -X POST $BASE_URL/identities/issuers/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/issuers/{issuer_id} 発行者アイデンティティ作成 issuer_idが半角英数以外"
curl -X POST $BASE_URL/identities/issuers/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/issuers/{issuer_id} 発行者アイデンティティ作成 issuer_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/identities/issuers/$ISSUER_ID3 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/issuers/{issuer_id} 発行者アイデンティティ作成 権限違い"
curl -X POST $BASE_URL/identities/issuers/$ISSUER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/providers/{provider_id} プロバイダーアイデンティティ作成 identity_authoritiesからAdminIdが取得できない"
curl -X POST $BASE_URL/identities/providers/$PROVIDER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/providers/{provider_id} プロバイダーアイデンティティ作成 provider_idがコントラクトに存在しない"
curl -X POST $BASE_URL/identities/providers/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/providers/{provider_id} プロバイダーアイデンティティ作成 provider_idが半角英数以外"
curl -X POST $BASE_URL/identities/providers/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/providers/{provider_id} プロバイダーアイデンティティ作成 権限違い"
curl -X POST $BASE_URL/identities/providers/$PROVIDER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 identity_authoritiesからAdminIdが取得できない"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 validator_idがコントラクトに存在しない"
curl -X POST $BASE_URL/identities/validators/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 validator_idが半角英数以外"
curl -X POST $BASE_URL/identities/validators/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 共通領域 issuer_idがコントラクトに存在しない"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID3 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"12345\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 共通領域 issuer_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID3 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID3\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 共通領域 issuer_id項なし"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID3 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 共通領域 issuer_id項目空"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID3 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 権限違い"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/identities/validators/{validator_id} バリデータアイデンティティ作成 付加領域 issuer_id項目がある"
curl -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers 発行者作成 identity_authoritiesからAdminIdが取得できない"
curl -X POST $BASE_URL/issuers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers 発行者作成 nameが33文字"
curl -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"123456789012345678901234567890123\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers 発行者作成 nameが空"
curl -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers 発行者作成 nameにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers 発行者作成 リクエストボディが設定されていない"
curl -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers 発行者作成 権限違い"
curl -X POST $BASE_URL/issuers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 identity_authoritiesからAdminIdが取得できない"
curl $BASE_URL/issuers?offset=0\&limit=20 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 limit<1"
curl $BASE_URL/issuers?offset=0\&limit=0 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 limit>100"
curl $BASE_URL/issuers?offset=0\&limit=101 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 offset<0"
curl $BASE_URL/issuers?offset=-1\&limit=20 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 offset>999,999,999,999"
curl $BASE_URL/issuers?offset=*************\&limit=20 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 offsetが登録済み件数より多い"
curl $BASE_URL/issuers?offset=************\&limit=20 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers?offset={offset}&limit={limit} 発行者一覧取得 権限違い"
curl $BASE_URL/issuers?offset=0\&limit=10 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者取得 identity_authoritiesからAdminId、IssuerIdが取得できない"
curl $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者取得 issuer_idがコントラクトに存在しない"
curl $BASE_URL/issuers/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者取得 issuer_idが半角英数以外"
curl $BASE_URL/issuers/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者取得 権限違い"
curl $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 identity_authoritiesからAdminIdが取得できない"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 issuer_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/issuers/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 issuer_idが半角英数以外"
curl -X PUT $BASE_URL/issuers/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 issuer_idが無効の状態 (無効にしてから行う)"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 nameが空"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 name項目無し"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 リクエストボディなし"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id} 発行者変更 権限違い"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"issuer_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id}/enabled 発行者を有効化／無効化 enabled項目無し"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id}/enabled 発行者を有効化／無効化 identity_authoritiesからAdminIdが取得できない"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID/enabled -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id}/enabled 発行者を有効化／無効化 issuer_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/issuers/12345/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id}/enabled 発行者を有効化／無効化 issuer_idが半角英数以外"
curl -X PUT $BASE_URL/issuers/\!\:@/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id}/enabled 発行者を有効化／無効化 リクエストボディなし"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/issuers/{issuer_id}/enabled 発行者を有効化／無効化 権限違い"
curl -X PUT $BASE_URL/issuers/$ISSUER_ID/enabled -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 3000 <= region_id <= 3999"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\",\"region_id\":2999}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 3000 <= region_id <= 3999"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\",\"region_id\":4000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 identity_authoritiesからAdminIdが取得できない"
curl -X POST $BASE_URL/providers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\",\"region_id\":3000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 nameが33文字"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"123456789012345678901234567890123\",\"region_id\":3000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 nameが空"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\",\"region_id\":3000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 nameにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider name\",\"region_id\":3000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 name項目がない"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"region_id\":3000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 regionが空"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\",\"region_id\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 region項目がない"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 リクエストボディなし"
curl -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー作成 権限違い"
curl -X POST $BASE_URL/providers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\",\"region_id\":3000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー取得 identity_authoritiesからAdminId、ProviderIdが取得できない"
curl $BASE_URL/providers -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers プロバイダー取得 権限違い"
curl $BASE_URL/providers -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 identity_authoritiesからAdminIdが取得できない"
curl -X PUT $BASE_URL/providers/$PROVIDER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 nameが空"
curl -X PUT $BASE_URL/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 name項目無し"
curl -X PUT $BASE_URL/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":\"provider_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 provider_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/providers/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 provider_idが半角英数以外"
curl -X PUT $BASE_URL/providers/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 リクエストボディなし"
curl -X PUT $BASE_URL/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/providers/{provider_id} プロバイダー変更 権限違い"
curl -X PUT $BASE_URL/providers/$PROVIDER_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"provider_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs トークン作成 deposited項目がない"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs トークン作成 identity_authoritiesからProviderIdが取得できない"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"token_name\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 nameが33文字"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"123456789012345678901234567890123\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 nameが空"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 nameにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"token Name\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 name項目がない"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 peg_kindが33文字"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"123456789012345678901234567890123\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 peg_kindが空"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 peg_kindにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"J PY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 peg_kind項目がない"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 symbolが33文字"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"123456789012345678901234567890123\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 symbolが空"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 symbolにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"token Symbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 symbol項目がない"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン作成 リクエストボディが設定されていない"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs トークン作成 権限違い"
curl -X POST $BASE_URL/tokenSpecs -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\",\"deposited\":false}" -w '\n%{http_code}\n'
echo ""
case_echo "/tokenSpecs トークン照会 identity_authoritiesからProviderIdが取得できない"
curl $BASE_URL/tokenSpecs -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs トークン照会 権限違い"
curl $BASE_URL/tokenSpecs -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 identity_authoritiesからProviderIdが取得できない"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"token_name\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 name,symbol,peg_kindがすべて空"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\",\"symbol\":\"\",\"peg_kind\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 nameが33文字"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"123456789012345678901234567890123\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 nameにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"token Name\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 peg_kindが33文字"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"123456789012345678901234567890123\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 peg_kindにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"J PY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 symbolが33文字"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"123456789012345678901234567890123\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 symbolにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"token Symbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 token_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/tokenSpecs/12345 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 token_idが半角英数以外"
curl -X PUT $BASE_URL/tokenSpecs/\!\:@ -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 リクエストボディが設定されていない"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/{token_id} トークン更新 権限違い"
curl -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"tokenName\",\"symbol\":\"tokenSymbol\",\"peg_kind\":\"JPY\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"12345\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 account_idが半角英数以外"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\";@:\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID4\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 account_id項目なし"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 account_id項目空"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 balance<burn_amount"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"burn_amount\":************}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 burn_amountが0"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"burn_amount\":0}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 burn_amountが1,000,000,000,000"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"burn_amount\":*************}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 burn_amount項目がない"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 identity_authoritiesからIssuerIdが取得できない"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"burn_amount\":10}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 リクエストボディなし"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 権限違い"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"burn_amount\":1}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"12345\",\"mint_amount\":1000000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 account_idが半角英数以外"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\";@:\",\"mint_amount\":1000000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID4\",\"mint_amount\":1000000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\",\"mint_amount\":1000000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 account_id項目なし"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"mint_amount\":1000000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 account_id項目空"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"\",\"mint_amount\":1000000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 identity_authoritiesからIssuerIdが取得できない"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"mint_amount\":10}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 mint_amountが0"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"mint_amount\":0}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 mint_amountが1,000,000,000,000"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"mint_amount\":*************}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 mint_amount項目がない"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 リクエストボディなし"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 権限違い"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"mint_amount\":1000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 account_idがコントラクトに存在しない"
curl -X GET $BASE_URL/transactions/accounts/12345?offset=0\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 account_idが半角英数以外"
curl -X GET $BASE_URL/transactions/accounts/\!\:@?offset=0\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 from_dateが/区切り"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=0\&limit=10\&from_date=2021/01/01T00:00:00\&to_date=2020-12-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 from_dateよりto_dateが古い"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=0\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2020-12-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 identity_authoritiesからValidatorIdが取得できない"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=0\&limit=10 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"from_date\":\"2021-01-01T00:00:00\",\"to_date\":\"2022-01-01T00:00:00\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 limit<1"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?limit=0\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 limit>100"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?limit=101\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 offset<0"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=-1\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 offset>999,999,999,999"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=*************\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 offsetが登録済み件数より多い"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=************\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 to_dateが/区切り"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?offset=0\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2020/12/01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 validatorと紐付いていないaccount_idを指定"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2?offset=0\&limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id} 取引一覧照会 権限違い"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID?limit=10\&from_date=2021-01-01T00:00:00\&to_date=2022-01-01T00:00:00 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 account_idがコントラクトに存在しない"
curl -X GET $BASE_URL/transactions/accounts/12345/txhash/$TXHASH -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 account_idが半角英数以外"
curl -X GET $BASE_URL/transactions/accounts/\!\:@/txhash/$TXHASH -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 account_idに関連しないtransaction_hash"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID/txhash/$TXHASH2 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 identity_authoritiesからValidatorIdが取得できない"
curl $BASE_URL/transactions/accounts/$ACCOUNT_ID/txhash/$TXHASH -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 validatorと紐付いていないaccount_idを指定"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2/txhash/$TXHASH -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 権限違い"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID/txhash/$TXHASH -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{account_id}/txhash/{transaction_hash} 取引照会 存在しないtransaction_hash"
curl -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID/txhash/12345 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 account_signatureが16進数以外"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"z\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 account_signature項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 account_signature項目空"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 approve_amount項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 INFOが誤り"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 info項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 info項目空"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 owner_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/accounts/12345/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 owner_account_idが半角英数以外"
curl -X POST $BASE_URL/transactions/accounts/\!\:@/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 owner_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID4/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 owner_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID3/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 spender_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"12345\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 spender_account_idが解約済みの状態(解約済みにしてから行う)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID8\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 spender_account_idが半角英数以外"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \";@:\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 spender_account_id空"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 spender_account_id項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 validatorと紐付いていないowner_account_idを指定"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID2/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 アカウント署名が誤り"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 リクエストボディなし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 権限違い"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 account_signatureが16進数以外"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"z\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 account_signature項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 account_signature項目空"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 approve_amount項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 INFOが誤り"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 info項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 info項目空"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 owner_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/accounts/12345/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 owner_account_idが半角英数以外"
curl -X POST $BASE_URL/transactions/accounts/\!\:@/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 owner_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID4/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 owner_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID3/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 spender_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"12345\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 spender_account_idが解約済みの状態(解約済みにしてから行う)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID8\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 spender_account_idが半角英数以外"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \";@:\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 spender_account_id空"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 spender_account_id項目なし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 validatorと紐付いていないowner_account_idを指定"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID2/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 アカウント署名が誤り"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 リクエストボディなし"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 権限違い"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 identity_authoritiesからValidatorIdが取得できない"
curl $BASE_URL/transactions/accounts/$ACCOUNT_ID/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 owner_account_idがコントラクトに存在しない"
curl $BASE_URL/transactions/accounts/12345/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 owner_account_idが半角英数以外"
curl $BASE_URL/transactions/accounts/\!\:@/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 spender_account_idがコントラクトに存在しない"
curl $BASE_URL/transactions/accounts/$ACCOUNT_ID/spenders/12345/allowance -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 spender_account_idが半角英数以外"
curl $BASE_URL/transactions/accounts/$ACCOUNT_ID/spenders/\!\:@/allowance -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 validatorと紐付いていないowner_account_idを指定"
curl $BASE_URL/transactions/accounts/$ACCOUNT_ID2/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/spenders/{spender_account_id}/allowance 送金許可取得 権限違い"
curl $BASE_URL/transactions/accounts/$ACCOUNT_ID/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"12345\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_idが空"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID4\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_idのデジタル通貨残高<exchange_amount"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":************,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_id項目がない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_signatureが16進数以外"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"12345\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"z\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_signature項目なし"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_signature項目空"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) exchange_amountが0"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":0,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) exchange_amountが1,000,000,000,000"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":*************,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) exchange_amount項目がない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":123,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) INFOが誤り"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_token_id\":\"$TO_TOKEN_ID\",\"exchange_amount\":1000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) info項目なし"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) info項目空"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) to_region_idが空"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) to_region_id項目がない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) アカウント署名が誤り"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":123,\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) リクエストボディなし"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) 権限違い"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"12345\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_idが空"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID4\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID3\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_idのデジタル通貨残高<exchange_amount"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":************,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_id項目がない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_signatureが16進数以外"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"z\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_signature項目なし"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_signature項目空"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 exchange_amountが0"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":0,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 exchange_amountが1,000,000,000,000"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":*************,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 exchange_amount項目がない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 INFOが誤り"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":123,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 info項目なし"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 info項目空"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 to_region_idが空"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 to_region_id項目がない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 アカウント署名が誤り"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":123,\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 リクエストボディなし"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 権限違い"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 account_signatureが16進数以外"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"z\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 account_signature項目なし"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 account_signature項目空"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 from_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"12345\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 from_account_idが空"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 from_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID4\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 from_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID3\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 from_account_idのデジタル通貨残高<transfer_amount"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":**********,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 from_account_id項目がない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 fromとtoが同じ"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 INFOが誤り"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":123,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 info項目なし"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 info項目空"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 misc_value1が0x始まり"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"0xabcd\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 misc_value1の長さが33byte"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"123456789012345678901234567890123\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 misc_value2の長さが33byte"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"123456789012345678901234567890123\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 send_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"12345\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 send_account_idが空"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 send_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID4\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 send_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID3\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 send_account_id項目がない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 to_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"12345\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 to_account_idが空"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 to_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID4\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 to_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID3\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 to_account_id項目がない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 transfer_amountが0"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":0,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 transfer_amountが1,000,000,000,000"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":*************,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 transfer_amount項目がない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 アカウント署名が誤り"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":123,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 リクエストボディが設定されていない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 権限違い"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 account_signatureが16進数以外"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"z\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 account_signature項目なし"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 account_signature項目空"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 from_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"12345\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 from_account_idが空"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 from_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID4\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 from_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID3\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 from_account_idのデジタル通貨残高<transfer_amount"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":**********,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 from_account_id項目がない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 fromとtoが同じ"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 identity_authoritiesからValidatorIdが取得できない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"transfer_amount\":100, \"token_id\": \"$TOKEN_ID\",\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 INFOが誤り"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":123,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INVALID_INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 info項目なし"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 info項目空"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 misc_value1の長さが33byte"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"123456789012345678901234567890123\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 misc_value2が0x始まり"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"0x1234\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 misc_value2の長さが33byte"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"123456789012345678901234567890123\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 send_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"12345\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 send_account_idが空"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 send_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID4\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 send_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID3\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 send_account_id項目がない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 to_account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"12345\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 to_account_idが空"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 to_account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID4\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 to_account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID3\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 to_account_id項目がない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 transfer_amountが0"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":0,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 transfer_amountが1,000,000,000,000"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":*************,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 transfer_amount項目がない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 アカウント署名が誤り"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":123,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$INVALID_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 リクエストボディが設定されていない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 権限違い"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 identity_authoritiesからAdminIdが取得できない"
curl -X POST $BASE_URL/validators -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 nameが33文字"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"123456789012345678901234567890123\",\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 nameが空"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\",\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 nameにASCIIの感嘆符からチルダまでの文字以外が含まれる"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator name\",\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 name項目がない"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 共通領域 issuer_idがコントラクトに存在しない"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\"12345\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 共通領域 issuer_idが空"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 共通領域 issuer_idが半角英数以外"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\";@:\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 共通領域 issuer_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\"$ISSUER_ID3\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 共通領域 issuer_id項目がない"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 権限違い"
curl -X POST $BASE_URL/validators -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\"$ISSUER_ID\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators バリデータ作成 付加領域 issuer_idがある"
curl -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\",\"issuer_id\":\"12345\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators?offset={offset}&limit={limit} バリデータ一覧取得 identity_authoritiesからAdminIdが取得できない"
curl $BASE_URL/validators?offset=0\&limit=20 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators?offset={offset}&limit={limit} バリデータ一覧取得 limit<1"
curl $BASE_URL/validators?offset=0\&limit=0 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators?offset={offset}&limit={limit} バリデータ一覧取得 limit>100"
curl $BASE_URL/validators?offset=0\&limit=101 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators?offset={offset}&limit={limit} バリデータ一覧取得 offset<0"
curl $BASE_URL/validators?offset=-1\&limit=20 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators?offset={offset}&limit={limit} バリデータ一覧取得 offset>999,999,999,999"
curl $BASE_URL/validators?offset=*************\&limit=20 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators?offset={offset}&limit={limit} バリデータ一覧取得 権限違い"
curl $BASE_URL/validators?offset=0\&limit=10 -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n'  
echo ""
case_echo "/validators/{validator_id} バリデータ取得 identity_authoritiesからAdminId、ValidatorIdが取得できない"
curl $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ取得 identity_authoritiesからValidatorIdが取得できない"
curl "$BASE_URL/validators/$VALIDATOR_ID?offset=0&limit=20" -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ取得 validator_idがコントラクトに存在しない"
curl $BASE_URL/validators/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ取得 validator_idが半角英数以外"
curl $BASE_URL/validators/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ取得 権限違い"
curl $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 identity_authoritiesからAdminIdが取得できない"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 nameが空"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 name項目無し"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":\"validator_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 validator_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/validators/12345 -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 validator_idが半角英数以外"
curl -X PUT $BASE_URL/validators/\!\:@ -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 リクエストボディなし"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id} バリデータ変更 権限違い"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator_name\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id}/enabled バリデータを有効化／無効化 enabled項目無し"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"dummy\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id}/enabled バリデータを有効化／無効化 identity_authoritiesからAdminIdが取得できない"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id}/enabled バリデータを有効化／無効化 validator_idがコントラクトに存在しない"
curl -X PUT $BASE_URL/validators/12345/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id}/enabled バリデータを有効化／無効化 validator_idが半角英数以外"
curl -X PUT $BASE_URL/validators/\!\:@/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id}/enabled バリデータを有効化／無効化 リクエストボディなし"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/validators/{validator_id}/enabled バリデータを有効化／無効化 権限違い"
curl -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"enabled\":true}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 misc_value2が0x始まり"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"abc\",\"misc_value2\":\"0xf00\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 misc_value1が0x始まり"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"0xabcdef\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) to_region_idが同じ領域"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":3000,\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 to_region_idが同じ領域"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":3000,\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 infoが16進数以外"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 infoが16進数以外"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 infoが16進数以外"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 infoが16進数以外"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) infoが16進数以外"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 infoが16進数以外"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 infoが16進数以外"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 infoが16進数以外"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"z\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 送金限度額オーバー"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID1\",\"from_account_id\":\"$ACCOUNT_ID1\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10000,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 累積限度額オーバー"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID11\",\"from_account_id\":\"$ACCOUNT_ID11\",\"to_account_id\":\"$ACCOUNT_ID\",\"transfer_amount\":5001,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO11\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 送金限度額オーバー"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID1\",\"from_account_id\":\"$ACCOUNT_ID1\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":10000,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 累積限度額オーバー"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID11\",\"from_account_id\":\"$ACCOUNT_ID11\",\"to_account_id\":\"$ACCOUNT_ID\",\"transfer_amount\":5001,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO11\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) 割当限度額オーバー"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) 累積限度額オーバー"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID11\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":5001,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO11\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 割当限度額オーバー"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID10\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":2001,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO10\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 累積限度額オーバー"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID11\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":5001,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$INFO11\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 交換限度額オーバー"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\",\"mint_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/mint 発行者トークン発行 累積限度額オーバー"
curl -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\",\"mint_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 償却限度額オーバー"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn 発行者トークン償却 累積限度額オーバー"
curl -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID1\",\"burn_amount\":10000}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 issuerと紐付いていないaccount_idを指定"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID2\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 account_idがコントラクトに存在しない"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"12345\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 account_idが半角英数以外"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \";@:\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 account_idが本人未確認の状態 (未確認にしてから行う)"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID4\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 account_idが無効の状態 (無効にしてから行う)"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID3\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 account_id項目なし"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 account_id項目空"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 リクエストボディなし"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 権限違い"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID10\", \"cancel_transaction_hash\": \"$BURN_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 存在しないトランザクションハッシュを指定"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID10\", \"cancel_transaction_hash\": \"$INVALID_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/tokenSpecs/burn/cancel 共通領域コイン償却取消 mintのトランザクションハッシュを指定"
curl -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID10\", \"cancel_transaction_hash\": \"$MINT_CANCEL_HASH\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 account_signatureの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$SHORT_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 account_signatureの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$LONG_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) account_signatureが0x始まりでない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 account_signatureが0x始まりでない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$NO_PREFIX_ACCOUNT_SIGNATURE\",\"info\":\"$INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 infoの桁数相違(桁数不足)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$SHORT_INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 infoの桁数相違(桁数オーバー)"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$LONG_INFO\"}" -w '\n%{http_code}\n'  
echo ""
case_echo "/accounts/{account_id}/synchronous 付加領域のアカウント申込受付 infoが0x始まりでない"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/accounts/{account_id}/synchronous/check 付加領域のアカウント申込受付前確認 infoが0x始まりでない"
curl -X POST $BASE_URL/accounts/$ACCOUNT_ID/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve 送金許可設定 infoが0x始まりでない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/accounts/{owner_account_id}/approve/check 送金許可設定前確認 infoが0x始まりでない"
curl -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID5\",\"approve_amount\":10000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange 利用者のデジタル通貨から他デジタル通貨への振替(交換) infoが0x始まりでない"
curl -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/exchange/check 利用者のデジタル通貨から他デジタル通貨への振替(交換)前確認 infoが0x始まりでない"
curl -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\":\"$ACCOUNT_ID\",\"to_region_id\":\"$TO_REGION_ID\",\"exchange_amount\":1000,\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer 利用者アカウントから他利用者への送金 infoが0x始まりでない"
curl -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n' 
echo ""
case_echo "/transactions/transfer/check 利用者アカウントから他利用者への送金前確認 infoが0x始まりでない"
curl -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\":\"$ACCOUNT_ID\",\"from_account_id\":\"$ACCOUNT_ID\",\"to_account_id\":\"$ACCOUNT_ID2\",\"transfer_amount\":100,\"misc_value1\":\"hoge\",\"misc_value2\":\"foo\",\"account_signature\":\"$ACCOUNT_SIGNATURE\",\"info\":\"$NO_PREFIX_INFO\"}" -w '\n%{http_code}\n'  
echo ""