#!/bin/bash

ENV_DEV="dev"
ENV_SANDBOX_FIN="sandbox-fin"
ENV_SANDBOX_IND="sandbox-ind"
ENV_DCJPY_FIN="dcjpy-fin"
ENV_DCJPY_IND="dcjpy-ind"
ENV_XCHAIN_FIN="xchain-fin"
ENV_XCHAIN_IND="xchain-ind"
ENV_VEGA_FIN="vega-fin"
ENV_VEGA_IND_A="vega-ind-a"
ENV_VEGA_IND_B="vega-ind-b"

ENV_LIST=" $ENV_DEV \
| $ENV_SANDBOX_FIN | $ENV_SANDBOX_IND \
| $ENV_DCJPY_FIN | $ENV_DCJPY_IND \
| $ENV_XCHAIN_FIN | $ENV_XCHAIN_IND \
| $ENV_VEGA_FIN | $ENV_VEGA_IND_A | $ENV_VEGA_IND_B \
"

if [ $# -ne 1 ]; then
    echo "環境名を入力してください"
    echo "USAGE : ./core/test-tools/port_forward.sh ($ENV_LIST)"
    exit
fi

# 環境変数を読み込む
# 他のシェル作成時に環境変数をファイル化する
if [ $1 == "$ENV_DEV" ]; then
  export PRIVATE_KEY=bastion_dev.pem
  export RDS_URL=dcbg-dcf-dev.cluster-c3swqgm1n2fm.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_SANDBOX_FIN" ]; then
  export PRIVATE_KEY=bastion_sandbox.pem
  export RDS_URL=dcbg-dcf-sandbox.cluster-crywzgfrboya.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_SANDBOX_IND" ]; then
  export PRIVATE_KEY=bastion_sandbox-ext.pem
  export RDS_URL=dcbg-dcf-sandbox-ext.cluster-cwlbxumw6dwg.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_DCJPY_FIN" ]; then
  export PRIVATE_KEY=bastion_dcjpy-fin.pem
  export RDS_URL=dcbg-dcf-dcjpy-fin.cluster-ccjlblybrled.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_DCJPY_IND" ]; then
  export PRIVATE_KEY=bastion_dcjpy-ind.pem
  export RDS_URL=dcbg-dcf-dcjpy-ind.cluster-c5tjhrelstte.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_XCHAIN_FIN" ]; then
  export PRIVATE_KEY=bastion_xchain-fin.pem
  export RDS_URL=dcbg-dcf-xchain-fin.cluster-cdixbtrhow7n.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_XCHAIN_IND" ]; then
  export PRIVATE_KEY=bastion_xchain-ind.pem
  export RDS_URL=dcbg-dcf-xchain-ind.cluster-cxmopx9ilsn1.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_VEGA_FIN" ]; then
  export PRIVATE_KEY=bastion_vega-fin.pem
  export RDS_URL=dcbg-dcf-vega-fin.cluster-cktsfocn2q9w.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_VEGA_IND_A" ]; then
  export PRIVATE_KEY=bastion_vega-ind-a.pem
  export RDS_URL=dcbg-dcf-vega-ind-a.cluster-ceu24ghtiay8.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
elif [ $1 == "$ENV_VEGA_IND_B" ]; then
  export PRIVATE_KEY=bastion_vega-ind-b.pem
  export RDS_URL=dcbg-dcf-vega-ind-b.cluster-ccvzyetsn2mk.ap-northeast-1.rds.amazonaws.com
  export DB_PORT=5432
else
  echo "環境名は($ENV_LIST)を指定してください"
  exit 0
fi

export LOCAL_DB_PORT=5432

echo "対象の環境のProfileに変更していることを確認し、続行する場合はyを入力してください"
echo -n " -> "
read INPUT_STR
if [ "$INPUT_STR" != "y" ]; then
    exit 0
fi

# 踏み台のインスタンスIDを取得する
INSTANCE_ID=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=*bastion" --query "Reservations[].Instances[].InstanceId" | grep "i-" | sed 's/^.*"\(.*\)".*$/\1/')

# RDSにポートフォワードする
ssh -i ~/.ssh/$PRIVATE_KEY ec2-user@$INSTANCE_ID -L $LOCAL_DB_PORT:$RDS_URL:$DB_PORT -N &
