#!/bin/bash
# 正常系テストのうち、バリデータの無効化〜有効化を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 1 ]; then
    echo "[引数なし] ./10_normal6-ind.sh [validator_id]"
    exit 9
fi

VALIDATOR_ID=$1

echo "----- 10_normal6-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "43-1 バリデータの無効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"enabled":false}')
echo $RESPONSE | jq .
echo ""

sleep 10

echo "41-4 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq .
echo ""

echo "43-2 バリデータの有効化"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/validators/$VALIDATOR_ID/enabled -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"enabled":true}')
echo $RESPONSE | jq .
echo ""

echo "----- 10_normal6-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
