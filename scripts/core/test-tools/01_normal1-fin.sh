#!/bin/bash
# 正常系テストのうち、発行者作成〜バリデータ作成を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

echo "----- 01_normal1-fin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "21-1 発行者一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "20 発行者作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"issuer-name"}')
echo $RESPONSE
ISSUER_ID=$(echo $RESPONSE | jq -r ".issuer_id")
if [ $ISSUER_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "22-1 発行者取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "21-2 発行者一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "17 発行者のアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
I_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
I_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
if [ $I_CLIENT_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
I_ACCESS_TOKEN=$(curl -Ss -u $I_CLIENT_ID:$I_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $I_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 1
fi
echo ""

echo "identity_authoritiesの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'identity_authorities : issuer_id OK' from identity_authorities where issuer_id='$ISSUER_ID'")
echo ""

echo "external_signersの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'external_signers : issuer_id OK' from external_signers where signer_id='$ISSUER_ID'")
echo ""

echo "46 発行者変更"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"test-issuer"}')
echo $RESPONSE
if [ "test-issuer" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "22-2 発行者取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "test-issuer" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "41-1 バリデータ一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "40 バリデータ作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator-name\",\"issuer_id\":\"$ISSUER_ID\"}")
echo $RESPONSE
VALIDATOR_ID=$(echo $RESPONSE | jq -r ".validator_id")
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "19 バリデータのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"$ISSUER_ID\"}")
echo $RESPONSE
V_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
V_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
if [ $I_CLIENT_ID != $V_CLIENT_ID ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ $I_CLIENT_SECRET != $V_CLIENT_SECRET ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "identity_authoritiesの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'identity_authorities : validator_id OK' from identity_authorities where validator_id='$VALIDATOR_ID'")
echo ""

echo "external_signersの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'external_signers : validator_id OK' from external_signers where signer_id='$VALIDATOR_ID'")
echo ""
sleep 10

echo "42-1 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:issuer_idが設定されていない"
    exit 1
fi
echo ""

echo "41-2 バリデータ一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "47 バリデータ変更"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"test-validator"}')
echo $RESPONSE
if [ "test-validator" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "42-2 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "test-validator" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 01_normal1-fin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo ""
echo "------------"
echo "ISSUER_ID=$ISSUER_ID"
echo "I_CLIENT_ID=$I_CLIENT_ID"
echo "I_CLIENT_SECRET=$I_CLIENT_SECRET"
echo ""
echo "VALIDATOR_ID=$VALIDATOR_ID"
echo "V_CLIENT_ID=$V_CLIENT_ID"
echo "V_CLIENT_SECRET=$V_CLIENT_SECRET"
echo "------------"
echo ""
