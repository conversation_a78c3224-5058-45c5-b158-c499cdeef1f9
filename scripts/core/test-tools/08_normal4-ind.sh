#!/bin/bash
# 正常系テストのうち、アカウント同期を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 5 ]; then
    echo "[引数なし] ./08_normal4-ind.sh [validator_client_id] [validator_client_secret] [account_id4] [account_id4のsk_o] [account_id4のinfo]"
    exit 9
fi

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# ACCESS_TOKENの取得
V_CLIENT_ID=$1
V_CLIENT_SECRET=$2
V_ACCESS_TOKEN=$(curl -Ss -u $V_CLIENT_ID:$V_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $V_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi
ACCOUNT_ID4=$3
SK_O4=$4
INFO4=$5

echo "----- 08_normal4-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "アカウント同期用アカウント署名作成"
SYNCHRONOUS_ACCOUNT_SIGNATURE4=$("$SIGNATURE_TOOLS"/tools/synchronous_signature.sh "$SK_O4" "$ACCOUNT_ID4" | grep "account signature:" | sed -r 's/^account signature: //'); echo "$SYNCHRONOUS_ACCOUNT_SIGNATURE4"
if [ -z "$SYNCHRONOUS_ACCOUNT_SIGNATURE4" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "44-3 アカウント同期"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID4/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SYNCHRONOUS_ACCOUNT_SIGNATURE4\",\"info\":\"$INFO4\"}")
echo $RESPONSE
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 08_normal4-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
