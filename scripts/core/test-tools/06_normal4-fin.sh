#!/bin/bash
# 正常系テストのうち、転送を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 8 ]; then
    echo "[引数なし] ./06_normal4-fin.sh [issuer_client_id] [issuer_client_secret] [account_id1] [account_id1のsk_o] [account_id1のinfo] [account_id2] [account_id2のsk_o] [account_id2のinfo]"
    exit 9
fi

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# ACCESS_TOKENの取得
I_CLIENT_ID=$1
I_CLIENT_SECRET=$2
I_ACCESS_TOKEN=$(curl -Ss -u $I_CLIENT_ID:$I_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $I_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi
ACCOUNT_ID1=$3
SK_O1=$4
INFO1=$5
ACCOUNT_ID2=$6
SK_O2=$7
INFO2=$8
echo ACCOUNT_ID1:$ACCOUNT_ID1
echo SK_O1:$SK_O1
echo INFO1:$INFO1
echo ACCOUNT_ID2:$ACCOUNT_ID2
echo SK_O2:$SK_O2
echo INFO2:$INFO2

TO_REGION_ID=3001

# issuer_idとvalidator_idをidentity_authoritiesから取得
ISSUER_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select issuer_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $ISSUER_ID = null ]; then
    echo "ERROR:ISSUER_IDの取得に失敗しました"
    exit 9
fi
VALIDATOR_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:VALIDATOR_IDの取得に失敗しました"
    exit 9
fi

echo "----- 06_normal4-fin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "22-3 発行者取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "42-3 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:issuer_idが設定されていない"
    exit 1
fi
echo ""

echo "49-2 残高取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "58-1 付加領域のアカウント作成(送金用1)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID1/industry -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"region_id\":$TO_REGION_ID}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "49-3 残高取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "58-2 付加領域のアカウント作成(送金用2)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID2/industry -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"region_id\":$TO_REGION_ID}")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "34-2 トークン発行(送金用1)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID1\",\"mint_amount\": 10000}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "49-4 残高取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "転送用アカウント署名作成"
EXCHANGE_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/exchange_signature.sh "$SK_O1" "$ACCOUNT_ID1" $TO_REGION_ID 10000 | grep "account signature:" | sed -r 's/^account signature: //'); echo "$EXCHANGE_ACCOUNT_SIGNATURE"
if [ -z "$EXCHANGE_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "57 転送前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/exchange/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID1\",\"to_region_id\": $TO_REGION_ID,\"exchange_amount\": 10000, \"account_signature\": \"$EXCHANGE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "36 転送"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/exchange -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID1\",\"to_region_id\": $TO_REGION_ID,\"exchange_amount\": 10000, \"account_signature\": \"$EXCHANGE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
TX5=$(echo $RESPONSE | jq -r ".transaction_hash")
if [ $TX5 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

# シェル実行時のアカウントの状態が、必ずbalance0とは限らないので、シェルで金額のチェックはしない
echo "3-9 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "49-5 残高取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/balances -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "8-6 取引一覧照会"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1\?limit=3  -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "9-3 取引照会"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1/txhash/$TX5 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $TX5 != $(echo $RESPONSE | jq -r ".transaction_hash") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 06_normal4-fin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo ""
echo "------------"
echo "I_ACCESS_TOKEN=$I_ACCESS_TOKEN"
echo "------------"
echo ""