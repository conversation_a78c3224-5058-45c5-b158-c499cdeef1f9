#!/bin/bash
# 正常系テストのうち、アカウント作成〜トークン償却を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 2 ]; then
    echo "[引数なし] ./03_normal3-fin.sh [issuer_client_id] [issuer_client_secret]"
    exit 9
fi

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# ACCESS_TOKENの取得
I_CLIENT_ID=$1
I_CLIENT_SECRET=$2
I_ACCESS_TOKEN=$(curl -Ss -u $I_CLIENT_ID:$I_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $I_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi

# issuer_idとvalidator_idをidentity_authoritiesから取得
ISSUER_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select issuer_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $ISSUER_ID = null ]; then
    echo "ERROR:ISSUER_IDの取得に失敗しました"
    exit 9
fi
VALIDATOR_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$I_CLIENT_ID'"))
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:VALIDATOR_IDの取得に失敗しました"
    exit 9
fi

echo "----- 03_normal3-fin start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "22-3 発行者取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/issuers/$ISSUER_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "42-3 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ $ISSUER_ID != $(echo $RESPONSE | jq -r ".issuer_id") ]; then
    echo "ERROR:issuer_idが設定されていない"
    exit 1
fi
echo ""

echo "1-2 アカウント作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"transfer_limit":0,"exchange_limit":0,"mint_limit":0,"burn_limit":0,"daily_limit":40000}')
echo $RESPONSE
ACCOUNT_ID1=$(echo $RESPONSE | jq -r ".account_id")
if [ $ACCOUNT_ID1 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "50-2 アカウントのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "7-2 アカウントの本人確認"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID1/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "52-1 ワンタイムキー取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "sk_o,info設定"
SK_O1=$(echo "$RESPONSE" | jq -r ".sk_o");INFO1=$(echo "$RESPONSE" | jq -r ".info"); echo "SK_O1=$SK_O1"; echo "INFO1=$INFO1"
if [ -z "$SK_O1" ] || [ -z "$INFO1" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "1-3 アカウント作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
ACCOUNT_ID2=$(echo $RESPONSE | jq -r ".account_id")
if [ $ACCOUNT_ID1 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "50-3 アカウントのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "7-3 アカウントの本人確認"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID2/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "52-2 ワンタイムキー取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID2/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "sk_o,info設定"
SK_O2=$(echo "$RESPONSE" | jq -r ".sk_o");INFO2=$(echo "$RESPONSE" | jq -r ".info"); echo "SK_O2=$SK_O2"; echo "INFO2=$INFO2"
if [ -z "$SK_O2" ] || [ -z "$INFO2" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "1-4 アカウント作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
ACCOUNT_ID4=$(echo $RESPONSE | jq -r ".account_id")
if [ $ACCOUNT_ID4 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "50-4 アカウントのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/accounts/$ACCOUNT_ID4 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "7-4 アカウントの本人確認"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID4/identified -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"identified\":true}")
echo $RESPONSE
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "52-3 ワンタイムキー取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID4/security -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "sk_o,info設定"
SK_O4=$(echo "$RESPONSE" | jq -r ".sk_o");INFO4=$(echo "$RESPONSE" | jq -r ".info"); echo "SK_O4=$SK_O4"; echo "INFO4=$INFO4"
if [ -z "$SK_O4" ] || [ -z "$INFO4" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

sleep 10

echo "送金許可用アカウント署名作成"
APPROVE_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/approve_signature.sh "$SK_O1" "$ACCOUNT_ID1" "$ACCOUNT_ID2" 100000 | grep "account signature:" | sed -r 's/^account signature: //'); echo "$APPROVE_ACCOUNT_SIGNATURE"
if [ -z "$APPROVE_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "59 送金許可設定前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID1/approve/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID2\",\"approve_amount\": 100000,\"account_signature\": \"$APPROVE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".owner_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "10 送金許可設定"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/accounts/$ACCOUNT_ID1/approve -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"spender_account_id\": \"$ACCOUNT_ID2\",\"approve_amount\": 100000,\"account_signature\": \"$APPROVE_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".owner_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "11 送金許可取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1/spenders/$ACCOUNT_ID2/allowance -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".owner_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ 100000 != $(echo $RESPONSE | jq -r ".allowance_amount") ]; then
    echo "ERROR:allowance_amountが違います"
    exit 1
fi
echo ""

echo "8-1 取引一覧照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "34-1 トークン発行(送金用1)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/mint -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID1\",\"mint_amount\": 10000}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "3-6 アカウント取得(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ 10000 != $(echo $RESPONSE | jq -r ".balance") ]; then
    echo "ERROR:balanceが違います"
    exit 1
fi
if [ 10000 != $(echo $RESPONSE | jq -r ".cumulative_amount") ]; then
    echo "ERROR:cumulative_amountが違います"
    exit 1
fi
echo ""

echo "8-2 取引一覧照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1?offset=0\&limit=100\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "55 累積限度額初期化(送金用1)"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID1/cumulativereset -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "3-7 アカウント取得(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ 10000 != $(echo $RESPONSE | jq -r ".balance") ]; then
    echo "ERROR:balanceが違います"
    exit 1
fi
if [ 0 != $(echo $RESPONSE | jq -r ".cumulative_amount") ]; then
    echo "ERROR:cumulative_amountが違います"
    exit 1
fi
echo ""

echo "送金用アカウント署名作成"
TRANSFER_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/transfer_signature.sh "$SK_O1" "$ACCOUNT_ID1" "$ACCOUNT_ID1" "$ACCOUNT_ID2" 10000 | grep "account signature:" | sed -r 's/^account signature: //'); echo "$TRANSFER_ACCOUNT_SIGNATURE"
if [ -z "$TRANSFER_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "56 送金前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/transfer/check -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\": \"$ACCOUNT_ID1\",\"from_account_id\": \"$ACCOUNT_ID1\",\"to_account_id\": \"$ACCOUNT_ID2\",\"transfer_amount\": 10000, \"account_signature\": \"$TRANSFER_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".send_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "37 送金(送金用1→送金用2)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/transactions/transfer -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"send_account_id\": \"$ACCOUNT_ID1\",\"from_account_id\": \"$ACCOUNT_ID1\",\"to_account_id\": \"$ACCOUNT_ID2\",\"transfer_amount\": 10000, \"account_signature\": \"$TRANSFER_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
TX2=$(echo $RESPONSE | jq -r ".transaction_hash")
if [ $TX2 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "2-3 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "8-3 取引一覧照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1?offset=0\&limit=100\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "9-1 取引照会(送金用1)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID1/txhash/$TX2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".from_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "9-2 取引照会(送金用2)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2/txhash/$TX2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".to_account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "33 トークン償却(送金用2)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/burn -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID2\",\"burn_amount\": 10000}")
echo $RESPONSE | jq
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
# 61で用いるハッシュを記録する
CANCEL_HASH=`echo $RESPONSE | jq -r ".transaction_hash"`
echo ""
sleep 20

echo "3-8 アカウント取得(送金用2)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ 0 != $(echo $RESPONSE | jq -r ".balance") ]; then
    echo "ERROR:balanceが違います"
    exit 1
fi
if [ 10000 != $(echo $RESPONSE | jq -r ".cumulative_amount") ]; then
    echo "ERROR:cumulative_amountが違います"
    exit 1
fi
echo ""

echo "8-4 取引一覧照会(送金用2)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2?offset=0\&limit=100\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "61 トークン償却取消(送金用2)"
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs/burn/cancel -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_id\": \"$ACCOUNT_ID2\",\"cancel_transaction_hash\": \"$CANCEL_HASH\"}")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ $CANCEL_HASH != $(echo $RESPONSE | jq -r ".cancel_transaction_hash") ]; then
    echo "ERROR:cancel_transaction_hashが違います"
    exit 1
fi
echo ""
sleep 20

echo "3-9 アカウント取得(送金用2)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID2 -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
if [ 10000 != $(echo $RESPONSE | jq -r ".balance") ]; then
    echo "ERROR:balanceが違います"
    exit 1
fi
if [ 0 != $(echo $RESPONSE | jq -r ".cumulative_amount") ]; then
    echo "ERROR:cumulative_amountが違います"
    exit 1
fi
echo ""

echo "8-5 取引一覧照会(送金用2)"
RESPONSE=$(curl -Ss -X GET $BASE_URL/transactions/accounts/$ACCOUNT_ID2?offset=0\&limit=100\&from_date=2021-01-01T00:00:00\&to_date=2030-01-01T00:00:00\&region_id=3000\&region_id_sort=desc\&date_sort=desc -H "Authorization: $I_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "----- 03_normal3-fin end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
