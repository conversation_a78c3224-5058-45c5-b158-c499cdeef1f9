#!/bin/bash
# 環境初期リリース時のプロバイダ作成とトークン作成を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

echo "----- 00_init-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "25 プロバイダー作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"provider-name","region_id":3001}')
echo $RESPONSE
PROVIDER_ID=$(echo $RESPONSE | jq -r ".provider_id")
if [ $PROVIDER_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "27-1 プロバイダー取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $PROVIDER_ID != $(echo $RESPONSE | jq -r ".provider_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "18 プロバイダーのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
P_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
P_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
if [ $P_CLIENT_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
P_ACCESS_TOKEN=$(curl -Ss -u $P_CLIENT_ID:$P_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $P_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 1
fi
echo ""

echo "identity_authoritiesの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'identity_authorities : provider_id OK' from identity_authorities where provider_id='$PROVIDER_ID'")
echo ""

echo "external_signersの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'external_signers : provider_id OK' from external_signers where signer_id='$PROVIDER_ID'")
echo ""

echo "45 プロバイダー変更"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/providers/$PROVIDER_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"test-provider"}')
echo $RESPONSE
if [ "test-provider" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "27-2 プロバイダー取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/providers -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "test-provider" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "27-3 プロバイダー取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/providers -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "test-provider" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "30 トークン作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"token-name\",\"symbol\":\"test-coin\",\"peg_kind\":\"USD\",\"deposited\":false}")
echo $RESPONSE
TOKEN_ID=$(echo $RESPONSE | jq -r ".token_id")
if [ $TOKEN_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "31-1 トークン照会"
RESPONSE=$(curl -Ss -X GET $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $TOKEN_ID != $(echo $RESPONSE | jq -r ".token_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "32-1 トークン更新"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"test-token\",\"symbol\":\"\",\"peg_kind\":\"\"}")
echo $RESPONSE
if [ "test-token" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "32-2 トークン更新"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\",\"symbol\":\"test-symbol\",\"peg_kind\":\"\"}")
echo $RESPONSE
if [ "test-symbol" != $(echo $RESPONSE | jq -r ".symbol") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "32-3 トークン更新"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/tokenSpecs/$TOKEN_ID -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"\",\"symbol\":\"\",\"peg_kind\":\"JPY\"}")
echo $RESPONSE
if [ "JPY" != $(echo $RESPONSE | jq -r ".peg_kind") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "31-2 トークン照会"
RESPONSE=$(curl -Ss -X GET $BASE_URL/tokenSpecs -H "Authorization: $P_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "test-token" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:nameの更新に失敗しました"
    exit 1
fi
if [ "test-symbol" != $(echo $RESPONSE | jq -r ".symbol") ]; then
    echo "ERROR:symbolの更新に失敗しました"
    exit 1
fi
if [ "JPY" != $(echo $RESPONSE | jq -r ".peg_kind") ]; then
    echo "ERROR:peg_kindの更新に失敗しました"
    exit 1
fi
echo ""

echo "----- 00_init-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
