#!/bin/bash
# 正常系テストのうち、バリデータ作成を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

echo "----- 04_normal1-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "41-1 バリデータ一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "40 バリデータ作成"
RESPONSE=$(curl -Ss -X POST $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"validator-name\"}")
echo $RESPONSE
VALIDATOR_ID=$(echo $RESPONSE | jq -r ".validator_id")
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "19 バリデータのアイデンティティを作成する"
RESPONSE=$(curl -Ss -X POST $BASE_URL/identities/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"issuer_id\":\"\"}")
echo $RESPONSE
V_CLIENT_ID=$(echo $RESPONSE | jq -r ".client_id")
V_CLIENT_SECRET=$(echo $RESPONSE | jq -r ".client_secret")
if [ $V_CLIENT_ID = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "identity_authoritiesの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'identity_authorities : validator_id OK' from identity_authorities where validator_id='$VALIDATOR_ID'")
echo ""

echo "external_signersの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select 'external_signers : validator_id OK' from external_signers where signer_id='$VALIDATOR_ID'")
echo ""
sleep 10

echo "42-1 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "41-2 バリデータ一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "47 バリデータ変更"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"name":"test-validator"}')
echo $RESPONSE
if [ "test-validator" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "42-2 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $A_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ "test-validator" != $(echo $RESPONSE | jq -r ".name") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 04_normal1-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo ""
echo "------------"
echo "VALIDATOR_ID=$VALIDATOR_ID"
echo "V_CLIENT_ID=$V_CLIENT_ID"
echo "V_CLIENT_SECRET=$V_CLIENT_SECRET"
echo "------------"
echo ""
