#!/bin/bash
# 正常系テストのうち、アカウント同期〜送金許可設定を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 8 ]; then
    echo "[引数なし] ./05_normal2-ind.sh [validator_client_id] [validator_client_secret] [account_id1] [account_id1のsk_o] [account_id1のinfo] [account_id2] [account_id2のsk_o] [account_id2のinfo]"
    exit 9
fi

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# ACCESS_TOKENの取得
V_CLIENT_ID=$1
V_CLIENT_SECRET=$2
V_ACCESS_TOKEN=$(curl -Ss -u $V_CLIENT_ID:$V_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $V_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi
ACCOUNT_ID1=$3
SK_O1=$4
INFO1=$5
ACCOUNT_ID2=$6
SK_O2=$7
INFO2=$8
echo ACCOUNT_ID1:$ACCOUNT_ID1
echo SK_O1:$SK_O1
echo INFO1:$INFO1
echo ACCOUNT_ID2:$ACCOUNT_ID2
echo SK_O2:$SK_O2
echo INFO2:$INFO2

# validator_idをidentity_authoritiesから取得
VALIDATOR_ID=$(echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select validator_id from identity_authorities where sub='$V_CLIENT_ID'"))
if [ $VALIDATOR_ID = null ]; then
    echo "ERROR:VALIDATOR_IDの取得に失敗しました"
    exit 9
fi

echo "----- 05_normal2-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "42-3 バリデータ取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/validators/$VALIDATOR_ID -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $VALIDATOR_ID != $(echo $RESPONSE | jq -r ".validator_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "2-1 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "付加領域のアカウント申込受付用アカウント署名作成"
SYNCHRONOUS_ACCOUNT_SIGNATURE=$("$SIGNATURE_TOOLS"/tools/synchronous_signature.sh "$SK_O1" "$ACCOUNT_ID1" | grep "account signature:" | sed -r 's/^account signature: //'); echo "$SYNCHRONOUS_ACCOUNT_SIGNATURE"
if [ -z "$SYNCHRONOUS_ACCOUNT_SIGNATURE" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "60-1 付加領域のアカウント申込受付前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID1/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SYNCHRONOUS_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
ACCOUNT_ID1=$(echo $RESPONSE | jq -r ".account_id")
if [ $ACCOUNT_ID1 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "44-1 付加領域のアカウント申込受付"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID1/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SYNCHRONOUS_ACCOUNT_SIGNATURE\",\"info\":\"$INFO1\"}")
echo $RESPONSE
ACCOUNT_ID1=$(echo $RESPONSE | jq -r ".account_id")
if [ $ACCOUNT_ID1 = null ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "account_attributesの登録結果を確認する"
echo $(psql -h $DB -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "select attribute from account_attributes where account_id='$ACCOUNT_ID1'")
echo ""
sleep 10

echo "3-1 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "2-2 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "5-1 アカウント属性取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/attribute -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "4 アカウント属性更新"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID1/attribute -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"data\":{\"string1\":\"aaa\",\"number1\":123,\"boolean1\":true}}")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "5-2 アカウント属性取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID1/attribute -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID1 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "付加領域のアカウント申込受付用アカウント署名作成"
SYNCHRONOUS_ACCOUNT_SIGNATURE2=$("$SIGNATURE_TOOLS"/tools/synchronous_signature.sh "$SK_O2" "$ACCOUNT_ID2" | grep "account signature:" | sed -r 's/^account signature: //'); echo "$SYNCHRONOUS_ACCOUNT_SIGNATURE2"
if [ -z "$SYNCHRONOUS_ACCOUNT_SIGNATURE2" ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "60-2 付加領域のアカウント申込受付前確認"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID2/synchronous/check -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SYNCHRONOUS_ACCOUNT_SIGNATURE2\",\"info\":\"$INFO2\"}")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "44-2 付加領域のアカウント申込受付"
RESPONSE=$(curl -Ss -X POST $BASE_URL/accounts/$ACCOUNT_ID2/synchronous -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d "{\"account_signature\":\"$SYNCHRONOUS_ACCOUNT_SIGNATURE2\",\"info\":\"$INFO2\"}")
echo $RESPONSE
if [ $ACCOUNT_ID2 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""
sleep 10

echo "2-3 アカウント一覧取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE | jq
echo ""

echo "----- 05_normal2-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
