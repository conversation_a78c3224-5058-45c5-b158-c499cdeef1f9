#!/bin/bash
# 正常系テストのうち、アカウント解約を実施

# 設定ファイルの存在チェック
if [  ! -e "env.sh" ]; then
  echo "env.sh が存在しません。template.env.sh よりコピーして、設定ファイルを用意してください。"
  exit 1
fi

# 環境変数
source ./env.sh

# 引数チェック
if [ $# -ne 3 ]; then
    echo "[引数なし] ./09_normal5-ind.sh [validator_client_id] [validator_client_secret] [account_id4]"
    exit 9
fi

# signature-tools インストール
pushd "$SIGNATURE_TOOLS" || { echo "pushd failure"; exit 1; }
npm install
popd || exit 0;

# ACCESS_TOKENの取得
V_CLIENT_ID=$1
V_CLIENT_SECRET=$2
V_ACCESS_TOKEN=$(curl -Ss -u $V_CLIENT_ID:$V_CLIENT_SECRET -d grant_type=client_credentials $TOKEN_URL | jq -r '.access_token')
if [ $V_ACCESS_TOKEN = null ]; then
    echo "ERROR:ACCESS_TOKENの取得に失敗しました"
    exit 9
fi
ACCOUNT_ID4=$3

echo "----- 09_normal5-ind start" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`

echo "3-3 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID4 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "51 アカウント解約"
RESPONSE=$(curl -Ss -X PUT $BASE_URL/accounts/$ACCOUNT_ID4/terminated -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"terminated": true}')
echo $RESPONSE
if [ $ACCOUNT_ID4 != $(echo $RESPONSE | jq -r ".account_id") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "3-4 アカウント取得"
RESPONSE=$(curl -Ss -X GET $BASE_URL/accounts/$ACCOUNT_ID4 -H "Authorization: $V_ACCESS_TOKEN" -H "Content-Type: application/json")
echo $RESPONSE
if [ true != $(echo $RESPONSE | jq -r ".terminated") ]; then
    echo "ERROR:失敗しました"
    exit 1
fi
echo ""

echo "----- 09_normal5-ind end" `TZ=JST-9 date "+%Y-%m-%d %H:%M:%S.%3N"`
