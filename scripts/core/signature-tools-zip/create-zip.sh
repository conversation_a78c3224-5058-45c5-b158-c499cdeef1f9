#!/bin/bash

DIR=$(dirname "$0")
cd $DIR

# ディレクトリを作成して、必要なファイルをコピー
mkdir ./signature-tools
# コピーするファイルのリストを変数に格納
files="../signature-tools/docker-compose.yml \
        ../signature-tools/Dockerfile \
        ../signature-tools/package.json \
        ../signature-tools/package-lock.json \
        ../signature-tools/README.md \
        ../signature-tools/requirements.txt"

# コピー先のディレクトリ
destination="./signature-tools"

# ファイルをコピー
cp $files $destination

mkdir ./signature-tools/tools
# zipに含めるファイルが追加になったときは、ここに追加
files="../signature-tools/tools/account_signature.js \
        ../signature-tools/tools/approve_signature.py \
        ../signature-tools/tools/arguments.json \
        ../signature-tools/tools/create_signature.py \
        ../signature-tools/tools/exchange_signature.py \
        ../signature-tools/tools/synchronous_signature.py \
        ../signature-tools/tools/tools.js \
        ../signature-tools/tools/transfer_signature.py"

# コピー先のディレクトリ
destination="./signature-tools/tools"

# ファイルをコピー
cp $files $destination

# signature-toolsをzip化
zip -r signature-tools.zip signature-tools/

# 後始末
rm -fR ./signature-tools
