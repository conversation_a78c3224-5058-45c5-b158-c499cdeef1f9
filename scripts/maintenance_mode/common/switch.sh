#!/bin/bash

BASE=$(
  cd "$(dirname "$0")" || exit
  cd ../..
  pwd
)
source "$BASE"/bin/common.sh

mode="$1"
environment="$2"

# check if the mode is 'on' or 'off'
if [[ "$mode" == "on" ]]; then
  printf "Enabling maintenance mode for %s\n\n" "$environment"
elif [[ "$mode" == "off" ]]; then
  printf "Disabling maintenance mode for %s\n\n" "$environment"
else
  echo "Invalid mode. Please use 'on' or 'off'."
  exit 1
fi

# check aws sts
if ! aws sts get-caller-identity --no-cli-pager; then
  printf "\nMake sure you've run aws-mfa or check your profile setting (~/.aws/config)\n"
  exit 1
else
  echo
fi

# start codebuild
message "warn" "You are about to switch maintenance mode $mode, in environment: $environment"
choice 'Is it ok to proceed?'

TARGET_REGIONS=("us-east-1" "ap-northeast-1" "ap-northeast-3")
TARGET_PROJECT_NAME="maintenance-mode-$mode-batch"

for region in "${TARGET_REGIONS[@]}"; do
  projects=$(aws codebuild list-projects --query "projects[?contains(@, \`$TARGET_PROJECT_NAME\`)]" --region "$region" --output text)

  for project in $projects; do
    aws codebuild start-build --project-name "$project" --region "$region" --no-cli-pager
  done
done
