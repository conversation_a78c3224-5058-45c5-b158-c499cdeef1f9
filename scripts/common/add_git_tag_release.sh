#!/bin/bash -eu

#
# 変数
#
REPOSITORY_PATH=""
ORGANIZATION=""
ENVIRONMENT_NAMES=""
COMPONENT_NAME=""
TAG_VERSION=""
COMMIT=""
RELEASE_NOTE=""

#
# 利用方法
#
function USAGE () {
  cat <<EOF
----------------------------------------------------------------
  DESCRIPTION
    This script is for adding git tag in local git repository and pushing tag to remote repository. Then create release note.

    See "$0 -h" for this description.

  SYNOPSIS
    $0
    -r <value>
    -o <value>
    -e <value>
    -c <value>
    -v <value>
    -i <value>
    -n <value>

  OPTIONS
    -r (string) (REQUIRED)
        Specify repository path.
    -o (string) (REQUIRED)
        Specify github organization.
    -e (string) (REQUIRED)
        Specify environment names separated by commas.
    -c (string) (REQUIRED)
        Specify component name.
    -v (string) (REQUIRED)
        Specify version.
    -i (string) (REQUIRED)
        Specify commit ID or branch name.
    -n (string) (REQUIRED)
        Specify release note.

  EXAMPLES
    Example 1: To add tag for cloud release with dcbg-dcf-terraform-sandbox and dcbg-dcf-terraform-tfvars repository in dev-fin environment.
      RELEASE_NOTE="【リリース作業】【xchain】外部ABIをアップロードするためのクラウドリソース展開
      https://decurret.atlassian.net/browse/DCFC-17098"

      $0 -r <your local path>/dcbg-dcf-terraform-sandbox \\
        -o decurret-lab \\
        -e dev-fin \\
        -c cloud \\
        -v 2.72.0 \\
        -i c797bd2ac8756bc3610d87f86e2205bb0a2bf854 \\
        -n "\${RELEASE_NOTE}"

      $0 -r <your local path>/dcbg-dcf-terraform-tfvars \\
        -o decurret-lab \\
        -e dev-fin \\
        -c cloud \\
        -v 2.72.0 \\
        -i edc3225d617bbb4e493af9a644a0dbf13450b517 \\
        -n "\${RELEASE_NOTE}"

    Example 2: To add tag to develop branch head for datadog release with dcbg-dcf-terraform-datadog and dcbg-dcf-terraform-tfvars repository in sandbox-fin, sandbox-ind, sandbox-int, sandbox-bpmfin, sandbox-bpmind environment.

      RELEASE_NOTE="【リリース作業】SOPSによる暗号化対応をリリースする
      https://decurret.atlassian.net/browse/DCFC-19657"

      $0 -r <your local path>/dcbg-dcf-terraform-datadog \\
        -o decurret-lab \\
        -e sandbox-fin,sandbox-ind,sandbox-int,sandbox-bpmfin,sandbox-bpmind \\
        -c datadog \\
        -v 2.24.0 \\
        -i develop \\
        -n "\${RELEASE_NOTE}"

      $0 -r <your local path>/dcbg-dcf-terraform-tfvars \\
        -o decurret-lab \\
        -e sandbox-fin,sandbox-ind,sandbox-int,sandbox-bpmfin,sandbox-bpmind \\
        -c datadog \\
        -v 2.24.0 \\
        -i develop \\
        -n "\${RELEASE_NOTE}"
----------------------------------------------------------------
EOF
}

#
# 引数チェック
#
function CHECK_ARGUMENT () {
  if [[ "${REPOSITORY_PATH}" == "" ]]; then
    return 1
  fi

  if [[ "${ORGANIZATION}" == "" ]]; then
    return 1
  fi

  if [[ "${ENVIRONMENT_NAMES}" == "" ]]; then
    return 1
  fi

  if [[ "${COMPONENT_NAME}" == "" ]]; then
    return 1
  fi

  if [[ "${TAG_VERSION}" == "" ]]; then
    return 1
  fi

  if [[ "${COMMIT}" == "" ]]; then
    return 1
  fi

  if [[ "${RELEASE_NOTE}" == "" ]]; then
    return 1
  fi

  return 0
}

#
# 確認用メッセージ
#
function CONFIRMATION () {
  read -p "Is it ok to proceed? (y/N): " ANSWER
  case "${ANSWER}" in
    [yY]*)
      echo "";;
    *)
      echo "Stopped $0."
      exit 0;;
  esac
}

#
# add git tag and push
# $1 (string)
#  dryrun: dry run
#  others: run
#
function ADD_GIT_TAG_AND_PUSH () {
  for ENVIRONMENT_NAME in $(echo "${ENVIRONMENT_NAMES//,/ }")
  do
    if [[ "$1" == "dryrun" ]]; then
      # dry run
      echo "git tag ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION} ${COMMIT}"
      echo "git push origin ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION}"
    else
      # run
      echo "git tag ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION} ${COMMIT}" | bash
      echo "git push origin ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION}" | bash
    fi
  done
}

#
# add git release
# $1 (string)
#  dryrun: dry run
#  others: run
#
function ADD_GIT_RELEASE () {
  REPOSITORY_NAME="${REPOSITORY_PATH##*/}"
  for ENVIRONMENT_NAME in $(echo "${ENVIRONMENT_NAMES//,/ }")
  do
    if [[ "$1" == "dryrun" ]]; then
      # dry run
      echo "gh release create ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION} --repo ${ORGANIZATION}/${REPOSITORY_NAME} --title ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION} --notes \"${RELEASE_NOTE}\""
    else
      # run
      echo "gh release create ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION} --repo ${ORGANIZATION}/${REPOSITORY_NAME} --title ${ENVIRONMENT_NAME}/${COMPONENT_NAME}/${TAG_VERSION} --notes \"${RELEASE_NOTE}\"" | bash
    fi
  done
}

#
# Main
#

# 引数を変数に代入
while getopts hr:o:e:c:v:i:n: OPT
do
  case $OPT in
    r) REPOSITORY_PATH=$OPTARG;;
    o) ORGANIZATION=$OPTARG;;
    e) ENVIRONMENT_NAMES=$OPTARG;;
    c) COMPONENT_NAME=$OPTARG;;
    v) TAG_VERSION=$OPTARG;;
    i) COMMIT=$OPTARG;;
    n) RELEASE_NOTE=$OPTARG;;
    ?) USAGE
      exit 0;;
  esac
done

# 引数チェック
if ! $(CHECK_ARGUMENT); then
  USAGE
  exit 1
fi

# ターゲットのチェック
cd "${REPOSITORY_PATH}"
echo ""
echo "## Show Commit information"
echo "----------------------------------------------------------------"
git show "${COMMIT}"
echo "----------------------------------------------------------------"
echo ""

# Dryrun (adding git tag and push)
echo "## Dryrun for adding git tag and push"
echo "----------------------------------------------------------------"
ADD_GIT_TAG_AND_PUSH "dryrun"
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION

# Run
ADD_GIT_TAG_AND_PUSH "run"
echo ""

# Dryrun (adding git release
echo "## Dryrun for adding git release"
echo "----------------------------------------------------------------"
ADD_GIT_RELEASE "dryrun"
echo "----------------------------------------------------------------"
echo ""
CONFIRMATION

# Run
ADD_GIT_RELEASE "run"
echo ""

echo "Completed $0."
echo ""
