#!/bin/bash

## Define common script functions.
message() {
  case $1 in
  "info")
    echo -e "\xF0\x9F\x8E\x89 \033[0;32m$2\033[0;39m"
    ;;
  "warn")
    echo -e "\xF0\x9F\x9A\xA7 \033[1;33m$2\033[0;39m"
    ;;
  "err")
    echo -e "\xf0\x9f\x94\xa5 \033[1;31m$2\033[0;39m"
    ;;
  esac
}

choice() {
  read -p "$(message "info" "$1 (y/N):")" yn
  case "$yn" in [yY]*) ;; *)
    message "err" "canceled."
    exit
    ;;
  esac
}
