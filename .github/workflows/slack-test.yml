name: "slack notification test"

on:
  workflow_dispatch:

env:
  SLACK_WEBHOOK: ${{secrets.SLACK_WEBHOOK_URL}}
  SLACK_CHANNEL: C029T5V73SQ
  SLACK_USERNAME: "DevOps"

jobs:
  slack-notify:
    runs-on: ubuntu-latest
    steps:
      - name: Slack Notification Sample
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_TITLE: "${{ github.repository }} actions run."
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "\
            @UL3JS0LCF \
            The build-and-push \
            job failed to execute."