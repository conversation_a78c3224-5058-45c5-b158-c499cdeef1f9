# https://action-slack.netlify.app/with
name: "slack notification test2"

on:
  workflow_dispatch:

env:
  SLACK_WEBHOOK_URL: ${{secrets.SLACK_WEBHOOK_URL}}

jobs:
  slack-notify:
    runs-on: ubuntu-latest
    steps:
      - name: Slack Notification Sample
        uses: 8398a7/action-slack@v3
        with:
          channel: C029T5V73SQ
          status: custom
          custom_payload: |
            {
              text: "<@here>",
              attachments: [{
                color: '${{ job.status }}' === 'success' ? 'good' : '${{ job.status }}' === 'failure' ? 'danger' : 'warning',
                title: "${{ github.repository }} #${{ github.event.number}}",
                title_link: "${{ github.event.pull_request.html_url }}",
                text: "${{ github.event.pull_request.title }} #${{ github.event.number }}",
                fields: [{
                  title: "Status",
                  value: "${{ job.status }}",
                  short: true
                }, {
                  title: "Log",
                  value: `<${process.env.GITHUB_SERVER_URL}/${{ github.repository }}/actions/runs/${{ github.run_id }}|Checks URL>`,
                  short: true
                }]
              }]
            }
          # mention: 'here'
          # status: ${{ job.status }}
          # fields: repo,message,commit,author,action,job,took,eventName,ref,workflow
