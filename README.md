# dcbg-dcf-devops-tools

## Introduction

DevOps をサポートするツールライブラリ集。特段ルールは定めていないが、各種実装ごとにディレクトリは別けて整理したほうが良いだろう。

## tool-listed

| TOOLS                          | DESCRIPTION                                   |
| ------------------------------ | --------------------------------------------- |
| scripts/gen-git-repo.sh        | 新規リポジトリを作成                          |
| scripts/ssh-bastion-bffvir.sh  | 踏み台サーバ SSH ログイン（TIS-POC 環境）     |
| scripts/ssh-bastion-sandcom.sh | 踏み台サーバ SSH ログイン（sandbox 共通環境） |
| scripts/ssh-bastion-sandext.sh | 踏み台サーバ SSH ログイン（sandbox 付加環境） |

### How to use

各種シェル実行前に`scripts/sample.env` をコピーして、`scripts/.env` を作成して以下を編集する。

```shell
TOKEN="specify your PAT"	// github PATを設定
USER="specify your username"	// github account usernameを設定
```

## scripts/bpm

### initial-setup

| TOOLS                      | DESCRIPTION                                                                                                                                                                                               | EXAMPLE |
|----------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|
| delete-initial-data-bpm.sh | データ削除を一括で行うシェル。<br>下記のシェルを順番に呼び出している。<br>処理が中断された場合などは下記のシェルを個別に実行することでリカバリが可能。<br>・_01_delete_table.sh<br>・_02_delete_secret_manager.sh<br>・_03_delete_cognito.sh                                        | ./delete-initial-data-bpm.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数：環境名(prod, stage, dev, sandbox, local) |
| insert-initial-data-bpm.sh | データ登録を一括で行うシェル。<br>下記のシェルを順番に呼び出している。<br>処理が中断された場合などは下記のシェルを個別に実行することでリカバリが可能。<br>・_11_create_table.sh<br>・_12_insert_data.sh                                                                            | ./insert-initial-data-bpm.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| select-initial-data-bpm.sh | データ取得と検証を一括で行うシェル。<br>下記のシェルを順番に呼び出している。<br>リリース手順においては実行した結果をリリース作業手順書に貼り付けることで、登録結果が想定通りかどうかを確認している。<br>処理が中断された場合などは下記のシェルを個別に実行することでリカバリが可能。<br>・_21_check_db_data.sh<br>・_22_check_secret_manager.sh | ./select-initial-data-bpm.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| setup-bizzone.sh           | BizZoneの初期データの登録として、プロバイダーの作成を行うシェル。<br>BPMのAPIを順次実行し、実行結果としてAPIの戻り値をまとめた内容を出力する。<br>途中で処理中断した場合は再度シェルを実行することでリカバリが可能。                                                                                    | ./setup-bizzone.sh<br>引数なし |
| setup-finzone.sh           | FinZoneの初期データの登録として、プロバイダー、イシュア、バリデータの作成を行うシェル。<br>BPMのAPIを順次実行し、実行結果としてAPIの戻り値をまとめた内容を出力する。<br>途中で処理中断した場合は再度シェルを実行することでリカバリが可能。                                                                         | ./setup-finzone.sh<br>引数なし |

上記ツールから呼び出しているスクリプトの一覧

| TOOLS   | DESCRIPTION                                                                                                                                                                                                        | EXAMPLE |
| ------- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| _01_delete_table.sh        | psqlでテーブルの削除、関数の削除、シーケンスの削除を実施する。<br>DBの接続を行うため、シェル内でポートフォワードと切断をしている。                                                                                                                                             | ./_01_delete_table.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _02_delete_secret_manager.sh | SecretManagerの削除を実施する。<br>レプリケートが行われている環境の場合、レプリケートの解除を行ってから削除を実施する。<br>各業務を実施する中で登録されたデータについては数が膨大となる可能性があるため、複数のウィンドウを自動起動し削除処理を並列実施する。<br>初期構築で登録したデータについてはキー指定で削除を行う。                                          | ./_02_delete_secret_manager.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _03_delete_cognito.sh | Cognitoの削除を実施する。<br>データの数が膨大となる可能性があるため、複数のウィンドウを自動起動し削除処理を並列実施する。                                                                                                                                                 | ./_03_delete_cognito.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _11_create_table.sh | psqlでSQLファイルを呼び出し、TBL作成、マスタデータ登録、関数の登録を行う。<br>呼び出しているSQLファイルの詳細は以下を参照。<br>https://github.com/decurret-lab/dcbg-dcjpy-bpm-db-migration/tree/develop/er                                                              | ./_11_create_table.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _12_insert_data.sh                    | service_admin, auth_clientの初期データを登録する。<br>登録に使用しているSQLは以下の通り。<br>`$CORE_ADMIN_CLIENT_ID`については各環境によって異なる。`INSERT INTO service_admin (service_id, admin_id) VALUES ('0', 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz');`<br>`INSERT INTO auth_client (entity_id, entity_type, client_id) VALUES('zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz', 'admin', '"$CORE_ADMIN_CLIENT_ID"')` | ./_12_insert_data.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _13_create_secret_manager.sh                    | SecretManagerの初期データを登録する。<br>登録する際は`alias/app-resource-encryption-key`で暗号化を実施している。<br>実施対象の環境に応じてレプリケートの実施も行う。                                                                                                     | ./_13_create_secret_manager.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _21_check_db_data.sh                    | TBLの件数、シーケンスの初期値、service_adminの登録内容、auth_clientの登録内容がそれぞれ想定通りかを検証するシェル。                                                                                                                                            | ./_21_check_db_data.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _22_check_secret_manager.sh                    | SecretManager の登録内容が想定通りかを確認するシェル。<br>実施対象の環境に応じてレプリケートされた内容の検証も行う。                                                                                                                                                | ./_22_check_secret_manager.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _port_forward.sh                    | ポートフォワードを行うシェル。<br>各シェルから呼び出されることを想定しており、本シェルのみを実行することは想定していない。                                                                                                                                                    | ./_port_forward.sh true<br>第一引数：APIのポートフォワード要否(prod環境向け) |
| _disconnect_port_forward.sh                    | ポートフォワードの切断を行うシェル。<br>各シェルから呼び出されることを想定しており、本シェルのみを実行することは想定していない。                                                                                                                                                 | ./_disconnect_port_forward.sh<br>引数なし |
| utils.sh                    | 共通処理を集約したシェル。                                                                                                                                                                                                      | (なし) |

上記のinitial-setup配下のスクリプトは下記BPM Serverのリリース作業手順内で使用  
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2626912466/BPM+Server

### test-tools

https://decurret.atlassian.net/wiki/spaces/DIG/pages/2394882056/BPM+Server#%E3%83%86%E3%82%B9%E3%83%88%E5%8F%A3%E5%BA%A7%E4%BD%9C%E6%88%90%E3%83%84%E3%83%BC%E3%83%AB

| TOOLS                      | DESCRIPTION                                                                                      |
| -------------------------- | ------------------------------------------------------------------------------------------------ |
| create-test-user.sh        | テスト口座作成シェル（Developer Admin Team Role を持っている人が、ローカルから実行する前提です） |
| env-bpmfin.sh              | 共通領域の環境変数設定シェル                                                                     |
| env-bpmind.sh              | 付加領域の環境変数設定シェル                                                                     |
| disconnect_port_forward.sh | ポートフォワードを切断するシェル                                                                 |
| port_forward.sh            | ローカルから sandbox-bpmfin、sandbox-bpmind にポートフォワードを行うシェル                       |

### 正常系テスト資材

https://decurret.atlassian.net/wiki/spaces/DIG/pages/2394882056/BPM+Server+IT#%E3%82%B7%E3%82%A7%E3%83%AB%E3%82%92%E5%88%A9%E7%94%A8%E3%81%97%E3%81%9F%E6%AD%A3%E5%B8%B8%E7%B3%BB%E3%83%86%E3%82%B9%E3%83%88%E3%81%AE%E5%AE%9F%E6%96%BD

| TOOLS                          | DESCRIPTION                                                                                                                                                |
| ------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 01_transfer_bpmfin.sh          | 共通領域でサインイン〜送金を行うシェル                                                                                                                     |
| 02_synchronous_bpmind.sh       | 付加領域でサインイン〜付加領域用アカウント開設チェックを行うシェル                                                                                         |
| 03_exchange_bpmfin.sh          | 共通領域で付加領域用口座作成〜チャージを行うシェル                                                                                                         |
| 04_account_operation_bpmind.sh | 付加領域で送金、返還、解約を行うシェル                                                                                                                     |
| 05_terminated_bpmfin.sh        | 共通領域で解約を行うシェル                                                                                                                                 |
| 06_create_admin_bpmfin.sh      | 共通領域で管理者作成〜照会を行うシェル                                                                                                                     |
| 07_account_operation_bpmfin.sh | 共通領域で停止、凍結、解約を行うシェル                                                                                                                     |
| 08_create_admin_bpmind.sh      | 付加領域で管理者作成〜照会を行うシェル                                                                                                                     |
| 09_terminated_bpmind.sh        | 付加領域で停止、解約を行うシェル                                                                                                                           |
| create_account_bpmfin.sh       | 共通領域の正常系テスト口座作成を行うシェル（create-test-user.sh を利用するため、Developer Admin Team Role を持っている人が、ローカルから実行する前提です） |
| create_account_bpmind.sh       | 付加領域の正常系テスト口座作成を行うシェル（create-test-user.sh を利用するため、Developer Admin Team Role を持っている人が、ローカルから実行する前提です） |

### 準正常系テスト資材

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM+Server+IT#%E6%BA%96%E6%AD%A3%E5%B8%B8%E7%B3%BB%E3%83%86%E3%82%B9%E3%83%88%E5%AE%9F%E6%96%BD%E6%89%8B%E9%A0%86

| TOOLS                                | DESCRIPTION                                                                                                                                                  |
| ------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| semi_normal_create_account_bpmind.sh | 共通領域の準正常系テスト口座作成を行うシェル（create-test-user.sh を利用するため、Developer Admin Team Role を持っている人が、ローカルから実行する前提です） |
| semi_normal_create_account_bpmind.sh | 付加領域の準正常系テスト口座作成を行うシェル（create-test-user.sh を利用するため、Developer Admin Team Role を持っている人が、ローカルから実行する前提です） |
| semi_normal_sign_in_bpmfin.sh        | 共通領域の準正常系テスト口座にサインインを行うシェル                                                                                                         |
| semi_normal_sign_in_bpmind.sh        | 付加領域の準正常系テスト口座にサインインを行うシェル                                                                                                         |
| semi_normal_test_bpmfin.sh           | 共通領域の準正常系テストを行うシェル                                                                                                                         |
| semi_normal_test_bpmind.sh           | 付加領域の準正常系テストを行うシェル                                                                                                                         |

## scripts/contract

### id-recovery(各種 ID の再登録)

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/ID+v2

| TOOLS                 | DESCRIPTION                 |
| --------------------- | --------------------------- |
| env.sh                | 環境変数用のシェル          |
| register-issuer.sh    | providerId 再登録用のシェル |
| register-provider.sh  | tokenId 再登録用のシェル    |
| register-token.sh     | issuerId 再登録用シェル     |
| register-validator.sh | validatorId 再登録用シェル  |

## scripts/core

### id-resolver

実行方法など、Confluence を参照
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Sandbox+account_id

| TOOLS          | DESCRIPTION                        |
|----------------|------------------------------------|
| id_resolver.py | dc_bank_numberからaccount_idを生成するツール |

### tool-listed

| TOOLS          | DESCRIPTION                             |
| -------------- | --------------------------------------- |
| rds-connect.sh | BASTION 経由で RDS ポートフォワード接続 |

### initial-setup

| TOOLS                    | DESCRIPTION                                                                                                                                              | EXAMPLE |
| ------------------------ |----------------------------------------------------------------------------------------------------------------------------------------------------------|----------|
| delete-initial-data-core.sh | データ削除を一括で行うシェル。<br>下記のシェルを順番に呼び出している。<br>処理が中断された場合などは下記のシェルを個別に実行することでリカバリが可能。<br>・_01_delete_table.sh<br>・_02_delete_dynamodb.sh<br>・_03_delete_ssm.sh | ./delete-initial-data-core.sh fin stage<br><br>第一引数(必須)：対象のZone<br>第二引数：環境名(prod, stage, dev, sandbox, local) |
| insert-initial-data-core.sh | データ登録を一括で行うシェル。<br>呼び出す対象となるシェルは現状では1つのみだが、今後シェル追加の可能性と、他のシェルとの処理単位を揃えるため本シェルを用意している。<br>・_11_put_ssm.sh | ./insert-initial-data-core.sh fin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| select-initial-data-core.sh | データ取得と検証を一括で行うシェル。<br>下記のシェルを順番に呼び出している。<br>リリース手順においては実行した結果をリリース作業手順書に貼り付けることで、登録結果が想定通りかどうかを確認している。<br>処理が中断された場合などは下記のシェルを個別に実行することでリカバリが可能。<br>・_21_check_db_data.sh<br>・_22_check_ssm.sh  | ./select-initial-data-core.sh fin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| setup-bizzone.sh | BizZoneの初期データの登録として、プロバイダーの作成を行うシェル。<br>coreのAPIを順次実行し、実行結果としてAPIの戻り値をまとめた内容を出力する。<br>途中で処理中断した場合は再度シェルを実行することでリカバリが可能。  | ./setup-bizzone.sh<br>引数なし |
| setup-finzone.sh | FinZoneの初期データの登録として、プロバイダー、イシュア、バリデータの作成を行うシェル。<br>coreのAPIを順次実行し、実行結果としてAPIの戻り値をまとめた内容を出力する。<br>途中で処理中断した場合は再度シェルを実行することでリカバリが可能。 | ./setup-finzone.sh<br>引数なし |

上記ツールから呼び出しているスクリプトの一覧

| TOOLS   | DESCRIPTION                                                                                                                                                                                                        | EXAMPLE |
| ------- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| _01_delete_table.sh        | psqlでテーブルの削除、関数の削除、シーケンスの削除を実施する。<br>DBの接続を行うため、シェル内でポートフォワードと切断をしている。                                                                                                                                             | ./_01_delete_table.sh bpmfin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _02_delete_dynamodb.sh | DynamoDBの初期化を実施する。<br>`sending_counter`については値を0に更新する。<br>`balance_cache`については登録されたデータ全件を削除する。<br>データ数が膨大となる可能性があるため、削除処理については複数のウィンドウを自動起動し並列実施する。<br>上記以外のテーブルについては処理は行わない。 | ./_02_delete_dynamodb.sh fin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _03_delete_ssm.sh | SSMの削除を実施する。<br>データの数が膨大となる可能性があるため、複数のウィンドウを自動起動し削除処理を並列実施する。<br>レプリケートが行われている環境の場合、レプリケート先についても同様に削除を実施する。 | ./_03_delete_ssm.sh fin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _11_put_ssm.sh | SSMの初期データを登録する。<br>登録する際は`alias/app-resource-encryption-key`で暗号化を実施している。 | ./_11_put_ssm.sh fin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _21_check_db_data.sh | TBLの件数が想定通りかを検証するシェル。 | ./_21_check_db_data.sh fin stage<br>第一引数(必須)：対象のZone<br>第二引数(必須)：環境名(prod, stage, dev, sandbox, local) |
| _22_check_ssm.sh | SSM の登録内容が想定通りかを確認するシェル。<br>実施対象の環境に応じてレプリケートされた内容の検証も行う。<br>検証時は登録内容を複合化する。 | 登録内容がそれぞれ想定通りかを検証するシェル。 |
| _port_forward.sh                    | ポートフォワードを行うシェル。<br>各シェルから呼び出されることを想定しており、本シェルのみを実行することは想定していない。                                                                                                                                                    | ./_port_forward.sh true<br>第一引数：APIのポートフォワード要否(prod環境向け) |
| _disconnect_port_forward.sh                    | ポートフォワードの切断を行うシェル。<br>各シェルから呼び出されることを想定しており、本シェルのみを実行することは想定していない。                                                                                                                                                 | ./_disconnect_port_forward.sh<br>引数なし |
| utils.sh                    | 共通処理を集約したシェル。                                                                                                                                                                                                      | (なし) |

上記のinitial-setup配下のスクリプトは下記Coreのリリース作業手順内で使用
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2638938113/Core

### load-test-tools(負荷試験資材)

実行方法など、Confluence を参照
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1530593307

### poc

register_id との違いは、name が入れられることのみ

| TOOLS                    | DESCRIPTION                                    |
| ------------------------ | ---------------------------------------------- |
| createIssuerValidator.sh | Issuer、Validator を作成するシェル(共通領域用) |
| createValidator.sh       | Validator を作成するシェル(付加領域用)         |

### register_id(発行者、バリデータの登録)

https://decurret.atlassian.net/wiki/spaces/DIG/pages/1626440190

| TOOLS                         | DESCRIPTION                              |
| ----------------------------- | ---------------------------------------- |
| register_id.sh                | 発行者、バリデータの登録を実施するシェル |
| register_id_for_common.env    | sandbox 環境（共通領域）用の環境ファイル |
| register_id_for_extension.env | sandbox 環境（付加領域）用の環境ファイル |

### signature-tools

アカウント署名の説明
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********#%E3%82%A2%E3%82%AB%E3%82%A6%E3%83%B3%E3%83%88%E7%BD%B2%E5%90%8D%E3%81%AB%E3%81%A4%E3%81%84%E3%81%A6

ツールの使い方
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Sandbox

| TOOLS                    | 外部提供有無 | DESCRIPTION                                                        |
|--------------------------| ------------ |--------------------------------------------------------------------|
| account_signature.js     | 有           | 各スクリプトから呼ばれ、アカウント署名を作成する js                                        |
| approve_signature.py     | 有           | approve 用アカウント署名を作成するスクリプト                                         |
| arguments.json           | 有           | create_signature.pyで使用する、入力するべき引数を表示するjson                         |
| create_signature.py      | 有           | 各種pythonファイル                                                       |
| exchange_signature.py    | 有           | exchange 用アカウント署名を作成するスクリプト                                        |
| recover_signature.js     | 無           | recover_signature.sh から呼ばれ、アカウント署名の検証をする js                        |
| recover_signature.py     | 無           | message hash と signature から publicKey を取得するスクリプト<br>アカウント署名検証に使用する |
| synchronous_signature.py | 有           | synchronous 用アカウント署名を作成するスクリプト                                     |
| tools.js                 | 有           | 共通処理の js                                                           |
| transfer_signature.py    | 有           | transfer 用アカウント署名を作成するスクリプト                                        |

### signature-tools-zip

| TOOLS         | DESCRIPTION                           |
| ------------- | ------------------------------------- |
| create-zip.sh | signature-tools を zip に固めるシェル |

外部提供有のソースを修正し Sandbox 環境にリリースする場合、create-zip.sh を実行して zip ファイルを作成する。  
作成した zip ファイルを、dcbg-dcf-core-api に commit します。  
リリースや確認方法は、dcbg-dcf-core-api の README を参照ください。  
https://github.com/decurret-lab/dcbg-dcf-core-api

### test-tools

https://decurret.atlassian.net/wiki/spaces/DIG/pages/1949466690/API
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1949499393/API

| TOOLS                      | DESCRIPTION                                                                                            |
| -------------------------- | ------------------------------------------------------------------------------------------------------ |
| template.env.sh            | 正常系テストの環境変数をまとめたテンプレート。利用する際はこのファイルより `env.sh` を作成してください |
| port_forward.sh            | 踏み台サーバに対してポートフォワードを行うシェル                                                       |
| disconnect_port_forward.sh | 上記ポートフォワードの解除を行うシェル                                                                 |
| 00_init-fin.sh             | [共通領域用] 環境初期リリース時のプロバイダ作成〜トークン作成を実行するシェル                          |
| 00_init-ind.sh             | [付加領域用] 環境初期リリース時のプロバイダ作成〜トークン作成を実行するシェル                          |
| _NN_\__XXXX_-fin.sh        | [共通領域用] 各正常系テストを実行するシェル                                                            |
| _NN_\__XXXX_-ind.sh        | [付加領域用] 各正常系テストを実行するシェル                                                            |
| semi-normal.sh             | 準正常系テストを実行するシェル                                                                         |

テストスクリプトを実行するにあたり、 `template.env.sh` をコピーして `env.sh` を作成し、各環境に沿った設定を行ってください。

## scripts/besu

### create static-node.json

BESU 関連の各手順書で共通で使用するスクリプト群

動作確認手順: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2542928040/BESU

| TOOLS                    | DESCRIPTION                   |
| ------------------------ | ----------------------------- |
| start_besu.sh            | BESU を起動する               |
| check_besu_behavior.sh   | BESU 単体の動作を確認する     |
| check_besu_block_sync.sh | BESU のブロック同期を確認する |

## scripts/besu/recreate

BESU ノードの再作成作業のためのスクリプト群

手順書: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2416541724/BESU+user+data+ami

| TOOLS                             | DESCRIPTION                                |
| --------------------------------- | ------------------------------------------ |
| disable_termination_protection.sh | 終了保護をはずす                           |
| terminate_instance.sh             | インスタンスを終了する                     |
| stop_datadog_agent.sh             | Datadog Agent を停止する                   |
| start_datadog_agent.sh            | Datadog Agent を起動する                   |
| change_autoscaling_size.sh        | Auto Scaling Group のサイズを変更する      |
| replace_data_volume.sh            | インスタンスのデータボリュームを付け替える |
| delete_unused_volume.sh           | 未使用ボリュームを削除する                 |

## scripts/besu/backup

バックアップノードの再作成作業のためのスクリプト群

手順書: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2530181219

| TOOLS                                   | DESCRIPTION                                      |
| --------------------------------------- | ------------------------------------------------ |
| terminate_besu_backup_instance.sh       | バックアップノードを終了する                     |
| create_ebs_data_volume_from_snapshot.sh | snapshot からデータボリュームを作成する          |
| replace_ebs_data_volume.sh              | バックアップノードのデータボリュームを付け替える |

## scripts/allcomponents

### tool-listed
#### Core関連環境の自動設定手順
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1984069913/ALL#%E8%87%AA%E5%8B%95%E8%A8%AD%E5%AE%9A%E6%89%8B%E9%A0%86.1
#### Web3Stream関連環境の自動設定手順
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2259779711/Web3Stream+RDSStream#%E3%83%AD%E3%83%BC%E3%82%AB%E3%83%AB%E7%92%B0%E5%A2%83%E8%87%AA%E5%8B%95%E6%A7%8B%E7%AF%89
#### Relayer関連環境の自動設定手順
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2518581288/Relayer#1.%E3%83%AD%E3%83%BC%E3%82%AB%E3%83%AB%E7%92%B0%E5%A2%83%E8%87%AA%E5%8B%95%E6%A7%8B%E7%AF%89
#### BPMServer関連環境の自動設定手順
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2525462608/BPMServer+IT#%E6%89%8B%E9%A0%86%E6%A6%82%E8%A6%81


| TOOLS                            | DESCRIPTION                                          |
|----------------------------------|------------------------------------------------------|
| start_core-components.sh         | core→besu→contract→BCClient→BCMonitoringを順にローカルで一括起動 |
| start_web3stream-components.sh   | besu→contract→Web3Streamを順にローカルで一括起動                 |
| start_relayer-components.sh | besu→contract→Relayerを順にローカルで一括起動                    |
| start_bpm-components.sh | bpm関連コンポーネントを順にローカルで一括起動                             |
| stop.sh                          | 全コンポーネントを一時停止                                        |
| restart_core-components.sh | stop.shで一時停止したCore関連環境の全コンポーネントを再起動                  |
| restart_web3stream-components.sh | stop.shで一時停止したWeb3Stream関連環境の全コンポーネントを再起動            |
| restart_relayer-components.sh | stop.shで一時停止したRelayer関連環境の全コンポーネントを再起動               |
| restart_bpm-components.sh     | stop.shで一時停止したbpm関連環境の全コンポーネントを再起動                   |
| destroy.sh                       | 全コンポーネントを削除(docker-compose down/besuノード削除)           |

### subscripts(start.shから実行するサブスクリプト)
| TOOLS                            | DESCRIPTION                      |
|----------------------------------|----------------------------------|
| _load_env.sh                     | 環境変数設定                           |
| start_core.sh                    | coreの起動とDBのマイグレーション・更新           |
| start_besu_network.sh            | besuノードの生成・起動                    |
| start_besu_node_for_relayer.sh   | Relayer用のbesuノード(共通・付加ノード)の生成・起動 |
| start_bcclient.sh                | ABIファイルのコピーとBCClientの起動          |
| start_bcmonitoring.sh            | ABIファイルのコピーとBCMonitoringの起動      |
| start_migrate_contract.sh        | コントラクトマイグレーション                   |
| start_migrate_contract_shared.sh | 残高DLTのコントラクトマイグレーション             |
| start_migrate_contract_ibc.sh    | IBCのコントラクトマイグレーション               |
| start_web3stream.sh              | ABIファイルのコピーとWeb3Streamの起動        |
| start_relayer.sh                 | ABIファイルのコピーとRelayerの起動           |
| start_bank_api_mock.sh           | BANK_API(Mock)の起動                |
| start_bff.sh                     | bffの起動                           |
| start_bpm.sh                     | bpmの起動                           |
| start_core-with-bcclientmock                | coreの起動とDBマイグレーション(BPM用)         |


## scripts/datadog

### tool-listed

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Datadog
| TOOLS | DESCRIPTION |
| ------------------------- | ---------------------------------------------------------------------- |
| create-account.sh | DataDog アカウントを新規発行するシェル。user-invite.sh も連動して呼び出している |
| user-invite.sh | アカウント発行済みのユーザーに対し、招待メールを送信するシェル |
| delete-account.sh | DataDog アカウントの削除を実行するシェル |
| add-role.sh | アカウント発行済みのユーザーを DataDog ロールに追加するシェル |
| datadog.env | datadog の API 打鍵に必要な環境変数を管理するファイル |

## scripts/gas

チーム全体で利用している Google Apps Script のプログラムファイル群
各プログラムを利用しているシート一覧は以下リンクを参照
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Google+Apps+Scripts

ファイルの追加・編集は、Chrome 公式の GitHub Assistant を利用して行い、直でブランチを切ってのコミットは避けること。
手順は以下リンクを参照
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Google+Apps+Script+Git

### tool-listed

| TOOLS                        | 使用ファイル                       |
| ---------------------------- | ---------------------------------- |
| approval_request_list.gs     | 承認依頼一覧                       |
| create_evidence_sheet.gs     | 【テンプレート】リリース作業手順書 |
| datadog_alert_list.gs        | DataDog アラート自動収集用シート   |
| datadog_webhook_interface.gs | DataDog アラート自動収集用シート   |
| personal_work_condition.gs   | 個人稼働状況まとめ                 |
| release_calender.gs          | 【各環境】リリースカレンダー       |
| work_condition_importer.gs   | 稼働管理インポート用シート         |
| work_condition.gs            | 稼働状況                           |

## scripts/gas/test-tools

チーム全体で利用している Google Apps Script のプログラムファイル群を修正する際に、動作確認等で必要な諸ツール群

使用方法は [Confluence](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2596306994/Google+Apps+Script) を参照する。

### tool-listed

| TOOLS                   | 使用ファイル                                                                  |
| ----------------------- | ----------------------------------------------------------------------------- |
| datadog_webhook_test.sh | DataDog アラート自動収集用シートのテストに利用するシェルスクリプト            |
| datadog_webhook.env     | DataDog アラート自動収集用シートの API 打鍵に必要な環境変数を管理するファイル |

## scirpts/cloud/sops

### tool-listed

| TOOLS                          | 使用ファイル                                            |
| ------------------------------ | ------------------------------------------------------- |
| \_common_sops_encryption.sh    | 他スクリプトで共通で使用する定数と関数の定義            |
| create_sops_encryption_key.sh  | SOPS で使用する KMS 鍵 を作成するシェルスクリプト       |
| get_sops_encryption_key_arn.sh | SOPS で使用する KMS 鍵の arn を取得するシェルスクリプト |

## scripts/cloud/kubernetes/sampleapp

Kubernetes のクラウド検証用サンプルアプリリソースとスクリプト群

使用方法は [Confluence](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2551349271/EKS+Kubernetes) を参照する。

## scirpts/cloud/env-start-stop

### tool-listed

| TOOLS            | 使用ファイル                                           |
| ---------------- | ------------------------------------------------------ |
| start_env.sh     | 各環境の RDS,BESU,EKS を順番に起動するシェルスクリプト |
| stop_env.sh      | 各環境の EKS,BESU,RDS を順番に停止するシェルスクリプト |
| start_bastion.sh | 踏み台を起動するシェルスクリプト                       |
| stop_bastion.sh  | 踏み台を停止するシェルスクリプト                       |

各環境の起動・停止を行うスクリプト

使用方法は Confluence の [AWS の各環境の自動起動停止処理を手動実施する手順](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2511700163/AWS) を参照する.

## scripts/cloud/log-test

### tool-listed

| TOOLS                   | 使用ファイル                                         |
| ----------------------- | ---------------------------------------------------- |
| put-lambda-log-event.sh | 他スクリプトで共通で使用するログ送信シェルスクリプト |
| put-eks-log-event.sh    | EKS にログ送信するシェルスクリプト                   |
| put-eks-log-event.sh    | Lambda にログ送信するシェルスクリプト                |

使用方法は Confluence の [DataDog へのログサブスクリプションを確認するために CloudWatch Logs にテスト用のログを送信する手順](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2566521105/DataDog+CloudWatch+Logs) を参照する

## scirpts/cloud/cloudformation

### tool-listed

| TOOLS              | 使用ファイル                                                     |
| ------------------ | ---------------------------------------------------------------- |
| check_stacksets.sh | CloudFormation の StackSets の設定状況を確認するためのスクリプト |

使用方法は Confluence の [CloudFormation StackSets 適用手順書](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2063990785/CloudFormation+StackSets) を参照する

## scripts/common

### tool-listed

| TOOLS                  | 使用ファイル                                             |
| ---------------------- | -------------------------------------------------------- |
| add_git_tag_release.sh | git のタグ付けとリリースノートを作成するシェルスクリプト |

使用方法は Confluence の [GitHub でタグ付けとリリースノートを作成するシェルの使用方法](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2600796241/GitHub) を参照する


## modules/

シェルスクリプトでは実現が困難なツールモジュールを管理する。

### tool-listed

| TOOLS                   | DESCRIPTION                                |
| ----------------------- | ------------------------------------------ |
| github-secret-register  | GitHub Environment に secret を登録するツール |
