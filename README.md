# dcbg-dcf-devops-tools

## Introduction

A collection of tool libraries that support DevOps. While no specific rules are defined, it would be better to organize directories separately for each implementation.

## tool-listed

| TOOLS                          | DESCRIPTION                                        |
| ------------------------------ | -------------------------------------------------- |
| scripts/gen-git-repo.sh        | Create new repository                              |
| scripts/ssh-bastion-bffvir.sh  | Bastion server SSH login (TIS-POC environment)    |
| scripts/ssh-bastion-sandcom.sh | Bastion server SSH login (sandbox common environment) |
| scripts/ssh-bastion-sandext.sh | Bastion server SSH login (sandbox extension environment) |

### How to use

Before executing various shells, copy `scripts/sample.env` to create `scripts/.env` and edit the following:

```shell
TOKEN="specify your PAT"	// Set GitHub PAT
USER="specify your username"	// Set GitHub account username
```

## scripts/bpm

### initial-setup

| TOOLS                      | DESCRIPTION                                                                                                                                                                                               | EXAMPLE |
|----------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|
| delete-initial-data-bpm.sh | Shell script that performs batch data deletion.<br>Calls the following shells in order.<br>Recovery is possible by executing the following shells individually if processing is interrupted.<br>・_01_delete_table.sh<br>・_02_delete_secret_manager.sh<br>・_03_delete_cognito.sh                                        | ./delete-initial-data-bpm.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument: Environment name (prod, stage, dev, sandbox, local) |
| insert-initial-data-bpm.sh | Shell script that performs batch data registration.<br>Calls the following shells in order.<br>Recovery is possible by executing the following shells individually if processing is interrupted.<br>・_11_create_table.sh<br>・_12_insert_data.sh                                                                            | ./insert-initial-data-bpm.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| select-initial-data-bpm.sh | Shell script that performs batch data retrieval and verification.<br>Calls the following shells in order.<br>In release procedures, the execution results are pasted into the release work procedure document to verify if the registration results are as expected.<br>Recovery is possible by executing the following shells individually if processing is interrupted.<br>・_21_check_db_data.sh<br>・_22_check_secret_manager.sh | ./select-initial-data-bpm.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| setup-bizzone.sh           | Shell script that creates providers as initial data registration for BizZone.<br>Executes BPM APIs sequentially and outputs the summarized API return values as execution results.<br>Recovery is possible by re-executing the shell if processing is interrupted midway.                                                                                    | ./setup-bizzone.sh<br>No arguments |
| setup-finzone.sh           | Shell script that creates providers, issuers, and validators as initial data registration for FinZone.<br>Executes BPM APIs sequentially and outputs the summarized API return values as execution results.<br>Recovery is possible by re-executing the shell if processing is interrupted midway.                                                                         | ./setup-finzone.sh<br>No arguments |

List of scripts called from the above tools

| TOOLS   | DESCRIPTION                                                                                                                                                                                                        | EXAMPLE |
| ------- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| _01_delete_table.sh        | Performs table deletion, function deletion, and sequence deletion using psql.<br>Port forwarding and disconnection are performed within the shell to connect to the DB.                                                                                                                                             | ./_01_delete_table.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _02_delete_secret_manager.sh | Performs SecretManager deletion.<br>For environments where replication is performed, replication is removed before deletion.<br>For data registered during business operations, multiple windows are automatically launched for parallel deletion processing due to potentially large data volumes.<br>Data registered during initial construction is deleted by key specification.                                          | ./_02_delete_secret_manager.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _03_delete_cognito.sh | Performs Cognito deletion.<br>Multiple windows are automatically launched for parallel deletion processing due to potentially large data volumes.                                                                                                                                                 | ./_03_delete_cognito.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _11_create_table.sh | Calls SQL files using psql to create tables, register master data, and register functions.<br>For details of the called SQL files, refer to the following:<br>https://github.com/decurret-lab/dcbg-dcjpy-bpm-db-migration/tree/develop/er                                                              | ./_11_create_table.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _12_insert_data.sh                    | Registers initial data for service_admin and auth_client.<br>The SQL used for registration is as follows:<br>`$CORE_ADMIN_CLIENT_ID` varies by environment. `INSERT INTO service_admin (service_id, admin_id) VALUES ('0', 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz');`<br>`INSERT INTO auth_client (entity_id, entity_type, client_id) VALUES('zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz', 'admin', '"$CORE_ADMIN_CLIENT_ID"')` | ./_12_insert_data.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _13_create_secret_manager.sh                    | Registers initial data for SecretManager.<br>Encryption is performed using `alias/app-resource-encryption-key` during registration.<br>Replication is also performed according to the target environment.                                                                                                     | ./_13_create_secret_manager.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _21_check_db_data.sh                    | Shell script that verifies if table counts, sequence initial values, service_admin registration contents, and auth_client registration contents are as expected.                                                                                                                                            | ./_21_check_db_data.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _22_check_secret_manager.sh                    | Shell script that verifies if SecretManager registration contents are as expected.<br>Also verifies replicated contents according to the target environment.                                                                                                                                                | ./_22_check_secret_manager.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _port_forward.sh                    | Shell script that performs port forwarding.<br>Intended to be called from other shells, not intended to be executed alone.                                                                                                                                                    | ./_port_forward.sh true<br>First argument: Whether API port forwarding is required (for prod environment) |
| _disconnect_port_forward.sh                    | Shell script that disconnects port forwarding.<br>Intended to be called from other shells, not intended to be executed alone.                                                                                                                                                 | ./_disconnect_port_forward.sh<br>No arguments |
| utils.sh                    | Shell script that consolidates common processing.                                                                                                                                                                                                      | (None) |

The scripts under the above initial-setup are used in the BPM Server release work procedures below:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM+Server

### test-tools

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM+Server#%E3%83%86%E3%82%B9%E3%83%88%E5%8F%A3%E5%BA%A7%E4%BD%9C%E6%88%90%E3%83%84%E3%83%BC%E3%83%AB

| TOOLS                      | DESCRIPTION                                                                                      |
| -------------------------- | ------------------------------------------------------------------------------------------------ |
| create-test-user.sh        | Test account creation shell (intended to be executed locally by someone with Developer Admin Team Role) |
| env-bpmfin.sh              | Environment variable setting shell for common area                                               |
| env-bpmind.sh              | Environment variable setting shell for extension area                                            |
| disconnect_port_forward.sh | Shell to disconnect port forwarding                                                             |
| port_forward.sh            | Shell to perform port forwarding from local to sandbox-bpmfin and sandbox-bpmind               |

### Normal System Test Materials

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM+Server+IT#%E3%82%B7%E3%82%A7%E3%83%AB%E3%82%92%E5%88%A9%E7%94%A8%E3%81%97%E3%81%9F%E6%AD%A3%E5%B8%B8%E7%B3%BB%E3%83%86%E3%82%B9%E3%83%88%E3%81%AE%E5%AE%9F%E6%96%BD

| TOOLS                          | DESCRIPTION                                                                                                                                                |
| ------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 01_transfer_bpmfin.sh          | Shell to perform sign-in to transfer in common area                                                                                                       |
| 02_synchronous_bpmind.sh       | Shell to perform sign-in to extension area account opening check in extension area                                                                        |
| 03_exchange_bpmfin.sh          | Shell to perform extension area account creation to charge in common area                                                                                 |
| 04_account_operation_bpmind.sh | Shell to perform transfer, return, and cancellation in extension area                                                                                     |
| 05_terminated_bpmfin.sh        | Shell to perform cancellation in common area                                                                                                              |
| 06_create_admin_bpmfin.sh      | Shell to perform administrator creation to inquiry in common area                                                                                         |
| 07_account_operation_bpmfin.sh | Shell to perform suspension, freeze, and cancellation in common area                                                                                      |
| 08_create_admin_bpmind.sh      | Shell to perform administrator creation to inquiry in extension area                                                                                      |
| 09_terminated_bpmind.sh        | Shell to perform suspension and cancellation in extension area                                                                                            |
| create_account_bpmfin.sh       | Shell to create normal system test accounts for common area (intended to be executed locally by someone with Developer Admin Team Role using create-test-user.sh) |
| create_account_bpmind.sh       | Shell to create normal system test accounts for extension area (intended to be executed locally by someone with Developer Admin Team Role using create-test-user.sh) |

### Semi-Normal System Test Materials

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BPM+Server+IT#%E6%BA%96%E6%AD%A3%E5%B8%B8%E7%B3%BB%E3%83%86%E3%82%B9%E3%83%88%E5%AE%9F%E6%96%BD%E6%89%8B%E9%A0%86

| TOOLS                                | DESCRIPTION                                                                                                                                                  |
| ------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| semi_normal_create_account_bpmind.sh | Shell to create semi-normal system test accounts for common area (intended to be executed locally by someone with Developer Admin Team Role using create-test-user.sh) |
| semi_normal_create_account_bpmind.sh | Shell to create semi-normal system test accounts for extension area (intended to be executed locally by someone with Developer Admin Team Role using create-test-user.sh) |
| semi_normal_sign_in_bpmfin.sh        | Shell to sign in to semi-normal system test accounts in common area                                                                                         |
| semi_normal_sign_in_bpmind.sh        | Shell to sign in to semi-normal system test accounts in extension area                                                                                      |
| semi_normal_test_bpmfin.sh           | Shell to perform semi-normal system tests in common area                                                                                                    |
| semi_normal_test_bpmind.sh           | Shell to perform semi-normal system tests in extension area                                                                                                 |

## scripts/contract

### id-recovery (Re-registration of various IDs)

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/ID+v2

| TOOLS                 | DESCRIPTION                        |
| --------------------- | ---------------------------------- |
| env.sh                | Shell for environment variables    |
| register-issuer.sh    | Shell for providerId re-registration |
| register-provider.sh  | Shell for tokenId re-registration  |
| register-token.sh     | Shell for issuerId re-registration |
| register-validator.sh | Shell for validatorId re-registration |

## scripts/core

### id-resolver

For execution methods, refer to Confluence:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Sandbox+account_id

| TOOLS          | DESCRIPTION                        |
|----------------|------------------------------------|
| id_resolver.py | Tool to generate account_id from dc_bank_number |

### tool-listed

| TOOLS          | DESCRIPTION                             |
| -------------- | --------------------------------------- |
| rds-connect.sh | RDS port forwarding connection via BASTION |

### initial-setup

| TOOLS                    | DESCRIPTION                                                                                                                                              | EXAMPLE |
| ------------------------ |----------------------------------------------------------------------------------------------------------------------------------------------------------|----------|
| delete-initial-data-core.sh | Shell script that performs batch data deletion.<br>Calls the following shells in order.<br>Recovery is possible by executing the following shells individually if processing is interrupted.<br>・_01_delete_table.sh<br>・_02_delete_dynamodb.sh<br>・_03_delete_ssm.sh | ./delete-initial-data-core.sh fin stage<br><br>First argument (required): Target Zone<br>Second argument: Environment name (prod, stage, dev, sandbox, local) |
| insert-initial-data-core.sh | Shell script that performs batch data registration.<br>Currently only one shell is called, but this shell is prepared to align processing units with other shells and for potential future shell additions.<br>・_11_put_ssm.sh | ./insert-initial-data-core.sh fin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| select-initial-data-core.sh | Shell script that performs batch data retrieval and verification.<br>Calls the following shells in order.<br>In release procedures, the execution results are pasted into the release work procedure document to verify if the registration results are as expected.<br>Recovery is possible by executing the following shells individually if processing is interrupted.<br>・_21_check_db_data.sh<br>・_22_check_ssm.sh  | ./select-initial-data-core.sh fin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| setup-bizzone.sh | Shell script that creates providers as initial data registration for BizZone.<br>Executes core APIs sequentially and outputs the summarized API return values as execution results.<br>Recovery is possible by re-executing the shell if processing is interrupted midway.  | ./setup-bizzone.sh<br>No arguments |
| setup-finzone.sh | Shell script that creates providers, issuers, and validators as initial data registration for FinZone.<br>Executes core APIs sequentially and outputs the summarized API return values as execution results.<br>Recovery is possible by re-executing the shell if processing is interrupted midway. | ./setup-finzone.sh<br>No arguments |

List of scripts called from the above tools

| TOOLS   | DESCRIPTION                                                                                                                                                                                                        | EXAMPLE |
| ------- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
| _01_delete_table.sh        | Performs table deletion, function deletion, and sequence deletion using psql.<br>Port forwarding and disconnection are performed within the shell to connect to the DB.                                                                                                                                             | ./_01_delete_table.sh bpmfin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _02_delete_dynamodb.sh | Performs DynamoDB initialization.<br>Updates `sending_counter` value to 0.<br>Deletes all registered data in `balance_cache`.<br>Multiple windows are automatically launched for parallel deletion processing due to potentially large data volumes.<br>No processing is performed for tables other than the above. | ./_02_delete_dynamodb.sh fin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _03_delete_ssm.sh | Performs SSM deletion.<br>Multiple windows are automatically launched for parallel deletion processing due to potentially large data volumes.<br>For environments where replication is performed, deletion is also performed on replication destinations. | ./_03_delete_ssm.sh fin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _11_put_ssm.sh | Registers initial data for SSM.<br>Encryption is performed using `alias/app-resource-encryption-key` during registration. | ./_11_put_ssm.sh fin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _21_check_db_data.sh | Shell script that verifies if table counts are as expected. | ./_21_check_db_data.sh fin stage<br>First argument (required): Target Zone<br>Second argument (required): Environment name (prod, stage, dev, sandbox, local) |
| _22_check_ssm.sh | Shell script that verifies if SSM registration contents are as expected.<br>Also verifies replicated contents according to the target environment.<br>Registration contents are decrypted during verification. | Shell script that verifies if registration contents are as expected. |
| _port_forward.sh                    | Shell script that performs port forwarding.<br>Intended to be called from other shells, not intended to be executed alone.                                                                                                                                                    | ./_port_forward.sh true<br>First argument: Whether API port forwarding is required (for prod environment) |
| _disconnect_port_forward.sh                    | Shell script that disconnects port forwarding.<br>Intended to be called from other shells, not intended to be executed alone.                                                                                                                                                 | ./_disconnect_port_forward.sh<br>No arguments |
| utils.sh                    | Shell script that consolidates common processing.                                                                                                                                                                                                      | (None) |

The scripts under the above initial-setup are used in the Core release work procedures below:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2638938113/Core

### load-test-tools (Load Test Materials)

For execution methods, refer to Confluence:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1530593307

### poc

The only difference from register_id is that name can be entered

| TOOLS                    | DESCRIPTION                                    |
| ------------------------ | ---------------------------------------------- |
| createIssuerValidator.sh | Shell to create Issuer and Validator (for common area) |
| createValidator.sh       | Shell to create Validator (for extension area)         |

### register_id (Registration of Issuers and Validators)

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********

| TOOLS                         | DESCRIPTION                              |
| ----------------------------- | ---------------------------------------- |
| register_id.sh                | Shell to register issuers and validators |
| register_id_for_common.env    | Environment file for sandbox environment (common area) |
| register_id_for_extension.env | Environment file for sandbox environment (extension area) |

### signature-tools

Account signature explanation:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********#%E3%82%A2%E3%82%AB%E3%82%A6%E3%83%B3%E3%83%88%E7%BD%B2%E5%90%8D%E3%81%AB%E3%81%A4%E3%81%84%E3%81%A6

Tool usage:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Sandbox

| TOOLS                    | External Provision | DESCRIPTION                                                        |
|--------------------------| ------------ |--------------------------------------------------------------------|
| account_signature.js     | Yes          | JavaScript called from various scripts to create account signatures                                        |
| approve_signature.py     | Yes          | Script to create account signatures for approve                                         |
| arguments.json           | Yes          | JSON to display arguments that should be input for use with create_signature.py                         |
| create_signature.py      | Yes          | Various Python files                                                       |
| exchange_signature.py    | Yes          | Script to create account signatures for exchange                                        |
| recover_signature.js     | No           | JavaScript called from recover_signature.sh to verify account signatures                        |
| recover_signature.py     | No           | Script to get publicKey from message hash and signature<br>Used for account signature verification |
| synchronous_signature.py | Yes          | Script to create account signatures for synchronous                                     |
| tools.js                 | Yes          | JavaScript for common processing                                                           |
| transfer_signature.py    | Yes          | Script to create account signatures for transfer                                        |

### signature-tools-zip

| TOOLS         | DESCRIPTION                           |
| ------------- | ------------------------------------- |
| create-zip.sh | Shell to package signature-tools into zip |

When modifying externally provided sources and releasing to Sandbox environment, execute create-zip.sh to create a zip file.
Commit the created zip file to dcbg-dcf-core-api.
For release and verification methods, refer to the README of dcbg-dcf-core-api.
https://github.com/decurret-lab/dcbg-dcf-core-api

### test-tools

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/API
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/API

| TOOLS                      | DESCRIPTION                                                                                            |
| -------------------------- | ------------------------------------------------------------------------------------------------------ |
| template.env.sh            | Template that consolidates environment variables for normal system tests. When using, create `env.sh` from this file |
| port_forward.sh            | Shell to perform port forwarding to bastion server                                                       |
| disconnect_port_forward.sh | Shell to disconnect the above port forwarding                                                                 |
| 00_init-fin.sh             | [For common area] Shell to execute provider creation to token creation during environment initial release                          |
| 00_init-ind.sh             | [For extension area] Shell to execute provider creation to token creation during environment initial release                          |
| _NN_\__XXXX_-fin.sh        | [For common area] Shell to execute each normal system test                                                            |
| _NN_\__XXXX_-ind.sh        | [For extension area] Shell to execute each normal system test                                                            |
| semi-normal.sh             | Shell to execute semi-normal system tests                                                                         |

When executing test scripts, copy `template.env.sh` to create `env.sh` and configure settings according to each environment.

## scripts/besu

### create static-node.json

Script group commonly used in various BESU-related procedure documents

Operation verification procedure: https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/BESU

| TOOLS                    | DESCRIPTION                   |
| ------------------------ | ----------------------------- |
| start_besu.sh            | Start BESU               |
| check_besu_behavior.sh   | Check BESU standalone operation     |
| check_besu_block_sync.sh | Check BESU block synchronization |

## scripts/besu/recreate

Script group for BESU node recreation work

Procedure document: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2416541724/BESU+user+data+ami

| TOOLS                             | DESCRIPTION                                |
| --------------------------------- | ------------------------------------------ |
| disable_termination_protection.sh | Remove termination protection                           |
| terminate_instance.sh             | Terminate instance                     |
| stop_datadog_agent.sh             | Stop Datadog Agent                   |
| start_datadog_agent.sh            | Start Datadog Agent                   |
| change_autoscaling_size.sh        | Change Auto Scaling Group size      |
| replace_data_volume.sh            | Replace instance data volume |
| delete_unused_volume.sh           | Delete unused volumes                 |

## scripts/besu/backup

Script group for backup node recreation work

Procedure document: https://decurret.atlassian.net/wiki/spaces/DIG/pages/2530181219

| TOOLS                                   | DESCRIPTION                                      |
| --------------------------------------- | ------------------------------------------------ |
| terminate_besu_backup_instance.sh       | Terminate backup node                     |
| create_ebs_data_volume_from_snapshot.sh | Create data volume from snapshot          |
| replace_ebs_data_volume.sh              | Replace backup node data volume |

## scripts/allcomponents

### tool-listed
#### Core-related environment automatic setup procedure
https://decurret.atlassian.net/wiki/spaces/DIG/pages/1984069913/ALL#%E8%87%AA%E5%8B%95%E8%A8%AD%E5%AE%9A%E6%89%8B%E9%A0%86.1
#### Web3Stream-related environment automatic setup procedure
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2259779711/Web3Stream+RDSStream#%E3%83%AD%E3%83%BC%E3%82%AB%E3%83%AB%E7%92%B0%E5%A2%83%E8%87%AA%E5%8B%95%E6%A7%8B%E7%AF%89
#### Relayer-related environment automatic setup procedure
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2518581288/Relayer#1.%E3%83%AD%E3%83%BC%E3%82%AB%E3%83%AB%E7%92%B0%E5%A2%83%E8%87%AA%E5%8B%95%E6%A7%8B%E7%AF%89
#### BPMServer-related environment automatic setup procedure
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2525462608/BPMServer+IT#%E6%89%8B%E9%A0%86%E6%A6%82%E8%A6%81


| TOOLS                            | DESCRIPTION                                          |
|----------------------------------|------------------------------------------------------|
| start_core-components.sh         | Batch startup of core→besu→contract→BCClient→BCMonitoring locally in sequence |
| start_web3stream-components.sh   | Batch startup of besu→contract→Web3Stream locally in sequence                 |
| start_relayer-components.sh | Batch startup of besu→contract→Relayer locally in sequence                    |
| start_bpm-components.sh | Batch startup of bpm-related components locally in sequence                             |
| stop.sh                          | Temporarily stop all components                                        |
| restart_core-components.sh | Restart all Core-related environment components temporarily stopped by stop.sh                  |
| restart_web3stream-components.sh | Restart all Web3Stream-related environment components temporarily stopped by stop.sh            |
| restart_relayer-components.sh | Restart all Relayer-related environment components temporarily stopped by stop.sh               |
| restart_bpm-components.sh     | Restart all bpm-related environment components temporarily stopped by stop.sh                   |
| destroy.sh                       | Delete all components (docker-compose down/besu node deletion)           |

### subscripts (Sub-scripts executed from start.sh)
| TOOLS                            | DESCRIPTION                      |
|----------------------------------|----------------------------------|
| _load_env.sh                     | Environment variable setting                           |
| start_core.sh                    | Core startup and DB migration/update           |
| start_besu_network.sh            | Besu node generation and startup                    |
| start_besu_node_for_relayer.sh   | Besu node generation and startup for Relayer (common/extension nodes) |
| start_bcclient.sh                | ABI file copy and BCClient startup          |
| start_bcmonitoring.sh            | ABI file copy and BCMonitoring startup      |
| start_migrate_contract.sh        | Contract migration                   |
| start_migrate_contract_shared.sh | Balance DLT contract migration             |
| start_migrate_contract_ibc.sh    | IBC contract migration               |
| start_web3stream.sh              | ABI file copy and Web3Stream startup        |
| start_relayer.sh                 | ABI file copy and Relayer startup           |
| start_bank_api_mock.sh           | BANK_API (Mock) startup                |
| start_bff.sh                     | BFF startup                           |
| start_bpm.sh                     | BPM startup                           |
| start_core-with-bcclientmock                | Core startup and DB migration (for BPM)         |


## scripts/datadog

### tool-listed

https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Datadog
| TOOLS | DESCRIPTION |
| ------------------------- | ---------------------------------------------------------------------- |
| create-account.sh | Shell to issue new DataDog accounts. Also calls user-invite.sh in conjunction |
| user-invite.sh | Shell to send invitation emails to users who already have accounts issued |
| delete-account.sh | Shell to execute DataDog account deletion |
| add-role.sh | Shell to add users with issued accounts to DataDog roles |
| datadog.env | File to manage environment variables required for DataDog API calls |

## scripts/gas

Google Apps Script program file group used by the entire team
For a list of sheets using each program, refer to the following link:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Google+Apps+Scripts

File additions and edits should be done using Chrome's official GitHub Assistant, avoiding direct branch commits.
For procedures, refer to the following link:
https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/Google+Apps+Script+Git

### tool-listed

| TOOLS                        | Used Files                       |
| ---------------------------- | ---------------------------------- |
| approval_request_list.gs     | Approval request list                       |
| create_evidence_sheet.gs     | [Template] Release work procedure document |
| datadog_alert_list.gs        | DataDog alert automatic collection sheet   |
| datadog_webhook_interface.gs | DataDog alert automatic collection sheet   |
| personal_work_condition.gs   | Personal work condition summary                 |
| release_calender.gs          | [Each environment] Release calendar       |
| work_condition_importer.gs   | Work management import sheet         |
| work_condition.gs            | Work condition                           |

## scripts/gas/test-tools

Various tool groups required for operation verification when modifying Google Apps Script program file groups used by the entire team

For usage methods, refer to [Confluence](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2596306994/Google+Apps+Script).

### tool-listed

| TOOLS                   | Used Files                                                                  |
| ----------------------- | ----------------------------------------------------------------------------- |
| datadog_webhook_test.sh | Shell script used for testing DataDog alert automatic collection sheets            |
| datadog_webhook.env     | File to manage environment variables required for DataDog alert automatic collection sheet API calls |

## scripts/cloud/sops

### tool-listed

| TOOLS                          | Used Files                                            |
| ------------------------------ | ------------------------------------------------------- |
| \_common_sops_encryption.sh    | Definition of constants and functions commonly used by other scripts            |
| create_sops_encryption_key.sh  | Shell script to create KMS keys used by SOPS       |
| get_sops_encryption_key_arn.sh | Shell script to get ARN of KMS keys used by SOPS |

## scripts/cloud/kubernetes/sampleapp

Kubernetes cloud verification sample application resources and script group

For usage methods, refer to [Confluence](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2551349271/EKS+Kubernetes).

## scripts/cloud/env-start-stop

### tool-listed

| TOOLS            | Used Files                                           |
| ---------------- | ------------------------------------------------------ |
| start_env.sh     | Shell script to start RDS, BESU, EKS of each environment in sequence |
| stop_env.sh      | Shell script to stop EKS, BESU, RDS of each environment in sequence |
| start_bastion.sh | Shell script to start bastion                       |
| stop_bastion.sh  | Shell script to stop bastion                       |

Scripts to start and stop each environment

For usage methods, refer to Confluence's [Procedure for manually implementing automatic start/stop processing of each AWS environment](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2511700163/AWS).

## scripts/cloud/log-test

### tool-listed

| TOOLS                   | Used Files                                         |
| ----------------------- | ---------------------------------------------------- |
| put-lambda-log-event.sh | Log sending shell script commonly used by other scripts |
| put-eks-log-event.sh    | Shell script to send logs to EKS                   |
| put-eks-log-event.sh    | Shell script to send logs to Lambda                |

For usage methods, refer to Confluence's [Procedure for sending test logs to CloudWatch Logs to verify log subscription to DataDog](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2566521105/DataDog+CloudWatch+Logs)

## scripts/cloud/cloudformation

### tool-listed

| TOOLS              | Used Files                                                     |
| ------------------ | ---------------------------------------------------------------- |
| check_stacksets.sh | Script to check CloudFormation StackSets configuration status |

For usage methods, refer to Confluence's [CloudFormation StackSets Application Procedure](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2063990785/CloudFormation+StackSets)

## scripts/common

### tool-listed

| TOOLS                  | Used Files                                             |
| ---------------------- | -------------------------------------------------------- |
| add_git_tag_release.sh | Shell script to create git tags and release notes |

For usage methods, refer to Confluence's [How to use shell for creating tags and release notes in GitHub](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2600796241/GitHub)


## modules/

Manages tool modules that are difficult to implement with shell scripts.

### tool-listed

| TOOLS                   | DESCRIPTION                                |
| ----------------------- | ------------------------------------------ |
| github-secret-register  | Tool to register secrets in GitHub Environment |
