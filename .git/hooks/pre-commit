#!/bin/bash

# Get list of staged files
files=$(git diff --cached --name-only)

# Check if any of the staged files are in test/local directory
for file in $files; do
    if [[ $file == *"test/local"* ]]; then
        echo "⚠️  WARNING: You are modifying files in the test/local directory."
        echo "    File: $file"
        echo "    Please make sure these changes are intentional."
        echo "    Press Ctrl+C to abort the commit or any other key to continue..."
        read -n 1
    fi
done

exit 0
