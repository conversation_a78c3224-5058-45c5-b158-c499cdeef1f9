
from dataclasses import dataclass
import base64
import json

import requests
from requests import Response
from nacl import encoding
from nacl.public import <PERSON>Key, SealedBox

from .github_config import GithubConfig


@dataclass(frozen=True)
class SecretRegister:
    config: GithubConfig
    repository_id: str
    env_name: str
    pk_info: "PublicKeyInfo"

    @classmethod
    def create(cls, config: GithubConfig,
               repository_name: str, env_name: str) -> "SecretRegister":

        # repository_id の取得
        repository_id: str = cls._find_repository_id(
            config=config, repository_name=repository_name
        )
        # public_key の取得
        pk_info: PublicKeyInfo = cls._create_public_key_info(
            config=config, repository_id=repository_id, env_name=env_name
        )

        return SecretRegister(
            config=config,
            repository_id=repository_id,
            env_name=env_name,
            pk_info=pk_info
        )

    @classmethod
    def _find_repository_id(
            cls, config: GithubConfig, repository_name: str) -> str:

        repository_url: str = (
            "https://api.github.com/repos/"
            f"{config.repository_owner}/{repository_name}"
        )
        response: Response = requests.get(
            url=repository_url,
            headers=config.request_headers()
        )

        repository_info: dict = response.json()
        return str(repository_info["id"])

    @classmethod
    def _create_public_key_info(
            cls, config: GithubConfig,
            repository_id: str, env_name: str) -> "PublicKeyInfo":

        response: Response = requests.get(
            url=f"{cls._env_url(repository_id, env_name)}/public-key",
            headers=config.request_headers()
        )
        response.raise_for_status()

        pk_json: dict = response.json()
        return PublicKeyInfo(
            key_id=pk_json["key_id"], key=pk_json["key"]
        )

    @classmethod
    def _env_url(cls, repository_id: str, env_name: str) -> str:
        return (
            "https://api.github.com/repositories"
            f"/{repository_id}/environments/{env_name}/secrets"
        )

    def register(self, secret_name: str, secret_value: str) -> None:
        encrypted_value: str = self.pk_info.encrypt(secret_value)
        response: Response = requests.put(
            url=(
                f"{self._env_url(self.repository_id, self.env_name)}"
                f"/{secret_name}"
            ),
            headers=self.config.request_headers(),
            data=json.dumps(
                {
                    "encrypted_value": encrypted_value,
                    "key_id": self.pk_info.key_id
                }
            )
        )
        # status_code が 4xx, 5xx の場合に例外を発行
        response.raise_for_status()


# @dataclass(frozen=True, init=False)
class PublicKeyInfo:
    # key_id: str
    # _seal_box: SealedBox

    def __init__(self, key_id: str, key: str) -> None:
        self.key_id = key_id
        public_key: PublicKey = PublicKey(
            key.encode("utf-8"), encoding.Base64Encoder()
        )
        self._seal_box = SealedBox(public_key)

    def encrypt(self, value: str) -> str:
        encrypted: bytes = self._seal_box.encrypt(value.encode("utf-8"))
        return base64.b64encode(encrypted).decode("utf-8")
