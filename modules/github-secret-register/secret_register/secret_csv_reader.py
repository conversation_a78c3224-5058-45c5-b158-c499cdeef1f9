from typing import Optional
from dataclasses import dataclass
from pathlib import Path
import csv
from collections import namedtuple, defaultdict

from .environment_secret_info import EnvironmentSecretInfo

EnvironmentKey = namedtuple("EnvironmentKey", ["repository_name", "env_name"])


@dataclass(frozen=True)
class SecretRecord:
    repository_name: str
    env_name: str
    secret_name: str
    secret_value: str

    @property
    def environment_key(self) -> EnvironmentKey:
        return EnvironmentKey(
            repository_name=self.repository_name, env_name=self.env_name
        )


class SecretCsvReader:
    """
    以下の形式で定義した CSV ファイルを読み込む。

    `repository_name,env_name,secret_name,secret_value`
    """

    def read(self, file_path: Path) -> list[EnvironmentSecretInfo]:
        secret_records: list[SecretRecord] = [
            opt_record for opt_record in self._read_from_file(file_path)
            if opt_record is not None
        ]

        return self._load_environment_secrets(secret_records)

    def _read_from_file(self, file_path: Path) -> list[Optional[SecretRecord]]:
        with open(file_path) as file:
            csv_reader = csv.reader(file)
            return [self._read_record(row) for row in csv_reader]

    def _read_record(self, row: list[str]) -> Optional[SecretRecord]:
        if len(row) != 4:
            return None

        return SecretRecord(
            repository_name=row[0], env_name=row[1],
            secret_name=row[2], secret_value=row[3]
        )

    def _load_environment_secrets(
            self, secret_records: list[SecretRecord]
    ) -> list[EnvironmentSecretInfo]:

        if not secret_records:
            return []

        env_secret_set: dict[EnvironmentKey, dict[str, str]] = defaultdict(
            lambda: {})

        for record in secret_records:
            env_key: EnvironmentKey = record.environment_key
            secrets: dict[str, str] = env_secret_set[env_key]
            secrets[record.secret_name] = record.secret_value

        return [
            EnvironmentSecretInfo(
                repository_name=env_key.repository_name,
                env_name=env_key.env_name,
                secrets=secrets
            )
            for env_key, secrets in env_secret_set.items()
        ]
