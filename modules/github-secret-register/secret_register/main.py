import os
from argparse import Argument<PERSON>ars<PERSON>, Namespace
from pathlib import Path
import textwrap

from .github_config import GithubConfig
from .environment_secret_info import EnvironmentSecretInfo
from .secret_csv_reader import SecretCsvReader
from .secret_register import SecretRegister


def main():
    parser: ArgumentParser = _init_argument_parser()
    args: Namespace = parser.parse_args()

    config: GithubConfig = GithubConfig(os.environ)
    reader: SecretCsvReader = SecretCsvReader()
    secret_info_list: list[EnvironmentSecretInfo] = reader.read(
        file_path=args.csv_file
    )

    for secret_info in secret_info_list:
        secret_register: SecretRegister = SecretRegister.create(
            config=config,
            repository_name=secret_info.repository_name,
            env_name=secret_info.env_name
        )

        for secret_name, secret_value in secret_info.secrets.items():
            print(
                f"Secret を登録します。リポジトリ : {secret_info.repository_name} , "
                f"Environment : {secret_info.env_name} , "
                f"secret_name : {secret_name} , "
                f"secret_value : {secret_value}"
            )
            secret_register.register(
                secret_name=secret_name, secret_value=secret_value
            )


def _init_argument_parser() -> ArgumentParser:
    parser: ArgumentParser = ArgumentParser(
        textwrap.dedent(
            """
            CSV ファイルに記載した secret を読み込み、GitHub Environments に登録します。
            CSV ファイルはローカルに用意し、各行を以下の形式で記載してください。
                `repository_name,env_name,secret_name,secret_value`

            また、実行前に `.env` ファイルを用意してください。(詳細は `readme.md` 参照)
            """
        )
    )
    parser.add_argument(
        "--csv_file", type=Path, required=True, help="CSV ファイルのパスを指定"
    )

    return parser


if __name__ == "__main__":
    main()
