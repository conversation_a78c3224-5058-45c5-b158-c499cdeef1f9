# from dataclasses import dataclass


# @dataclass(frozen=True, init=False)
class GithubConfig:
    # access_token: str
    # repository_owner: str

    def __init__(self, config: dict[str, str]) -> None:
        self.access_token = config.get("ACCESS_TOKEN")
        self.repository_owner = config.get("OWNER")

    def request_headers(self) -> dict[str, str]:
        return {
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {self.access_token}",
            "X-GitHub-Api-Version": "2022-11-28"
        }
