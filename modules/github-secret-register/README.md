# github-secret-register
## はじめに
本ツールは、GitHub REST API を用いて、 GitHub Environment に secret を登録するものである。
高々数個の場合は、ブラウザから手動で設定するので十分だが、Environment が増え secret の個数が増えると手間がかかるので、本ツールを活用する

なお、GitHub REST API は curl コマンドでも十分利用できるが、 secret を登録する場合、GitHub が発行した公開鍵で暗号化する必要があるため、 python で実装した。

## 動作環境
- python3.9 (pyenv で用意するのがよい)
- pipenv (仮想環境)

## 環境構築手順
1. `modules/github-secret-register` ディレクトリに移動
1. pipenv にて仮想環境を構築する
    ```shell
    pipenv sync
    ```

## secret 設定方法
ローカルに以下の形式で CSV ファイルを記載する。
```
repository_name,env_name,secret_name,secret_value
```

## 動作手順
1. 予め、対象となるリポジトリに Environment を作成すること。
1. [GitHub の Personal access tokens (classic)](https://github.com/settings/tokens) にて、 access_token を発行する。
  この際、 `repo` スコープを付与すること。参照 : https://docs.github.com/ja/rest/actions/secrets?apiVersion=2022-11-28
1. `sample.env` をコピーして、 `.env` を作成する。
1. `.env` ファイルを開き、変数 `ACCESS_TOKEN` に上記で取得した access_token を記載する。
1. 下記コマンドにより、 secret を登録できる。
    ```shell
    pipenv run python -m secret_register.main --csv_file `CSV ファイルのパス`
    ```


### メモ : Environment の登録
curl で実施する。
```shell
TOKEN=...  # GitHubの Settings より発行したトークンを指定
OWNER=decurret-lab
REPO=dcbg-dcf-bpm-server

for ENVIRONMENT_NAME in dev-cl-fin dev-fin dev-sub-fin sandbox-fin; do
  curl \
    -X PUT \
    -H "Accept: application/vnd.github+json" \
    -H "Authorization: Bearer ${TOKEN}"\
    -H "X-GitHub-Api-Version: 2022-11-28" \
    https://api.github.com/repos/${OWNER}/${REPO}/environments/${ENVIRONMENT_NAME}
done
```
